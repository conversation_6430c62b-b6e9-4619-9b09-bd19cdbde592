{"name": "digital-battle-map", "version": "1.0.0", "description": "数字化营销作战", "author": "peng", "scripts": {"dev": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "echarts": "^5.4.1", "element-ui": "^2.15.7", "ezuikit-js": "^7.6.6", "js-cookie": "^2.2.0", "normalize.css": "^7.0.0", "nprogress": "^0.2.0", "path-to-regexp": "^2.4.0", "video.js": "^8.3.0", "vue": "^2.6.10", "vue-router": "^3.0.6", "vuex": "^3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.4.4", "@vue/cli-plugin-eslint": "^4.4.4", "@vue/cli-plugin-unit-jest": "^4.4.4", "@vue/cli-service": "^4.4.4", "@vue/test-utils": "^1.3.3", "autoprefixer": "^9.5.1", "babel-eslint": "^10.1.0", "babel-jest": "^23.6.0", "babel-plugin-dynamic-import-node": "^2.3.3", "chalk": "^2.4.2", "connect": "^3.6.6", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "^3.2.0", "mockjs": "^1.1.0", "runjs": "^4.3.2", "sass": "^1.26.8", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.3", "serve-static": "^1.13.2", "svg-sprite-loader": "^4.1.3", "svgo": "^1.2.2", "vue-template-compiler": "^2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}