项目基于vue-cli构建，需要全局安装vue-cli，安装方式如下：
```bash
# 全局安装vue-cli
npm install -g @vue/cli

# 如果安装太慢，可以先注册npm的淘宝镜像，然后再安装
# 设置淘宝镜像地址
npm config set registry https://registry.npm.taobao.org/
# 查看镜像地址
npm config get registry  
```


## Build Setup

```bash
# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装以来，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 [http://localhost:9528](http://localhost:9528)

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

## 其它

```bash
# 预览发布环境效果
npm run preview

# 预览发布环境效果 + 静态资源分析
npm run preview -- --report

# 代码格式检查
npm run lint

# 代码格式检查并自动修复
npm run lint -- --fix
```

