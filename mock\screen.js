const Mock = require('mockjs')

// const data = Mock.mock({
//   'items|30': [{
//     id: '@id',
//     title: '@sentence(10, 20)',
//     'status|1': ['published', 'draft', 'deleted'],
//     author: 'name',
//     display_time: '@datetime',
//     pageviews: '@integer(300, 5000)'
//   }]
// })
const overviewData = {
  "athlete": {
    "all": 89,
    "female": 42,
    "male": 47
  },
  "training": [{
      "all_count": 10,
      "department_id": 8,
      "training_count": 9,
      "dept_name": "男子步枪"
    },
    {
      "all_count": 5,
      "department_id": 10,
      "training_count": 5,
      "dept_name": "男子手枪"
    }
  ],
  "statistics": {
    "training_type_count": 2,
    "athlete": 89,
    "coach": 32,
    "device": 0
  }
}

const trainingNameInfoData = {
  "trainingName": [{
      "sportName": "平板支撑",
      "id": 1
    },
    {
      "sportName": "单脚支撑",
      "id": 2
    },
    {
      "sportName": "3000米跑",
      "id": 3
    },
    {
      "sportName": "仰卧起坐",
      "id": 4
    },
    {
      "sportName": "坐位体前屈",
      "id": 5
    }
  ]
}

const athleteTrainingInfoData = {
  "top10Athlete": [{
      "score": 10,
      "total": 10,
      "member_photo": "",
      "personId": 9589,
      "userName": "李国荣"
    },
    {
      "score": 10,
      "total": 10,
      "member_photo": "",
      "personId": 9584,
      "userName": "缪佳睿"
    }
  ],
  "scoreCount": [{
      "name": "5分",
      "value": 46
    },
    {
      "name": "6分",
      "value": 0
    },
    {
      "name": "7分",
      "value": 0
    },
    {
      "name": "8分",
      "value": 1
    },
    {
      "name": "9分",
      "value": 0
    },
    {
      "name": "10分",
      "value": 3
    }
  ],
  "fullScore": {
    "percentage": "6",
    "score10Count": 3
  }
}

const scrollAthleteInfoData = {
  "total": 6,
  "row": [{
      "member_id": 1,
      "member_photo": "",
      "member_name": "丁姝月1"
    },
    {
      "member_id": 2,
      "member_photo": "",
      "member_name": "丁姝月2"
    },
    {
      "member_id": 3,
      "member_photo": "",
      "member_name": "丁姝月3"
    }
  ]
}

const scrollCoachInfoData = {
  "total": 6,
  "row": [{
      "member_id": 1,
      "member_photo": "",
      "member_name": "梁荣1"
    },
    {
      "member_id": 2,
      "member_photo": "",
      "member_name": "林玲1"
    },
    {
      "member_id": 3,
      "member_photo": "",
      "member_name": "梁荣2"
    }
  ]
}

const programTypeInfoData = {
  "trainingName": [{
      "type_name": "射击",
      "type_id": 1,
      "program": [{
          "program_id": 1672068152133760,
          "program_name": "10米气步枪"
        },
        {
          "program_id": 1674216779169197,
          "program_name": "3000米跑"
        }
      ]
    },
    // {
    //   "type_name": "射箭",
    //   "type_id": 2,
    //   "program": [{
    //     "program_id": 1674185801178025,
    //     "program_name": "常规训练"
    //   }]
    // }
  ]
}

const shooterScoreSubsectionData = {
  "scoreCount": [{
      "name": "9环以下",
      "value": 3
    },
    {
      "name": "9-9.9环",
      "value": 13
    },
    {
      "name": "10-10.9环",
      "value": 2
    }
  ],
  "fullScore": {
    "percentage": "15",
    "score10Count": 2
  },
  "listTop5Shooter": [{
      "score": "9.5",
      "member_photo": "",
      "athleteName": "王红艳"
    },
    {
      "score": "9.2",
      "member_photo": "",
      "athleteName": "李红阳"
    }
  ]
}

const shooterCountInfoData = {
  "shooterScoreCount": [{
      "name": "9环以下",
      "value": 3
    },
    {
      "name": "9-9.9环",
      "value": 7
    },
    {
      "name": "10-10.9环",
      "value": 2
    }
  ],
  "shooterScoreList": [{
      "score": "9.2",
      "shootingTime": "2023-03-26 14:44:32"
    },
    {
      "score": "9.0",
      "shootingTime": "2023-03-26 14:44:32"
    }
  ],
  "athlete": {
    "member_photo": "",
    "member_name": "王红艳",
    "department": "女子拳击"
  },
  "shooterScoreGroup": [{
      "score": 80.8,
      "groupId": 1
    },
    {
      "score": 29.5,
      "groupId": 2
    }
  ],
  "maxScoreNumTimes": {
    "numTimes": 12,
    "maxScore": "9.5"
  },
  "bracelet": {
    "heartbeat": 178,
    "bloodoxygen": 98,
    "bloodpressl": 150,
    "bloodpressh": 100,
    "sos": false
  }
}

const trainingInfoData = {
  "maxScoreByDate": [{
      "score": 20,
      "match_date": "2023-03-25"
    },
    {
      "score": 30,
      "match_date": "2023-03-26"
    },
    {
      "score": 53,
      "match_date": "2023-03-27"
    },
    {
      "score": 20,
      "match_date": "2023-03-28"
    },
    {
      "score": 33,
      "match_date": "2023-03-29"
    },
    {
      "score": 53,
      "match_date": "2023-03-30"
    },
    {
      "score": 23,
      "match_date": "2023-03-31"
    },
  ],
  "trainingResult": [{
      "result": 8000,
      "score": 0,
      "unit": "秒",
      "startTime": 1679739489
    },
    {
      "result": 12000,
      "score": 0,
      "unit": "秒",
      "startTime": 1679739489
    }
  ]
}

const jdData = [
  {
    "EMG_IMU_EmgNames": "abc",
    "EMG_IMU_Emg": "sdfsdkjhfjewhrhsdfhsfjdsfhvhdsfhjewruywuisdhjfhdskjfwher",
    "Message_CreateDateTime": "2023-04-14T10:13:23.1299996+08:00"
  },
  {
    "EMG_IMU_EmgNames": "abc1",
    "EMG_IMU_Emg": "sdfsdkjhfjewhrhsdfhsfjdsfhvhdsfhjewruywuisdhjfhdskjfwher1",
    "Message_CreateDateTime": "2023-04-14T10:16:23.1299996+08:00"
  }
]

const shakeData = [
  {
    "cur_time": "11111111111",
    "horizontal": "42.23423",
    "longitudinal": "-42.23423"
  },
  {
    "cur_time": "11111111111",
    "horizontal": "42.23423",
    "longitudinal": "-42.23423"
  }
]

// const monitorData = [
//   {
//     "monitorUrl": "ezopen://open.ys7.com/K42232058/1.hd.live",
//     "accessToken": "at.3x1nu2kr6iogy1ua60bbjvmxc96l6wyn-2oayxbam4i-08xk6kn-wtdxkwt5n"
//   },
//   {
//     "monitorUrl": "ezopen://open.ys7.com/K42232058/2.hd.live",
//     "accessToken": "at.3x1nu2kr6iogy1ua60bbjvmxc96l6wyn-2oayxbam4i-08xk6kn-wtdxkwt5n"
//   }
// ]

module.exports = [{
    url: '/overview/getMemberCountInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: overviewData
      }
    }
  },
  {
    url: '/overview/getTrainingNameInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: trainingNameInfoData
      }
    }
  },
  {
    url: '/overview/getAthleteTrainingInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: athleteTrainingInfoData
      }
    }
  },
  {
    url: '/overview/getScrollAthleteInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: scrollAthleteInfoData
      }
    }
  },
  {
    url: '/overview/getScrollCoachInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: scrollCoachInfoData
      }
    }
  },
  {
    url: '/overview/getProgramTypeInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: programTypeInfoData
      }
    }
  },
  {
    url: '/overview/getShooterScoreSubsection',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: shooterScoreSubsectionData
      }
    }
  },
  {
    url: '/overview/getNotTrainingAthleteTotal',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: {
          total: 6
        }
      }
    }
  },
  {
    url: '/overview/getCoachTotal',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: {
          total: 6
        }
      }
    }
  },
  {
    url: '/analyse/getShooterCountInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: shooterCountInfoData
      }
    }
  },
  {
    url: '/analyse/getTrainingInfo',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: trainingInfoData
      }
    }
  },
  {
    url: '/analyse/getJDData',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: jdData
      }
    }
  },
  {
    url: '/analyse/getShootShake',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: shakeData
      }
    }
  },
  {
    url: '/overview/getMonitorList',
    type: 'post',
    response: config => {
      return {
        code: 20000,
        data: {
          start: 0,
          end: 1,
          list: [
            {
              "monitorUrl": "ezopen://open.ys7.com/K42232058/"+Math.floor(Math.random()*(31))+".hd.live",
              "accessToken": "at.3x1nu2kr6iogy1ua60bbjvmxc96l6wyn-2oayxbam4i-08xk6kn-wtdxkwt5n"
            },
            {
              "monitorUrl": "ezopen://open.ys7.com/K42232058/"+Math.floor(Math.random()*(31))+".hd.live",
              "accessToken": "at.3x1nu2kr6iogy1ua60bbjvmxc96l6wyn-2oayxbam4i-08xk6kn-wtdxkwt5n"
            }
          ]
        }
      }
    }
  }
]