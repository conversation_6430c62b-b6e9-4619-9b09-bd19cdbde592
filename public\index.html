<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico"> -->
  <title>数字化营销作战</title>

  <!-- 本地测试环境地址 -->
  <!-- <script type='text/javascript' src='http://127.0.0.1:9090/gisapi/getscript'></script>
  <script type='text/javascript' src='http://127.0.0.1:9090/gisapi/AiMapGeoTools.js'></script> -->

  <!-- 生产环境地址 -->
  <!-- <script type='text/javascript' src='http://*************:18080/gisapi/getscript'></script>
  <script type='text/javascript' src='http://*************:18080/gisapi/AiMapGeoTools.js'></script> -->
  <script type="text/javascript">
    // 是否为生产环境
    let isProd = true;

    //内网百度地图代理地址
    let baiduProxy = window.location.hostname + ":" + window.location.port;
    let thisHost = window.location.hostname + ":" + window.location.port;

    if (window.location.hostname != '*************' && window.location.hostname != '*************'
      && window.location.hostname != '*************') {
      isProd = false;
    }
    let map_online0 = "online0.map.bdimg.com",
      map_online1 = "online1.map.bdimg.com",
      map_online2 = "online2.map.bdimg.com",
      map_online3 = "online3.map.bdimg.com",
      map_online4 = "online4.map.bdimg.com",
      map_api = "api.map.baidu.com",
      map_shangetu0 = "shangetu0.map.bdimg.com/it/",
      map_shangetu1 = "shangetu1.map.bdimg.com/it/",
      map_shangetu2 = "shangetu2.map.bdimg.com/it/",
      map_shangetu3 = "shangetu3.map.bdimg.com/it/",
      map_shangetu4 = "shangetu4.map.bdimg.com/it/",
      map_d0 = "d0.map.baidu.com",
      map_d1 = "d1.map.baidu.com",
      map_d2 = "d2.map.baidu.com",
      map_d3 = "d3.map.baidu.com",
      map_api0 = "api0.map.bdimg.com",
      map_api1 = "api1.map.bdimg.com",
      map_api2 = "api2.map.bdimg.com",
      map_webmap0 = "webmap0.map.bdimg.com",
      map_maponline0 = "maponline0.bdimg.com",
      map_maponline1 = "maponline1.bdimg.com",
      map_maponline2 = "maponline2.bdimg.com",
      map_maponline3 = "maponline3.bdimg.com",
      map_g0 = "g0.api.map.baidu.com",
      map_g1 = "g1.api.map.baidu.com",
      map_g2 = "g2.api.map.baidu.com",
      map_g3 = "g3.api.map.baidu.com",
      map_g4 = "g4.api.map.baidu.com",
      map_gss0_bdstatic = "gss0.bdstatic.com",
      map_gss1_bdstatic = "gss1.bdstatic.com",
      map_gss2_bdstatic = "gss2.bdstatic.com",
      map_gss3_bdstatic = "gss3.bdstatic.com",
      map_ss0_bdstatic = "ss0.bdstatic.com",
      map_ss1_bdstatic = "ss1.bdstatic.com",
      map_ss2_bdstatic = "ss2.bdstatic.com",
      map_ss3_bdstatic = "ss3.bdstatic.com",
      map_tieba = "static.tieba.baidu.com",
      map_dlswbr = "dlswbr.baidu.com";

    if (isProd) {
      // baiduProxy = window.location.hostname + ':18080'
      baiduProxy = "*************:18080";
      map_online0 = baiduProxy + "/online0";
      map_online1 = baiduProxy + "/online1";
      map_online2 = baiduProxy + "/online2";
      map_online3 = baiduProxy + "/online3";
      map_online4 = baiduProxy + "/online4";
      map_api = baiduProxy + "/api";
      map_shangetu0 = baiduProxy + "/shangetu0/it/";
      map_shangetu1 = baiduProxy + "/shangetu1/it/";
      map_shangetu2 = baiduProxy + "/shangetu2/it/";
      map_shangetu3 = baiduProxy + "/shangetu3/it/";
      map_shangetu4 = baiduProxy + "/shangetu4/it/";
      map_d0 = baiduProxy + "/d0";
      map_d1 = baiduProxy + "/d1";
      map_d2 = baiduProxy + "/d2";
      map_d3 = baiduProxy + "/d3";
      map_api0 = baiduProxy + "/api";
      map_api1 = baiduProxy + "/api";
      map_api2 = baiduProxy + "/api";
      map_webmap0 = baiduProxy + "/webmap0";
      map_huiyan = baiduProxy + "/huiyan";
      map_lbsyun = baiduProxy + "/lbsyun";
      map_static = baiduProxy + "/map_static";
      map_gsp0 = baiduProxy + "/map_gsp0";
      map_gss0_bdstatic = baiduProxy + "/map_gss0_bdstatic";
      map_gss1_bdstatic = baiduProxy + "/map_gss1_bdstatic";
      map_gss2_bdstatic = baiduProxy + "/map_gss2_bdstatic";
      map_gss3_bdstatic = baiduProxy + "/map_gss3_bdstatic";
      map_ss0_bdstatic = baiduProxy + "/map_ss0_bdstatic";
      map_ss1_bdstatic = baiduProxy + "/map_ss1_bdstatic";
      map_ss2_bdstatic = baiduProxy + "/map_ss2_bdstatic";
      map_ss3_bdstatic = baiduProxy + "/map_ss3_bdstatic";

      map_maponline0 = baiduProxy + "/online0";
      map_maponline1 = baiduProxy + "/online1";
      map_maponline2 = baiduProxy + "/online2";
      map_maponline3 = baiduProxy + "/online3";

      map_g0 = baiduProxy + "/g0";
      map_g1 = baiduProxy + "/g1";
      map_g2 = baiduProxy + "/g2";
      map_g3 = baiduProxy + "/g3";
      map_g4 = baiduProxy + "/g4";

      map_tieba = baiduProxy + "/tieba";
      map_dlswbr = baiduProxy + "/dlswbr";
    }
    if (isProd) {
      document.write("<script type='text/javascript' src='http://*************:18080/bmapjs/bmap-3.0-proxy.min.js'/><\/script>");
    } else {
      document.write("<script type='text/javascript' src='./static/js/bmap-3.0-proxy.min.js'/><\/script>");
      // document.write("<script type='text/javascript' src='https://webapi.amap.com/maps?v=2.0&key=92feba15c3e955a347033e1538f4704a'/><\/script>");
    }
  </script>
  <script type="text/javascript" src="./static/js/MapV.js"></script>
  <script type="text/javascript" src="./static/js/DrawingManager_min.js"></script>
  <script type="text/javascript" src="./static/js/GeoUtils.js"></script>
  <link rel="stylesheet" href="./static/js/DrawingManager_min.css" />
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled. Please enable it
        to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
