window.TILE_VERSION = {
  "ditu": {
    "normal": {"version": "088", "updateDate": "20230524"},
    "satellite": {"version": "009", "updateDate": "20230524"},
    "normalTraffic": {"version": "081", "updateDate": "20230524"},
    "satelliteTraffic": {"version": "083", "updateDate": "20230524"},
    "mapJS": {"version": "104", "updateDate": "20230524"},
    "satelliteStreet": {"version": "083", "updateDate": "20230524"},
    "earthVector": {"version": "001", "updateDate": "20230524"}
  },
  "webapp": {
    "high_normal": {"version": "001", "updateDate": "20230524"},
    "lower_normal": {"version": "002", "updateDate": "20230524"}
  },
  "api_for_mobile": {
    "vector": {"version": "002", "updateDate": "20230524"},
    "vectorIcon": {"version": "002", "updateDate": "20230524"}
  }
};
window.BMAP_AUTHENTIC_KEY = "g2V4AbUccqljDzwG4CQdmhE481EIx8Uv";
(function () {
  function aa(a) {
    throw a;
  }

  var l = void 0, q = !0, s = null, t = !1;

  function ba() {
    return function () {
    }
  }

  function da(a) {
    return function (b) {
      this[a] = b
    }
  }

  function u(a) {
    return function () {
      return this[a]
    }
  }

  function ea(a) {
    return function () {
      return a
    }
  }

  var fa, ga = [];

  function ia(a) {
    return function () {
      return ga[a].apply(this, arguments)
    }
  }

  function ja(a, b) {
    return ga[a] = b
  }

  var ka, x = ka = x || {version: "1.3.4"};
  x.da = "$BAIDU$";
  window[x.da] = window[x.da] || {};
  x.object = x.object || {};
  x.extend = x.object.extend = function (a, b) {
    for (var c in b) b.hasOwnProperty(c) && (a[c] = b[c]);
    return a
  };
  x.U = x.U || {};
  x.U.fa = function (a) {
    return "string" == typeof a || a instanceof String ? document.getElementById(a) : a && a.nodeName && (1 == a.nodeType || 9 == a.nodeType) ? a : s
  };
  x.fa = x.Ic = x.U.fa;
  x.U.aa = function (a) {
    a = x.U.fa(a);
    if (a === s) return a;
    a.style.display = "none";
    return a
  };
  x.aa = x.U.aa;
  x.lang = x.lang || {};
  x.lang.Ig = function (a) {
    return "[object String]" == Object.prototype.toString.call(a)
  };
  x.Ig = x.lang.Ig;
  x.lang.DE = function (a) {
    if ("[object Object]" === Object.prototype.toString.call(a)) {
      for (var b in a) return t;
      return q
    }
    return t
  };
  x.DE = x.lang.DE;
  x.U.gk = function (a) {
    return x.lang.Ig(a) ? document.getElementById(a) : a
  };
  x.gk = x.U.gk;
  x.U.getElementsByClassName = function (a, b) {
    var c;
    if (a.getElementsByClassName) c = a.getElementsByClassName(b); else {
      var e = a;
      e == s && (e = document);
      c = [];
      var e = e.getElementsByTagName("*"), f = e.length, g = RegExp("(^|\\s)" + b + "(\\s|$)"), i, k;
      for (k = i = 0; i < f; i++) g.test(e[i].className) && (c[k] = e[i], k++)
    }
    return c
  };
  x.getElementsByClassName = x.U.getElementsByClassName;
  x.U.contains = function (a, b) {
    var c = x.U.gk, a = c(a), b = c(b);
    return a.contains ? a != b && a.contains(b) : !!(a.compareDocumentPosition(b) & 16)
  };
  x.ga = x.ga || {};
  /msie (\d+\.\d)/i.test(navigator.userAgent) && (x.ga.oa = x.oa = document.documentMode || +RegExp.$1);
  var la = {
    cellpadding: "cellPadding",
    cellspacing: "cellSpacing",
    colspan: "colSpan",
    rowspan: "rowSpan",
    valign: "vAlign",
    usemap: "useMap",
    frameborder: "frameBorder"
  };
  8 > x.ga.oa ? (la["for"] = "htmlFor", la["class"] = "className") : (la.htmlFor = "for", la.className = "class");
  x.U.cH = la;
  x.U.CF = function (a, b, c) {
    a = x.U.fa(a);
    if (a === s) return a;
    if ("style" == b) a.style.cssText = c; else {
      b = x.U.cH[b] || b;
      a.setAttribute(b, c)
    }
    return a
  };
  x.CF = x.U.CF;
  x.U.DF = function (a, b) {
    a = x.U.fa(a);
    if (a === s) return a;
    for (var c in b) x.U.CF(a, c, b[c]);
    return a
  };
  x.DF = x.U.DF;
  x.kl = x.kl || {};
  (function () {
    var a = RegExp("(^[\\s\\t\\xa0\\u3000]+)|([\\u3000\\xa0\\s\\t]+$)", "g");
    x.kl.trim = function (b) {
      return ("" + b).replace(a, "")
    }
  })();
  x.trim = x.kl.trim;
  x.kl.zp = function (a, b) {
    var a = "" + a, c = Array.prototype.slice.call(arguments, 1), e = Object.prototype.toString;
    if (c.length) {
      c = c.length == 1 ? b !== s && /\[object Array\]|\[object Object\]/.test(e.call(b)) ? b : c : c;
      return a.replace(/#\{(.+?)\}/g, function (a, b) {
        var i = c[b];
        "[object Function]" == e.call(i) && (i = i(b));
        return "undefined" == typeof i ? "" : i
      })
    }
    return a
  };
  x.zp = x.kl.zp;
  x.U.rc = function (a, b) {
    a = x.U.fa(a);
    if (a === s) return a;
    for (var c = a.className.split(/\s+/), e = b.split(/\s+/), f, g = e.length, i, k = 0; k < g; ++k) {
      i = 0;
      for (f = c.length; i < f; ++i) if (c[i] == e[k]) {
        c.splice(i, 1);
        break
      }
    }
    a.className = c.join(" ");
    return a
  };
  x.rc = x.U.rc;
  x.U.fy = function (a, b, c) {
    a = x.U.fa(a);
    if (a === s) return a;
    var e;
    if (a.insertAdjacentHTML) a.insertAdjacentHTML(b, c); else {
      e = a.ownerDocument.createRange();
      b = b.toUpperCase();
      if (b == "AFTERBEGIN" || b == "BEFOREEND") {
        e.selectNodeContents(a);
        e.collapse(b == "AFTERBEGIN")
      } else {
        b = b == "BEFOREBEGIN";
        e[b ? "setStartBefore" : "setEndAfter"](a);
        e.collapse(b)
      }
      e.insertNode(e.createContextualFragment(c))
    }
    return a
  };
  x.fy = x.U.fy;
  x.U.show = function (a) {
    a = x.U.fa(a);
    if (a === s) return a;
    a.style.display = "";
    return a
  };
  x.show = x.U.show;
  x.U.XD = function (a) {
    a = x.U.fa(a);
    return a === s ? a : a.nodeType == 9 ? a : a.ownerDocument || a.document
  };
  x.U.ib = function (a, b) {
    a = x.U.fa(a);
    if (a === s) return a;
    for (var c = b.split(/\s+/), e = a.className, f = " " + e + " ", g = 0, i = c.length; g < i; g++) f.indexOf(" " + c[g] + " ") < 0 && (e = e + (" " + c[g]));
    a.className = e;
    return a
  };
  x.ib = x.U.ib;
  x.U.TB = x.U.TB || {};
  x.U.jm = x.U.jm || [];
  x.U.jm.filter = function (a, b, c) {
    for (var e = 0, f = x.U.jm, g; g = f[e]; e++) if (g = g[c]) b = g(a, b);
    return b
  };
  x.kl.ZO = function (a) {
    return a.indexOf("-") < 0 && a.indexOf("_") < 0 ? a : a.replace(/[-_][^-_]/g, function (a) {
      return a.charAt(1).toUpperCase()
    })
  };
  x.U.c1 = function (a) {
    x.U.Dt(a, "expand") ? x.U.rc(a, "expand") : x.U.ib(a, "expand")
  };
  x.U.Dt = function (a) {
    if (arguments.length <= 0 || typeof a === "function") return this;
    if (this.size() <= 0) return t;
    var a = a.replace(/^\s+/g, "").replace(/\s+$/g, "").replace(/\s+/g, " "), b = a.split(" "), c;
    x.forEach(this, function (a) {
      for (var a = a.className, f = 0; f < b.length; f++) if (!~(" " + a + " ").indexOf(" " + b[f] + " ")) {
        c = t;
        return
      }
      c !== t && (c = q)
    });
    return c
  };
  x.U.Hg = function (a, b) {
    var c = x.U, a = c.fa(a);
    if (a === s) return a;
    var b = x.kl.ZO(b), e = a.style[b];
    if (!e) var f = c.TB[b], e = a.currentStyle || (x.ga.oa ? a.style : getComputedStyle(a, s)),
      e = f && f.get ? f.get(a, e) : e[f || b];
    if (f = c.jm) e = f.filter(b, e, "get");
    return e
  };
  x.Hg = x.U.Hg;
  /opera\/(\d+\.\d)/i.test(navigator.userAgent) && (x.ga.opera = +RegExp.$1);
  x.ga.NM = /webkit/i.test(navigator.userAgent);
  x.ga.vZ = /gecko/i.test(navigator.userAgent) && !/like gecko/i.test(navigator.userAgent);
  x.ga.KE = "CSS1Compat" == document.compatMode;
  x.U.ma = function (a) {
    a = x.U.fa(a);
    if (a === s) return a;
    var b = x.U.XD(a), c = x.ga, e = x.U.Hg;
    c.vZ > 0 && b.getBoxObjectFor && e(a, "position");
    var f = {left: 0, top: 0}, g;
    if (a == (c.oa && !c.KE ? b.body : b.documentElement)) return f;
    if (a.getBoundingClientRect) {
      a = a.getBoundingClientRect();
      f.left = Math.floor(a.left) + Math.max(b.documentElement.scrollLeft, b.body.scrollLeft);
      f.top = Math.floor(a.top) + Math.max(b.documentElement.scrollTop, b.body.scrollTop);
      f.left = f.left - b.documentElement.clientLeft;
      f.top = f.top - b.documentElement.clientTop;
      a = b.body;
      b = parseInt(e(a, "borderLeftWidth"));
      e = parseInt(e(a, "borderTopWidth"));
      if (c.oa && !c.KE) {
        f.left = f.left - (isNaN(b) ? 2 : b);
        f.top = f.top - (isNaN(e) ? 2 : e)
      }
    } else {
      g = a;
      do {
        f.left = f.left + g.offsetLeft;
        f.top = f.top + g.offsetTop;
        if (c.NM > 0 && e(g, "position") == "fixed") {
          f.left = f.left + b.body.scrollLeft;
          f.top = f.top + b.body.scrollTop;
          break
        }
        g = g.offsetParent
      } while (g && g != a);
      if (c.opera > 0 || c.NM > 0 && e(a, "position") == "absolute") f.top = f.top - b.body.offsetTop;
      for (g = a.offsetParent; g && g != b.body;) {
        f.left = f.left - g.scrollLeft;
        if (!c.opera || g.tagName != "TR") f.top = f.top - g.scrollTop;
        g = g.offsetParent
      }
    }
    return f
  };
  /firefox\/(\d+\.\d)/i.test(navigator.userAgent) && (x.ga.We = +RegExp.$1);
  /BIDUBrowser/i.test(navigator.userAgent) && (x.ga.z3 = q);
  var ma = navigator.userAgent;
  /(\d+\.\d)?(?:\.\d)?\s+safari\/?(\d+\.\d+)?/i.test(ma) && !/chrome/i.test(ma) && (x.ga.Qy = +(RegExp.$1 || RegExp.$2));
  /chrome\/(\d+\.\d)/i.test(navigator.userAgent) && (x.ga.dx = +RegExp.$1);
  x.oc = x.oc || {};
  x.oc.Rb = function (a, b) {
    var c, e, f = a.length;
    if ("function" == typeof b) for (e = 0; e < f; e++) {
      c = a[e];
      c = b.call(a, c, e);
      if (c === t) break
    }
    return a
  };
  x.Rb = x.oc.Rb;
  x.lang.da = function () {
    return "TANGRAM__" + (window[x.da]._counter++).toString(36)
  };
  window[x.da]._counter = window[x.da]._counter || 1;
  window[x.da]._instances = window[x.da]._instances || {};
  x.lang.Mt = function (a) {
    return "[object Function]" == Object.prototype.toString.call(a)
  };
  x.lang.Ja = function (a) {
    this.da = a || x.lang.da();
    window[x.da]._instances[this.da] = this
  };
  window[x.da]._instances = window[x.da]._instances || {};
  x.lang.Ja.prototype.pi = ia(0);
  x.lang.Ja.prototype.toString = function () {
    return "[object " + (this.cR || "Object") + "]"
  };
  x.lang.av = function (a, b) {
    this.type = a;
    this.returnValue = q;
    this.target = b || s;
    this.currentTarget = s
  };
  x.lang.Ja.prototype.addEventListener = function (a, b, c) {
    if (x.lang.Mt(b)) {
      !b.zl && (b.zl = {});
      !this.Ui && (this.Ui = {});
      var e = this.Ui, f;
      if (typeof c == "string" && c) {
        /[^\w\-]/.test(c) && aa("nonstandard key:" + c);
        f = b.Vx = c
      }
      a.indexOf("on") != 0 && (a = "on" + a);
      typeof e[a] != "object" && (e[a] = {});
      typeof b.zl[a] != "object" && (b.zl[a] = {});
      f = f || x.lang.da();
      b.zl[a].Vx = f;
      e[a][f] = b
    }
  };
  x.lang.Ja.prototype.removeEventListener = function (a, b) {
    a.indexOf("on") != 0 && (a = "on" + a);
    if (x.lang.Mt(b)) {
      if (!b.zl || !b.zl[a]) return;
      b = b.zl[a].Vx
    } else if (!x.lang.Ig(b)) return;
    !this.Ui && (this.Ui = {});
    var c = this.Ui;
    c[a] && c[a][b] && delete c[a][b]
  };
  x.lang.Ja.prototype.dispatchEvent = function (a, b) {
    x.lang.Ig(a) && (a = new x.lang.av(a));
    !this.Ui && (this.Ui = {});
    var b = b || {}, c;
    for (c in b) a[c] = b[c];
    var e = this.Ui, f = a.type;
    a.target = a.target || this;
    a.currentTarget = this;
    f.indexOf("on") != 0 && (f = "on" + f);
    x.lang.Mt(this[f]) && this[f].apply(this, arguments);
    if (typeof e[f] == "object") for (c in e[f]) e[f][c].apply(this, arguments);
    return a.returnValue
  };
  x.lang.xa = function (a, b, c) {
    var e, f, g = a.prototype;
    f = new Function;
    f.prototype = b.prototype;
    f = a.prototype = new f;
    for (e in g) f[e] = g[e];
    a.prototype.constructor = a;
    a.Q0 = b.prototype;
    if ("string" == typeof c) f.cR = c
  };
  x.xa = x.lang.xa;
  x.lang.Uc = function (a) {
    return window[x.da]._instances[a] || s
  };
  x.platform = x.platform || {};
  x.platform.HM = /macintosh/i.test(navigator.userAgent);
  x.platform.N5 = /MicroMessenger/i.test(navigator.userAgent);
  x.platform.OM = /windows/i.test(navigator.userAgent);
  x.platform.EZ = /x11/i.test(navigator.userAgent);
  x.platform.Ej = /android/i.test(navigator.userAgent);
  /android (\d+(\.\d)?)/i.test(navigator.userAgent) && (x.platform.pC = x.pC = RegExp.$1);
  x.platform.xZ = /ipad/i.test(navigator.userAgent);
  x.platform.GE = /iphone/i.test(navigator.userAgent);

  function na(a, b) {
    a.domEvent = b = window.event || b;
    a.clientX = b.clientX || b.pageX;
    a.clientY = b.clientY || b.pageY;
    a.offsetX = b.offsetX || b.layerX;
    a.offsetY = b.offsetY || b.layerY;
    a.screenX = b.screenX;
    a.screenY = b.screenY;
    a.ctrlKey = b.ctrlKey || b.metaKey;
    a.shiftKey = b.shiftKey;
    a.altKey = b.altKey;
    if (b.touches) {
      a.touches = [];
      for (var c = 0; c < b.touches.length; c++) a.touches.push({
        clientX: b.touches[c].clientX,
        clientY: b.touches[c].clientY,
        screenX: b.touches[c].screenX,
        screenY: b.touches[c].screenY,
        pageX: b.touches[c].pageX,
        pageY: b.touches[c].pageY,
        target: b.touches[c].target,
        identifier: b.touches[c].identifier
      })
    }
    if (b.changedTouches) {
      a.changedTouches = [];
      for (c = 0; c < b.changedTouches.length; c++) a.changedTouches.push({
        clientX: b.changedTouches[c].clientX,
        clientY: b.changedTouches[c].clientY,
        screenX: b.changedTouches[c].screenX,
        screenY: b.changedTouches[c].screenY,
        pageX: b.changedTouches[c].pageX,
        pageY: b.changedTouches[c].pageY,
        target: b.changedTouches[c].target,
        identifier: b.changedTouches[c].identifier
      })
    }
    if (b.targetTouches) {
      a.targetTouches = [];
      for (c = 0; c < b.targetTouches.length; c++) a.targetTouches.push({
        clientX: b.targetTouches[c].clientX,
        clientY: b.targetTouches[c].clientY,
        screenX: b.targetTouches[c].screenX,
        screenY: b.targetTouches[c].screenY,
        pageX: b.targetTouches[c].pageX,
        pageY: b.targetTouches[c].pageY,
        target: b.targetTouches[c].target,
        identifier: b.targetTouches[c].identifier
      })
    }
    a.rotation = b.rotation;
    a.scale = b.scale;
    return a
  }

  x.lang.qx = function (a) {
    var b = window[x.da];
    b.iT && delete b.iT[a]
  };
  x.event = {};
  x.V = x.event.V = function (a, b, c) {
    if (!(a = x.fa(a))) return a;
    b = b.replace(/^on/, "");
    a.addEventListener ? a.addEventListener(b, c, t) : a.attachEvent && a.attachEvent("on" + b, c);
    return a
  };
  x.kd = x.event.kd = function (a, b, c) {
    if (!(a = x.fa(a))) return a;
    b = b.replace(/^on/, "");
    a.removeEventListener ? a.removeEventListener(b, c, t) : a.detachEvent && a.detachEvent("on" + b, c);
    return a
  };
  x.U.Dt = function (a, b) {
    if (!a || !a.className || typeof a.className != "string") return t;
    var c = -1;
    try {
      c = a.className == b || a.className.search(RegExp("(\\s|^)" + b + "(\\s|$)"))
    } catch (e) {
      return t
    }
    return c > -1
  };
  x.qL = function () {
    function a(a) {
      document.addEventListener && (this.element = a, this.tL = this.Tk ? "touchstart" : "mousedown", this.DD = this.Tk ? "touchmove" : "mousemove", this.CD = this.Tk ? "touchend" : "mouseup", this.Eh = t, this.Hu = this.Gu = 0, this.element.addEventListener(this.tL, this, t), ka.V(this.element, "mousedown", ba()), this.handleEvent(s))
    }

    a.prototype = {
      Tk: "ontouchstart" in window || "createTouch" in document, start: function (a) {
        oa(a);
        this.Eh = t;
        this.Gu = this.Tk ? a.touches[0].clientX : a.clientX;
        this.Hu = this.Tk ? a.touches[0].clientY : a.clientY;
        this.element.addEventListener(this.DD, this, t);
        this.element.addEventListener(this.CD, this, t)
      }, move: function (a) {
        pa(a);
        var c = this.Tk ? a.touches[0].clientY : a.clientY;
        if (10 < Math.abs((this.Tk ? a.touches[0].clientX : a.clientX) - this.Gu) || 10 < Math.abs(c - this.Hu)) this.Eh = q
      }, end: function (a) {
        pa(a);
        this.Eh || (a = document.createEvent("Event"), a.initEvent("tap", t, q), this.element.dispatchEvent(a));
        this.element.removeEventListener(this.DD, this, t);
        this.element.removeEventListener(this.CD, this, t)
      }, handleEvent: function (a) {
        if (a) switch (a.type) {
          case this.tL:
            this.start(a);
            break;
          case this.DD:
            this.move(a);
            break;
          case this.CD:
            this.end(a)
        }
      }
    };
    return function (b) {
      return new a(b)
    }
  }();
  var A = window.BMap || {};
  A.version = "3.0";

  function qa(a, b) {
    if (navigator.cookieEnabled) {
      var c = new Date;
      c.setTime(c.getTime() + 2592E6);
      document.cookie = a + "=" + escape(b) + ";expires=" + c.toGMTString()
    } else localStorage ? localStorage.setItem(a, b) : sessionStorage && sessionStorage.setItem(a, b)
  }

  A.p3 = 0.34 > Math.random();
  0 <= A.version.indexOf("#") && (A.version = "3.1");
  A.$r = [];
  A.df = function (a) {
    this.$r.push(a)
  };
  A.Sr = [];
  A.Zk = function (a) {
    this.Sr.push(a)
  };
  A.wV = A.apiLoad || ba();
  A.mz = A.verify || function (a) {
    if (A.version && A.version >= 1.5) {
      var b = A.cd + "?qt=verify&ak=" + ra;
      a && (b = b + "&fromPanorama=" + a);
      sa(b, function (a) {
        if (a && a.error !== 0) {
          A = s;
          var b = "\u60a8\u63d0\u4f9b\u7684\u5bc6\u94a5\u4e0d\u662f\u6709\u6548\u7684\u767e\u5ea6LBS\u5f00\u653e\u5e73\u53f0\u5bc6\u94a5\uff0c\u6216\u6b64\u5bc6\u94a5\u672a\u5bf9\u672c\u5e94\u7528\u7684\u767e\u5ea6\u5730\u56feJavaScriptAPI\u6388\u6743\u3002\u60a8\u53ef\u4ee5\u8bbf\u95ee\u5982\u4e0b\u7f51\u5740\u4e86\u89e3\u5982\u4f55\u83b7\u53d6\u6709\u6548\u7684\u5bc6\u94a5\uff1ahttp://lbsyun.baidu.com/apiconsole/key#\u3002";
          a.error && ta[a.error] && (b = ta[a.error] + "\u8be6\u60c5\u67e5\u770b\uff1ahttp://lbsyun.baidu.com/apiconsole/key#\u3002");
          alert(b);
          if (typeof map !== "undefined" && typeof map.Ta === "function") {
            map.Ta().innerHTML = "";
            map.Ui = {}
          }
        }
      })
    }
    var a = +new Date, c = F("script", {type: "text/javascript", async: ""});
    c.charset = "utf-8";
    c.src = "https://dlswbr.baidu.com/heicha/mw/abclite-2063-s.js?_t=" + a;
    c.onload = function () {
      window.___abvk && qa("SECKEY_ABVK", window.___abvk)
    };
    window.__abbaidu_2063_cb = function (a) {
      a = JSON.parse(a);
      qa("BMAP_SECKEY", a.data)
    };
    c.addEventListener ? c.addEventListener("load", function (a) {
      a = a.target;
      a.parentNode.removeChild(a)
    }, t) : c.attachEvent && c.attachEvent("onreadystatechange", function () {
      var a = window.event.srcElement;
      a && (a.readyState == "loaded" || a.readyState == "complete") && a.parentNode.removeChild(a)
    });
    setTimeout(function () {
      document.getElementsByTagName("head")[0].appendChild(c);
      c = s
    }, 1)
  };
  var ra = window.BMAP_AUTHENTIC_KEY;
  window.BMAP_AUTHENTIC_KEY = s;
  var wa = window.BMap_loadScriptTime, xa = (new Date).getTime(), ya = s, za = q, Aa = 5042, Ba = 5002, Ca = 5003,
    Da = "load_mapclick", Ea = 5038, Fa = 5041, Ga = 5047, Ia = 5036, Ja = 5039, Ka = 5037, La = 5040, Ma = 5011,
    Na = 7E3, ta = {
      101: "\u60a8\u6240\u4f7f\u7528\u7684\u5bc6\u94a5ak\u6709\u95ee\u9898\uff0c\u4e0d\u652f\u6301jsapi\u670d\u52a1\uff0c\u53ef\u4ee5\u8bbf\u95ee\u8be5\u7f51\u5740\u4e86\u89e3\u5982\u4f55\u83b7\u53d6\u6709\u6548\u5bc6\u94a5\u3002",
      102: "MCODE\u53c2\u6570\u4e0d\u5b58\u5728\uff0cmobile\u7c7b\u578bMCODE\u53c2\u6570\u5fc5\u9700\u3002",
      200: "APP\u4e0d\u5b58\u5728\uff0cAK\u6709\u8bef\u8bf7\u68c0\u67e5\u518d\u91cd\u8bd5\u3002",
      201: "APP\u88ab\u60a8\u7981\u7528\u5566\u3002",
      202: "APP\u88ab\u7ba1\u7406\u5458\u5220\u9664\u5566\u3002",
      203: "APP\u7c7b\u578b\u9519\u8bef\u3002",
      210: "APP IP\u6821\u9a8c\u5931\u8d25\u3002",
      220: "APP Referer\u6821\u9a8c\u5931\u8d25\u3002\u8bf7\u68c0\u67e5\u8be5ak\u8bbe\u7f6e\u7684\u767d\u540d\u5355\u4e0e\u8bbf\u95ee\u6240\u6709\u7684\u57df\u540d\u662f\u5426\u4e00\u81f4\u3002",
      230: "APP Mcode\u7801\u6821\u9a8c\u5931\u8d25\u3002",
      240: "APP\u670d\u52a1\u88ab\u7981\u7528\u4e86\u3002",
      250: "\u8be5\u7528\u6237\u4e0d\u5b58\u5728...",
      251: "\u8be5\u7528\u6237\u88ab\u81ea\u5df1\u5220\u9664\u5566\u3002",
      252: "\u8be5\u7528\u6237\u88ab\u7ba1\u7406\u5458\u5220\u9664\u5566\u3002",
      260: "\u60a8\u6240\u4f7f\u7528\u7684\u5bc6\u94a5AK\u4e0d\u5305\u542b\u8be5\u670d\u52a1\u5462\uff0c",
      261: "\u60a8\u6240\u4f7f\u7528\u7684\u5bc6\u94a5AK\u7684\u8be5\u670d\u52a1\u88ab\u7981\u7528\u5566\uff0c",
      401: "\u60a8\u6240\u4f7f\u7528\u7684AK\u5e76\u53d1\u8d85\u9650\u4e86\uff0c",
      302: "\u60a8\u6240\u4f7f\u7528\u7684AK\u5929\u914d\u989d\u8d85\u9650\u4e86\uff0c"
    };
  var Oa = 0;

  function Pa(a, b) {
    if (a = x.fa(a)) {
      var c = this;
      x.lang.Ja.call(c);
      b = b || {};
      c.M = {
        EC: 200,
        jc: q,
        wx: t,
        sD: q,
        vp: q,
        xp: b.enableWheelZoom || t,
        oL: q,
        uD: q,
        wp: q,
        dt: q,
        zD: q,
        sp: b.enable3DBuilding || t,
        Nc: 25,
        a2: 240,
        iV: 450,
        Ac: J.Ac,
        Ld: J.Ld,
        Ot: !!b.Ot,
        kc: Math.round(b.minZoom) || 1,
        qc: Math.round(b.maxZoom) || 19,
        Wa: b.mapType || Qa,
        P6: t,
        lL: b.drawer || Oa,
        vx: q,
        ux: 500,
        rX: b.enableHighResolution !== t,
        Gm: b.enableMapClick !== t,
        devicePixelRatio: b.devicePixelRatio || window.devicePixelRatio || 1,
        oG: 99,
        Ee: b.mapStyle || s,
        LZ: b.logoControl === t ? t : q,
        EV: [],
        C3: b.beforeClickIcon || s,
        Yf: t,
        Gk: t,
        mp: t,
        fF: q,
        oD: b.enableBizAuthLogo === t ? t : q,
        Ma: b.coordsType || 5,
        u7: b.touchZoomCenter || 0,
        wD: b.enablePinchDragging === t ? t : q
      };
      c.M.Ee && (this.jZ(c.M.Ee.controls), this.CM(c.M.Ee.geotableId));
      c.M.Ee && c.M.Ee.styleId && c.g5(c.M.Ee.styleId);
      c.M.HC = {
        dark: {backColor: "#2D2D2D", textColor: "#bfbfbf", iconUrl: "dicons"},
        normal: {backColor: "#F3F1EC", textColor: "#c61b1b", iconUrl: "icons"},
        light: {backColor: "#EBF8FC", textColor: "#017fb4", iconUrl: "licons"}
      };
      b.enableAutoResize && (c.M.dt = b.enableAutoResize);
      b.enableStreetEntrance === t && (c.M.zD = b.enableStreetEntrance);
      b.enableDeepZoom === t && (c.M.oL = b.enableDeepZoom);
      var e = c.M.EV;
      if (K()) for (var f = 0, g = e.length; f < g; f++) if (x.ga[e[f]]) {
        c.M.devicePixelRatio = 1;
        break
      }
      e = -1 < navigator.userAgent.toLowerCase().indexOf("android");
      f = -1 < navigator.userAgent.toLowerCase().indexOf("mqqbrowser");
      if (-1 < navigator.userAgent.toLowerCase().indexOf("UCBrowser") || e && f) c.M.oG = 99;
      c.bb = a;
      c.NB(a);
      a.unselectable = "on";
      a.innerHTML = "";
      a.appendChild(c.Ba());
      b.size && this.He(b.size);
      e = c.wb();
      c.width = e.width;
      c.height = e.height;
      c.offsetX = 0;
      c.offsetY = 0;
      c.platform = a.firstChild;
      c.Fe = c.platform.firstChild;
      c.Fe.style.width = c.width + "px";
      c.Fe.style.height = c.height + "px";
      c.ce = {};
      c.ge = new L(0, 0);
      c.Jb = new L(0, 0);
      c.Za = 3;
      c.Bc = 0;
      c.SC = s;
      c.RC = s;
      c.Qb = "";
      c.ex = "";
      c.Uh = {};
      c.Uh.custom = {};
      c.Wi = {};
      c.$a = 0;
      b.useWebGL === t && Ra(t);
      c.W = new Sa(a, {Xe: "api", mT: q});
      c.W.aa();
      c.W.JF(c);
      b = b || {};
      e = c.Wa = c.M.Wa;
      c.Dc = e.Aj();
      e && e.MF(c.M.Ma);
      e === Ta && Ua(Ba);
      e === Va && Ua(Ca);
      e = c.M;
      e.rP = Math.round(b.minZoom);
      e.qP = Math.round(b.maxZoom);
      c.tv();
      c.ba = {Pc: t, pc: 0, Tt: 0, TM: 0, R5: 0, wC: t, qF: -1, xe: []};
      c.platform.style.cursor = c.M.Ac;
      for (f = 0; f < A.$r.length; f++) A.$r[f](c);
      c.ba.qF = f;
      c.ha();
      Wa.load("map", function () {
        c.ob()
      });
      c.M.Gm && (setTimeout(function () {
        Ua(Da)
      }, 1E3), Wa.load("mapclick", function () {
        window.MPC_Mgr = window.MPC_Mgr || {};
        window.MPC_Mgr[c.da] = new Xa(c)
      }, q));
      Ya() && Wa.load("oppc", function () {
        c.jv()
      });
      K() && Wa.load("opmb", function () {
        c.jv()
      });
      a = s;
      c.aC = []
    }
  }

  x.lang.xa(Pa, x.lang.Ja, "Map");
  x.extend(Pa.prototype, {
    Ba: function () {
      var a = F("div"), b = a.style;
      b.overflow = "visible";
      b.position = "absolute";
      b.zIndex = "0";
      b.top = b.left = "0px";
      var b = F("div", {"class": "BMap_mask"}), c = b.style;
      c.position = "absolute";
      c.top = c.left = "0px";
      c.zIndex = "9";
      c.overflow = "hidden";
      c.WebkitUserSelect = "none";
      a.appendChild(b);
      return a
    }, NB: function (a) {
      var b = a.style;
      b.overflow = "hidden";
      "absolute" !== $a(a).position && (b.position = "relative", b.zIndex = 0);
      b.backgroundColor = "#F3F1EC";
      b.color = "#000";
      b.textAlign = "left"
    }, ha: function () {
      var a = this;
      a.To = function () {
        var b = a.wb();
        if (a.width !== b.width || a.height !== b.height) {
          var c = new M(a.width, a.height), e = new O("onbeforeresize");
          e.size = c;
          a.dispatchEvent(e);
          a.xk((b.width - a.width) / 2, (b.height - a.height) / 2);
          a.Fe.style.width = (a.width = b.width) + "px";
          a.Fe.style.height = (a.height = b.height) + "px";
          c = new O("onresize");
          c.size = b;
          a.dispatchEvent(c)
        }
      };
      a.M.dt && (a.ba.tm = setInterval(a.To, 80))
    }, xk: function (a, b, c, e) {
      var f = this.ya().Wb(this.la()), g = this.Dc, i = q;
      if (c && (c instanceof P || c instanceof L)) c = ab(c, this.M.Ma);
      c && P.FE(c) && (this.ge = new L(c.lng, c.lat), i = t);
      if (c = c && e ? g.zi(c, this.Qb) : this.Jb) if (this.Jb = new L(c.lng + a * f, c.lat - b * f), (a = g.Dh(this.Jb, this.Qb)) && i) this.ge = a
    }, Vg: function (a, b) {
      if (bb(a) && (this.tv(), this.dispatchEvent(new O("onzoomstart")), a = this.qo(a).zoom, a !== this.Za)) {
        this.Bc = this.Za;
        this.Za = a;
        var c;
        b ? c = b : this.wh() && (c = this.wh().ma());
        c && (c = this.Do(ab(c, this.M.Ma), this.Bc), this.xk(this.width / 2 - c.x, this.height / 2 - c.y, this.sg(c, this.Bc), q));
        this.dispatchEvent(new O("onzoomstartcode"))
      }
    }, Xc: function (a) {
      this.Vg(a)
    }, uG: function (a) {
      this.Vg(this.Za + 1, a)
    }, vG: function (a) {
      this.Vg(this.Za - 1, a)
    }, Hi: function (a) {
      if (a instanceof P || a instanceof L) a = ab(a, this.M.Ma), this.Jb = this.Dc.zi(a, this.Qb), this.ge = P.FE(a) ? new L(a.lng, a.lat) : this.Dc.Dh(this.Jb, this.Qb)
    }, Og: function (a, b) {
      a = Math.round(a) || 0;
      b = Math.round(b) || 0;
      this.xk(-a, -b)
    }, Xo: function (a) {
      a && cb(a.Le) && (a.Le(this), this.dispatchEvent(new O("onaddcontrol", a)))
    }, dO: function (a) {
      a && cb(a.remove) && (a.remove(), this.dispatchEvent(new O("onremovecontrol", a)))
    }, om: function (a) {
      a && cb(a.za) && (a.za(this), this.dispatchEvent(new O("onaddcontextmenu", a)))
    }, jq: function (a) {
      a && cb(a.remove) && (this.dispatchEvent(new O("onremovecontextmenu", a)), a.remove())
    }, Ra: function (a) {
      a && cb(a.Le) && (a.Le(this), this.dispatchEvent(new O("onaddoverlay", a)))
    }, Lb: function (a) {
      a && cb(a.remove) && (a.remove(), this.dispatchEvent(new O("onremoveoverlay", a)))
    }, HK: function () {
      this.dispatchEvent(new O("onclearoverlays"))
    }, Te: function (a) {
      a && this.dispatchEvent(new O("onaddtilelayer", a))
    }, fg: function (a) {
      a && this.dispatchEvent(new O("onremovetilelayer", a))
    }, Sg: function (a) {
      if (this.Wa !== a) {
        this.M.OZ && this.o0(a);
        var b = new O("onsetmaptype");
        b.G6 = this.Wa;
        this.Wa = this.M.Wa = a;
        this.Dc = this.Wa.Aj();
        this.xk(0, 0, this.Fv(), q);
        this.tv();
        var c = this.qo(this.la()).zoom;
        this.Vg(c);
        this.dispatchEvent(b);
        b = new O("onmaptypechange");
        b.Za = c;
        b.Wa = a;
        this.dispatchEvent(b);
        a.MF(this.M.Ma);
        (a === db || a === Va) && Ua(Ca)
      }
    }, o0: function (a) {
      a === db || a === Va ? (this.Uy(q), this.vO(t), this.M.Yf = t, this.M.Gk = t) : (this.Uy(t), this.vO(q), this.M.Yf = q, this.M.Gk = q)
    }, Af: function (a) {
      var b = this;
      if (a instanceof P || a instanceof L) b.Hi(a, {noAnimation: q}); else if (eb(a)) if (b.Wa === Ta) {
        var c = J.AC[a];
        c && (pt = c.o, b.Af(pt))
      } else {
        var e = this.dI();
        e.vu(function (c) {
          0 === e.Rm() && 2 === e.Ka.result.type && (c = c.Qk(0).point, c = new L(c.lng, c.lat), c = fb(c, b.M.Ma), b.Af(c), Ta.Kk(a) && b.FF(a))
        });
        e.search(a, {log: "center"})
      }
    }, xd: function (a, b) {
      "[object Undefined]" !== Object.prototype.toString.call(b) && (b = parseInt(b));
      A.Vq("cus.fire", "time", {z_loadscripttime: xa - wa});
      var c = this;
      sa(A.cd + "?qt=business&ak=" + ra, function (a) {
        a && (0 === a.error && a.content && 0 === a.content.business && 1 === a.content.unauth) && (new gb).xE({
          zP: c.bb.getAttribute("id"),
          AP: "\u672a\u83b7\u5f97\u767e\u5ea6\u5730\u56fe\u5546\u7528\u6388\u6743"
        })
      });
      if (eb(a)) if (c.Wa === Ta) {
        var e = J.AC[a];
        e && (pt = e.o, c.xd(pt, b))
      } else {
        var f = c.dI();
        f.vu(function (e) {
          if (0 === f.Rm() && (2 === f.Ka.result.type || 11 === f.Ka.result.type)) {
            var g = e.Qk(0).point, e = b || hb.Bx(f.Ka.content.level, c), g = new L(g.lng, g.lat);
            c.xd(g, e);
            Ta.Kk(a) && c.FF(a)
          }
        });
        f.search(a, {log: "center"})
      } else if ((a instanceof P || a instanceof L) && b) {
        b = c.qo(b).zoom;
        c.Bc = c.Za || b;
        c.Za = b;
        e = c.ge;
        a = ab(a, this.M.Ma);
        c.ge = new L(a.lng, a.lat);
        c.Jb = c.Dc.zi(c.ge, c.Qb);
        c.SC = c.SC || c.Za;
        c.RC = c.RC || c.ge;
        var g = new O("onload"), i = new O("onloadcode");
        g.point = new L(a.lng, a.lat);
        g.pixel = c.Do(c.ge, c.Za);
        g.zoom = b;
        c.loaded || (c.loaded = q, c.dispatchEvent(g), ya || (ya = ib()));
        c.dispatchEvent(i);
        g = new O("onmoveend");
        g.kA = "centerAndZoom";
        e.Vb(c.ge) || c.dispatchEvent(g);
        c.dispatchEvent(new O("onmoveend"));
        c.Bc !== c.Za && (e = new O("onzoomend"), e.kA = "centerAndZoom", c.dispatchEvent(e));
        c.M.sp && c.sp()
      }
    }, dI: function () {
      this.ba.dN || (this.ba.dN = new jb(1));
      return this.ba.dN
    }, reset: function () {
      this.xd(this.RC, this.SC, q)
    }, enableDragging: function () {
      this.M.jc = q
    }, disableDragging: function () {
      this.M.jc = t
    }, enableInertialDragging: function () {
      this.M.vx = q
    }, disableInertialDragging: function () {
      this.M.vx = t
    }, enableScrollWheelZoom: function () {
      this.M.xp = q
    }, disableScrollWheelZoom: function () {
      this.M.xp = t
    }, enableContinuousZoom: function () {
      this.M.vp = q
    }, disableContinuousZoom: function () {
      this.M.vp = t
    }, enableDoubleClickZoom: function () {
      this.M.sD = q
    }, disableDoubleClickZoom: function () {
      this.M.sD = t
    }, enableKeyboard: function () {
      this.M.wx = q
    }, disableKeyboard: function () {
      this.M.wx = t
    }, enablePinchToZoom: function () {
      this.M.wp = q
    }, disablePinchToZoom: function () {
      this.M.wp = t
    }, enableAutoResize: function () {
      this.M.dt = q;
      this.To();
      this.ba.tm || (this.ba.tm = setInterval(this.To, 80))
    }, disableAutoResize: function () {
      this.M.dt = t;
      this.ba.tm && (clearInterval(this.ba.tm), this.ba.tm = s)
    }, enableBizAuthLogo: function () {
      this.M.oD = q;
      this.bp && this.bp.show()
    }, disableBizAuthLogo: function () {
      this.M.oD = t;
      this.bp && this.bp.aa()
    }, enableMapClick: function () {
      this.M.Gm = q;
      var a = this;
      window.MPC_Mgr && window.MPC_Mgr[a.da] ? window.MPC_Mgr[a.da].open() : (setTimeout(function () {
        Ua(Da)
      }, 1E3), Wa.load("mapclick", function () {
        window.MPC_Mgr = window.MPC_Mgr || {};
        window.MPC_Mgr[a.da] = new Xa(a)
      }, q))
    }, disableMapClick: function () {
      window.MPC_Mgr && window.MPC_Mgr[this.da] && window.MPC_Mgr[this.da].close();
      this.M.Gm = t
    }, sp: function () {
      this.M.sp = q;
      this.co || (this.co = new BuildingLayer({n4: q}), this.Te(this.co))
    }, SW: function () {
      this.M.sp = t;
      this.co && (this.fg(this.co), this.co = s, delete this.co)
    }, wb: function () {
      return this.Rs && this.Rs instanceof M ? new M(this.Rs.width, this.Rs.height) : new M(this.bb.clientWidth, this.bb.clientHeight)
    }, He: function (a) {
      a && a instanceof M ? (this.Rs = a, this.bb.style.width = a.width + "px", this.bb.style.height = a.height + "px") : this.Rs = s
    }, Hb: function () {
      return fb(this.ge, this.M.Ma)
    }, Fv: u("ge"), la: u("Za"), cW: function () {
      this.To()
    }, qo: function (a) {
      var b = this.M.kc, c = this.M.qc, e = t, a = Math.round(a);
      a < b && (e = q, a = b);
      a > c && (e = q, a = c);
      return {zoom: a, ED: e}
    }, Ta: u("bb"), vc: function (a, b) {
      a = ab(a, this.M.Ma);
      b = b || this.la();
      return this.Dc.vc(a, b, this.Jb, this.wb(), this.Qb)
    }, Do: function (a, b) {
      b = b || this.la();
      return this.Dc.vc(a, b, this.Jb, this.wb(), this.Qb)
    }, sg: function (a, b) {
      b = b || this.la();
      return this.Dc.cc(a, b, this.Jb, this.wb(), this.Qb)
    }, UT: function (a, b) {
      b = b || this.la();
      return this.Dc.Hy(a, b, this.Jb, this.wb())
    }, Hy: function (a, b) {
      return this.UT(a, b)
    }, cc: function (a, b) {
      return fb(this.sg(a, b), this.M.Ma)
    }, cf: function (a, b) {
      if (a) {
        var a = ab(a, this.M.Ma), c = this.Do(new L(a.lng, a.lat), b);
        c.x -= this.offsetX;
        c.y -= this.offsetY;
        return c
      }
    }, RZ: function (a, b) {
      b = b || this.la();
      return this.Dc.SZ(a, b, this.Jb, this.wb(), this.Qb)
    }, QZ: function (a, b) {
      if (a) {
        var c = this.RZ(new L(a.lng, a.lat), b);
        c.x -= this.offsetX;
        c.y -= this.offsetY;
        return c
      }
    }, ON: function (a, b) {
      if (a) {
        var c = new Q(a.x, a.y);
        c.x += this.offsetX;
        c.y += this.offsetY;
        return this.cc(c, b)
      }
    }, PT: function (a, b) {
      if (a) {
        var c = new Q(a.x, a.y);
        c.x += this.offsetX;
        c.y += this.offsetY;
        return this.sg(c, b)
      }
    }, pointToPixelFor3D: function (a, b) {
      var c = map.Qb;
      this.Wa === Ta && c && kb.NK(a, this, b)
    }, x6: function (a, b) {
      var c = map.Qb;
      this.Wa === Ta && c && kb.MK(a, this, b)
    }, y6: function (a, b) {
      var c = this, e = map.Qb;
      c.Wa === Ta && e && kb.NK(a, c, function (a) {
        a.x -= c.offsetX;
        a.y -= c.offsetY;
        b && b(a)
      })
    }, v6: function (a, b) {
      var c = map.Qb;
      this.Wa === Ta && c && (a.x += this.offsetX, a.y += this.offsetY, kb.MK(a, this, b))
    }, ke: function (a) {
      if (!this.Nt()) return new lb;
      var b = a || {}, a = b.margins || [0, 0, 0, 0], c = b.zoom || s, b = this.cc({x: a[3], y: this.height - a[2]}, c),
        a = this.cc({x: this.width - a[1], y: a[0]}, c);
      return new lb(b, a)
    }, VX: function (a) {
      if (!this.Nt()) return new lb;
      var b = a || {}, a = b.margins || [0, 0, 0, 0], c = b.zoom || s, b = this.Hy({x: a[3], y: this.height - a[2]}, c),
        a = this.Hy({x: this.width - a[1], y: a[0]}, c);
      return new lb(b, a)
    }, Nt: function () {
      return !!this.loaded
    }, rS: function (a, b) {
      for (var c = this.ya(), e = b.margins || [10, 10, 10, 10], f = b.zoomFactor || 0, g = e[1] + e[3], e = e[0] + e[2], i = c.sf(), k = c = c.Ye(); k >= i; k--) {
        var m = this.ya().Wb(k);
        if (a.gG().lng / m < this.width - g && a.gG().lat / m < this.height - e) break
      }
      k += f;
      k < i && (k = i);
      k > c && (k = c);
      return k
    }, Ct: function (a, b) {
      var c = {center: this.Hb(), zoom: this.la()};
      if (!a || !a instanceof lb && 0 === a.length || a instanceof lb && a.Gj()) return c;
      var e = [];
      a instanceof lb ? (e.push(a.tf()), e.push(a.Be())) : e = a.slice(0);
      for (var b = b || {}, f = [], g = 0, i = e.length; g < i; g++) {
        var k = ab(e[g], this.M.Ma);
        f.push(this.Dc.zi(k, this.Qb))
      }
      e = new lb;
      for (g = f.length - 1; 0 <= g; g--) e.extend(f[g]);
      if (e.Gj()) return c;
      c = e.Hb();
      f = this.rS(e, b);
      b.margins && (e = b.margins, g = (e[1] - e[3]) / 2, e = (e[0] - e[2]) / 2, i = this.ya().Wb(f), b.offset && (g = b.offset.width, e = b.offset.height), c.lng += i * g, c.lat += i * e);
      c = this.Dc.Dh(c, this.Qb);
      return {center: fb(new L(c.lng, c.lat), this.M.Ma), zoom: f}
    }, Tg: function (a, b) {
      var c;
      c = a && a.center ? a : this.Ct(a, b);
      var b = b || {}, e = b.delay || 200;
      if (c.zoom === this.Za && b.enableAnimation !== t) {
        var f = this;
        setTimeout(function () {
          f.Hi(c.center, {duration: 210})
        }, e)
      } else this.xd(c.center, c.zoom)
    }, Zf: u("ce"), wh: function () {
      return this.ba.xb && this.ba.xb.eb() ? this.ba.xb : s
    }, getDistance: function (a, b) {
      if (a && b) {
        if (a.Vb(b)) return 0;
        var c = this.M ? this.M.Ma : 5, a = ab(a, c), b = ab(b, c), c = 0, c = R.Mk(a, b);
        if (c === s || c === l) c = 0;
        return c
      }
    }, Nx: function () {
      var a = [], b = this.ua, c = this.Ie;
      if (b) for (var e in b) b[e] instanceof mb && a.push(b[e]);
      if (c) {
        e = 0;
        for (b = c.length; e < b; e++) a.push(c[e])
      }
      return a
    }, ya: function () {
      this.Wa.MF(this.M.Ma);
      return this.Wa
    }, NY: u("Id"), jv: function () {
      for (var a = this.ba.qF; a < A.$r.length; a++) A.$r[a](this);
      this.ba.qF = a
    }, FF: function (a) {
      this.Qb = Ta.Kk(a);
      this.ex = Ta.JL(this.Qb);
      this.Wa === Ta && this.Dc instanceof nb && (this.Dc.rj = this.Qb)
    }, setDefaultCursor: function (a) {
      this.M.Ac = a;
      this.platform && (this.platform.style.cursor = this.M.Ac)
    }, getDefaultCursor: function () {
      return this.M.Ac
    }, setDraggingCursor: function (a) {
      this.M.Ld = a
    }, getDraggingCursor: function () {
      return this.M.Ld
    }, $x: function () {
      return this.M.rX && 1.5 <= this.M.devicePixelRatio
    }, gC: function (a, b) {
      b ? this.Uh[b] || (this.Uh[b] = {}) : b = "custom";
      a.tag = b;
      a instanceof ob && (this.Uh[b][a.da] = a, a.za(this));
      var c = this;
      Wa.load("hotspot", function () {
        c.jv()
      }, q)
    }, L_: function (a, b) {
      b || (b = "custom");
      this.Uh[b][a.da] && delete this.Uh[b][a.da]
    }, gx: function (a) {
      a || (a = "custom");
      this.Uh[a] = {}
    }, tv: function () {
      var a = this.Wa.sf(), b = this.Wa.Ye(), c = this.M;
      c.kc = c.rP || a;
      c.qc = c.qP || b;
      c.kc < a && (c.kc = a);
      c.qc > b && (c.qc = b)
    }, setMinZoom: function (a) {
      a = Math.round(a);
      a > this.M.qc && (a = this.M.qc);
      this.M.rP = a;
      this.SJ()
    }, setMaxZoom: function (a) {
      a = Math.round(a);
      a < this.M.kc && (a = this.M.kc);
      this.M.qP = a;
      this.SJ()
    }, SJ: function () {
      this.tv();
      var a = this.M;
      this.Za < a.kc ? this.Xc(a.kc) : this.Za > a.qc && this.Xc(a.qc);
      var b = new O("onzoomspanchange");
      b.kc = a.kc;
      b.qc = a.qc;
      this.dispatchEvent(b)
    }, k5: u("aC"), getKey: function () {
      return ra
    }, q0: function (a) {
      function b(a) {
        c.M0 = a;
        var b = A.cd + "custom/v2/mapstyle", g = "qt=custom_v2&version=4&ak=" + ra + "&",
          g = g + "is_all=true&is_new=1&" + ("styles=" + encodeURIComponent(c.$F(a, f))), a = pb(b + "?" + g),
          g = a.substring(a.indexOf("?") + 1);
        qb(b, g, window[e + "cb"])
      }

      var c = this, e = this.da;
      A.Vq("cus.fire", "count", "z_setmapstylev2count");
      this.Uy(t);
      this.M.OZ = q;
      window.MPC_Mgr && window.MPC_Mgr[c.da] && window.MPC_Mgr[c.da].close();
      c.M.Gm = t;
      this.addEventListener("hidecopyright", function () {
        c.Dk.aa();
        c.M.mp = !!a.customEditor;
        c.M.mp === t && c.EF(new M(1, 1))
      });
      c.Dk && c.Dk.aa();
      this.M.mp = !!a.customEditor;
      this.M.e7 = !!a.sharing;
      this.M.K6 = !!a.preview;
      this.M.mp === t && this.EF(new M(1, 1));
      Wa.load("hotspot", function () {
        c.jv()
      }, q);
      window[e + "zoomRegion"] = {};
      window.R7 = [];
      window[e + "zoomStyleBody"] = [];
      window[e + "zoomFrontStyle"] = {};
      var f = this.la();
      x.extend({}, a);
      window[e + "cb"] = function (a) {
        a = JSON.parse(a);
        0 === a.status && (3 === a.data.style.length ? (window[e + "_bmap_baseFs"] = a.data.style, window[e + "StyleBody"] = a.data.style[2]) : window[e + "StyleBody"] = a.data.style, c.mP(), c.qZ())
      };
      if (a.styleId) {
        var g = "jsapi";
        a.sharing ? g = "sharing" : a.preview && (g = "preview");
        this.lY(a.styleId, g, b)
      } else b(a.styleJson);
      window.iconSetInfo_high || sa(A.url.proto + A.url.domain.TILE_ONLINE_URLS[0] + "/sty/icons_na2x.js?udt=20190108&v=001&from=jsapi")
    }, lY: function (a, b, c) {
      var e = this, f = this.da, g = (1E5 * Math.random()).toFixed(0);
      window[f + "_cbk_si_phpui" + g] = function (a) {
        var b = [];
        a.result && (0 === a.result.error && a.content && 0 === a.content.status) && (b = e.Ey(a.content.data.json));
        c && c(b)
      };
      window[f + "_cbk_si_api" + g] = function (a) {
        var b = [];
        0 === a.status && (b = a.info ? e.Ey(a.info.json) : e.Ey(a.data.json));
        c && c(b)
      };
      var i = "/apiconsole/custommap/";
      switch (b) {
        case "jsapi":
          i = A.cd + "?qt=custom_map&v=3.0";
          i += "&style_id=" + a + "&type=publish&ak=" + ra;
          i += "&callback=" + f + "_cbk_si_phpui" + g;
          break;
        case "sharing":
          i = i + "getSharingJson" + ("?styleid=" + a + "&type=edit") + ("&ck=" + f + "_cbk_si_api" + g);
          break;
        case "preview":
          i = i + "getJson" + ("?styleid=" + a + "&type=edit") + ("&ck=" + f + "_cbk_si_api" + g)
      }
      sa(i)
    }, LW: function () {
      Array.prototype.map || (Array.prototype.map = function (a, b) {
        var c, e, f;
        this == s && aa(new TypeError(" this is null or not defined"));
        var g = Object(this), i = g.length >>> 0;
        "[object Function]" != Object.prototype.toString.call(a) && aa(new TypeError(a + " is not a function"));
        b && (c = b);
        e = Array(i);
        for (f = 0; f < i;) {
          var k;
          f in g && (k = g[f], k = a.call(c, k, f, g), e[f] = k);
          f++
        }
        return e
      })
    }, Ey: function (a) {
      if (a === s || "" === a) return [];
      this.LW();
      var b = {
        t: "featureType",
        e: "elementType",
        v: "visibility",
        c: "color",
        l: "lightness",
        s: "saturation",
        w: "weight",
        z: "level",
        h: "hue",
        f: "fontsize",
        zri: "curZoomRegionId",
        zr: "curZoomRegion"
      }, c = {
        all: "all",
        g: "geometry",
        "g.f": "geometry.fill",
        "g.s": "geometry.stroke",
        l: "labels",
        "l.t.f": "labels.text.fill",
        "l.t.s": "labels.text.stroke",
        "l.t": "labels.text",
        "l.i": "labels.icon",
        "g.tf": "geometry.fill"
      };
      return a.split(",").map(function (a) {
        var a = a.split("|").map(function (a) {
          var e = b[a.split(":")[0]], a = c[a.split(":")[1]] ? c[a.split(":")[1]] : a.split(":")[1];
          switch (a) {
            case "poi":
              a = "poilabel";
              break;
            case "districtlabel":
              a = "districtlabel"
          }
          var f = {};
          f[e] = a;
          return f
        }), f = a[0], g = 1;
        a[1].elementType && (g = 2, x.extend(f, a[1]));
        for (var i = {}; g < a.length; g++) x.extend(i, a[g]);
        return x.extend(f, {stylers: i})
      })
    }, SY: function () {
      return this.ef.ng
    }, V4: function (a, b) {
      var c = this, e = this.da, f = (1E5 * Math.random()).toFixed(0);
      window[e + "_cbk" + f] = function (b) {
        b = JSON.parse(b);
        b = 3 === b.data.style.length ? b.data.style[2] : b.data.style;
        c.v1(b, a);
        c.mP(a);
        b = new O("onzoomfeatureload" + a);
        c.dispatchEvent(b);
        delete window[e + "_cbk" + f]
      };
      var g = A.cd + "custom/v2/mapstyle", i = "qt=custom_v2&ak=" + ra + "&", i = i + "is_all=true&is_new=1&";
      b.styleJson ? i += "styles=" + encodeURIComponent(this.$F(b.styleJson, parseInt(a, 10))) : b.styleId && (i += "styles=" + encodeURIComponent(c.$F(c.M0, parseInt(a, 10))));
      i = pb(g + "?" + i);
      i = i.substring(i.indexOf("?") + 1);
      qb(g, i, window[e + "_cbk" + f])
    }, EF: function (a, b) {
      var c = new O("oncopyrightoffsetchange", {VE: a, yW: b});
      this.M.RK = b;
      this.dispatchEvent(c)
    }, su: function (a) {
      var b = this;
      window.MPC_Mgr && window.MPC_Mgr[b.da] && window.MPC_Mgr[b.da].close();
      b.M.Gm = t;
      A.Vq("cus.fire", "count", "z_setmapstylecount");
      if (a) {
        b = this;
        a.styleJson && (a.styleStr = b.N0(a.styleJson));
        K() && x.ga.Qy ? setTimeout(function () {
          b.M.Ee = a;
          b.dispatchEvent(new O("onsetcustomstyles", a))
        }, 50) : (this.M.Ee = a, this.dispatchEvent(new O("onsetcustomstyles", a)), this.CM(b.M.Ee.geotableId));
        var c = {style: a.style};
        a.features && 0 < a.features.length && (c.features = q);
        a.styleJson && 0 < a.styleJson.length && (c.styleJson = q);
        Ua(5050, c);
        a.style && (c = b.M.HC[a.style] ? b.M.HC[a.style].backColor : b.M.HC.normal.backColor) && (this.Ta().style.backgroundColor = c)
      }
    }, jZ: function (a) {
      this.controls || (this.controls = {
        navigationControl: new rb,
        scaleControl: new sb,
        overviewMapControl: new tb,
        mapTypeControl: new ub
      });
      var b = this, c;
      for (c in this.controls) b.dO(b.controls[c]);
      a = a || [];
      x.oc.Rb(a, function (a) {
        b.Xo(b.controls[a])
      })
    }, CM: function (a) {
      a ? this.Ps && this.Ps.Kf === a || (this.fg(this.Ps), this.Ps = new vb({geotableId: a}), this.Te(this.Ps)) : this.fg(this.Ps)
    }, Vd: function () {
      var a = this.la() >= this.M.oG && this.ya() === Qa && 18 >= this.la(), b = t;
      try {
        document.createElement("canvas").getContext("2d"), b = q
      } catch (c) {
        b = t
      }
      return a && b
    }, getCurrentCity: function () {
      return {name: this.qh, code: this.Cs}
    }, xt: function () {
      this.W.vo();
      return this.W
    }, nZ: function (a) {
      Qa.setMaxZoom(a.maxZoom || 19);
      var b = new O("oninitindoorlayer");
      b.$e = a;
      this.dispatchEvent(b);
      this.M.Yf = t
    }, qZ: function (a) {
      if (this.M.Yf) {
        var b = new O("onupdatestyles");
        this.dispatchEvent(b)
      } else b = new O("oninitindoorlayer"), b.$e = a, this.dispatchEvent(b), this.M.Yf = q, this.M.Gk = q
    }, Uy: function (a) {
      this.M.fF = a;
      this.ef.Mb || (this.ef.Mb = this.ef.Ij[0].Mb);
      this.ef.Mb.parentElement.style.display = a ? "block" : "none"
    }, vO: function (a) {
      this.ef.ng.style.display = a ? "block" : "none"
    }, setPanorama: function (a) {
      this.W = a;
      this.W.JF(this)
    }, $F: function (a, b) {
      for (var c = this.da, e = {
        featureType: "t",
        elementType: "e",
        visibility: "v",
        color: "c",
        lightness: "l",
        saturation: "s",
        weight: "w",
        level: "z",
        hue: "h",
        fontsize: "f"
      }, f = {
        all: "all",
        geometry: "g",
        "geometry.fill": "g.f",
        "geometry.stroke": "g.s",
        labels: "l",
        "labels.text.fill": "l.t.f",
        "labels.text.stroke": "l.t.s",
        "labels.text": "l.t",
        "labels.icon": "l.i",
        "geometry.topfill": "g.f"
      }, g = [], i = this.Wa.sf(); i <= this.Wa.Ye(); i++) window[c + "zoomFrontStyle"][i] = {};
      window[c + "zoomFrontStyle"].main = {};
      for (var i = 0, k; k = a[i]; i++) if (!this.yZ(k)) {
        b = this.fY(k, b);
        if (("land" === k.featureType || "all" === k.featureType || "background" === k.featureType) && "string" === typeof k.elementType && ("geometry" === k.elementType || "geometry.fill" === k.elementType || "all" === k.elementType) && k.stylers && (!k.stylers.visibility || "off" !== k.stylers.visibility)) k.stylers.color && (window[c + "zoomFrontStyle"][b].bmapLandColor = k.stylers.color);
        "railway" === k.featureType && ("string" === typeof k.elementType && k.stylers) && (k.stylers.color && ("geometry" === k.elementType && (window[c + "zoomFrontStyle"][b].bmapRailwayFillColor = k.stylers.color, window[c + "zoomFrontStyle"][b].bmapRailwayStrokeColor = k.stylers.color), "geometry.fill" === k.elementType && (window[c + "zoomFrontStyle"][b].bmapRailwayFillColor = k.stylers.color), "geometry.stroke" === k.elementType && (window[c + "zoomFrontStyle"][b].bmapRailwayStrokeColor = k.stylers.color)), k.stylers.visibility && (window[c + "zoomFrontStyle"][b].bmapRailwayVisibility = k.stylers.visibility));
        "roadarrow" === k.featureType && ("labels.icon" === k.elementType && k.stylers) && (window[c + "zoomFrontStyle"][b].bmapRoadarrowVisibility = k.stylers.visibility);
        var m = {};
        x.extend(m, k);
        k = m.stylers;
        delete m.stylers;
        x.extend(m, k);
        k = [];
        for (var n in e) if (m[n] && !this.uZ(n)) if ("elementType" === n) k.push(e[n] + ":" + f[m[n]]); else {
          switch (m[n]) {
            case "poilabel":
              m[n] = "poi";
              break;
            case "districtlabel":
              m[n] = "label"
          }
          k.push(e[n] + ":" + m[n])
        }
        2 < k.length && g.push(k.join("|"))
      }
      return g.join(",")
    }, N0: function (a) {
      for (var b = {
        featureType: "t",
        elementType: "e",
        visibility: "v",
        color: "c",
        lightness: "l",
        saturation: "s",
        weight: "w",
        zoom: "z",
        hue: "h"
      }, c = {
        all: "all",
        geometry: "g",
        "geometry.fill": "g.f",
        "geometry.stroke": "g.s",
        labels: "l",
        "labels.text.fill": "l.t.f",
        "labels.text.stroke": "l.t.s",
        "lables.text": "l.t",
        "labels.icon": "l.i"
      }, e = [], f = 0, g; g = a[f]; f++) {
        var i = g.stylers;
        delete g.stylers;
        x.extend(g, i);
        var i = [], k;
        for (k in b) if (g[k]) if ("elementType" === k) i.push(b[k] + ":" + c[g[k]]); else {
          switch (g[k]) {
            case "poilabel":
              g[k] = "poi";
              break;
            case "districtlabel":
              g[k] = "label"
          }
          i.push(b[k] + ":" + g[k])
        }
        2 < i.length && e.push(i.join("|"))
      }
      return e.join(",")
    }, fY: function (a) {
      a = a.stylers.level;
      return a === l ? "main" : parseInt(a, 10)
    }, yZ: function (a) {
      var b = {};
      x.extend(b, a.stylers);
      delete b.curZoomRegionId;
      delete b.curZoomRegion;
      delete b.level;
      return x.DE(b) ? q : t
    }, J5: function (a, b) {
      var c = a.stylers.level;
      return c === l ? q : c === b + "" ? q : t
    }, uZ: function (a) {
      return {curZoomRegionId: q, curZoomRegion: q}[a] ? q : t
    }, l5: function (a, b) {
      var c = a.stylers.level, e = {};
      x.extend(e, b);
      c !== l && (e[parseInt(c, 10)] = q);
      return e
    }, v1: function (a, b) {
      var c = this.da;
      window[c + "zoomStyleBody"][b] = a;
      if (!window[c + "zoomRegion"][b]) for (var e = this.Wa.sf(), f = this.Wa.Ye(); e <= f; e++) window[c + "zoomRegion"][e] || (window[c + "zoomStyleBody"][e] = a)
    }, mP: function () {
      var a = this.da;
      if (window[a + "zoomFrontStyle"].main.bmapRoadarrowVisibility) for (var b = this.Wa.sf(); b <= this.Wa.Ye(); b++) window[a + "zoomFrontStyle"][b].bmapRoadarrowVisibility || (window[a + "zoomFrontStyle"][b].bmapRoadarrowVisibility = window[a + "zoomFrontStyle"].main.bmapRoadarrowVisibility);
      if (window[a + "zoomFrontStyle"].main.bmapLandColor) for (b = this.Wa.sf(); b <= this.Wa.Ye(); b++) window[a + "zoomFrontStyle"][b].bmapLandColor || (window[a + "zoomFrontStyle"][b].bmapLandColor = window[a + "zoomFrontStyle"].main.bmapLandColor);
      if (window[a + "zoomFrontStyle"].main.bmapRailwayFillColor) for (b = this.Wa.sf(); b <= this.Wa.Ye(); b++) window[a + "zoomFrontStyle"][b].bmapRailwayFillColor || (window[a + "zoomFrontStyle"][b].bmapRailwayFillColor = window[a + "zoomFrontStyle"].main.bmapRailwayFillColor);
      if (window[a + "zoomFrontStyle"].main.bmapRailwayStrokeColor) for (b = this.Wa.sf(); b <= this.Wa.Ye(); b++) window[a + "zoomFrontStyle"][b].bmapRailwayStrokeColor || (window[a + "zoomFrontStyle"][b].bmapRailwayStrokeColor = window[a + "zoomFrontStyle"].main.bmapRailwayStrokeColor);
      if (window[a + "zoomFrontStyle"].main.bmapRailwayVisibility) for (b = this.Wa.sf(); b <= this.Wa.Ye(); b++) window[a + "zoomFrontStyle"][b].bmapRailwayVisibility || (window[a + "zoomFrontStyle"][b].bmapRailwayVisibility = window[a + "zoomFrontStyle"].main.bmapRailwayVisibility)
    }, F3: function (a, b) {
      var c = {};
      x.extend(c, a);
      if (c[b]) {
        for (var e = this.Wa.sf(), f = this.Wa.Ye(); e <= f; e++) if (!c[e]) {
          c[e] = q;
          break
        }
        delete c[b]
      }
      return c
    }, H5: function (a) {
      return a.Lt || "0" === a.uid ? t : q
    }, gW: function () {
      delete this.Wi.A_
    }, K3: function () {
      this.Wi = {}
    }, Wo: function (a, b, c) {
      if (!this.M.mp) return t;
      a = a || "sp" + this.ba.h7++;
      if (b && 0 !== b.length) return c = c || {}, this.Wi[a] = this.Wi[a] || {
        polygon: [],
        polyline: []
      }, this.Wi = this.Wi || {}, this.Wi[a][c.type].push({VF: b, Yb: c.Yb, type: c.type, style: c.style}), a
    }, S1: function (a) {
      return wb / Math.pow(2, 18 - a)
    }
  });
  var wb = 4.007545274461451E7;

  function Ua(a, b) {
    if (a) {
      var b = b || {}, c = "", e;
      for (e in b) c = c + "&" + e + "=" + encodeURIComponent(b[e]);
      var f = function (a) {
        a && (xb = q, setTimeout(function () {
          yb.src = A.cd + "images/blank.gif?" + a.src
        }, 50))
      }, g = function () {
        var a = zb.shift();
        a && f(a)
      };
      e = (1E8 * Math.random()).toFixed(0);
      xb ? zb.push({src: "product=jsapi&sub_product=jsapi&v=" + A.version + "&sub_product_v=" + A.version + "&t=" + e + "&code=" + a + "&da_src=" + a + c}) : f({src: "product=jsapi&sub_product=jsapi&v=" + A.version + "&sub_product_v=" + A.version + "&t=" + e + "&code=" + a + "&da_src=" + a + c});
      Ab || (x.V(yb, "load", function () {
        xb = t;
        g()
      }), x.V(yb, "error", function () {
        xb = t;
        g()
      }), Ab = q)
    }
  }

  var xb, Ab, zb = [], yb = new Image;
  Ua(5E3, {device_pixel_ratio: window.devicePixelRatio, platform: navigator.platform});
  A.wM = {
    TILE_BASE_URLS: [map_gss0_bdstatic+"/starpic/?qt=satepc&", map_gss0_bdstatic+"/starpic/?qt=satepc&", map_gss0_bdstatic+"/starpic/?qt=satepc&", map_gss0_bdstatic+"/starpic/?qt=satepc&"],
    TILE_ONLINE_URLS: [map_maponline0, map_maponline1, map_maponline2, map_maponline3],
    TIlE_PERSPECT_URLS: [map_gss0_bdstatic+"/-OR1cTe9KgQFm2e88IuM_a", map_gss0_bdstatic+"/-ON1cTe9KgQFm2e88IuM_a", map_gss0_bdstatic+"/-OZ1cTe9KgQFm2e88IuM_a", map_gss0_bdstatic+"/-OV1cTe9KgQFm2e88IuM_a"],
    geolocControl: "gsp0.baidu.com/8LkJsjOpB1gCo2Kml5_Y_D3",
    TILES_YUN_HOST: ["gsp0.baidu.com/-eR1bSahKgkFkRGko9WTAnF6hhy", "gsp0.baidu.com/-eN1bSahKgkFkRGko9WTAnF6hhy", "gsp0.baidu.com/-eZ1bSahKgkFkRGko9WTAnF6hhy", "gsp0.baidu.com/-eV1bSahKgkFkRGko9WTAnF6hhy"],
    traffic: "itsmap2.baidu.com",
    message: "j.map.baidu.com",
    baidumap: "map.baidu.com",
    wuxian: "gsp0.baidu.com/6a1OdTeaKgQFm2e88IuM_a",
    pano: ["apisv0.bdimg.com", "apisv1.bdimg.com"],
    panoVerify: "api.map.baidu.com",
    main_domain_nocdn: {baidu: "api.map.baidu.com", other: "api.map.baidu.com"},
    main_domain_cdn: {
      baidu: [map_gss0_bdstatic, map_gss0_bdstatic, map_gss0_bdstatic],
      other: [map_api],
      webmap: ["webmap0.bdimg.com"]
    },
    map_click: "gsp0.baidu.com/80MWbzKh2wt3n2qy8IqW0jdnxx1xbK",
    vector_traffic: map_gss0_bdstatic
  };
  A.bZ = {
    TILE_BASE_URLS: [map_shangetu0, map_shangetu1, map_shangetu2, map_shangetu3],
    TILE_ONLINE_URLS: [map_maponline0, map_maponline1, map_maponline2, map_maponline3],
    TIlE_PERSPECT_URLS: [map_d0, map_d1, map_d2, map_d3],
    geolocControl: "loc.map.baidu.com",
    TILES_YUN_HOST: [map_g0, map_g1, map_g2, map_g3, map_g4],
    traffic: "itsmap2.baidu.com",
    message: "j.map.baidu.com",
    baidumap: "map.baidu.com",
    wuxian: "wuxian.baidu.com",
    pano: ["apisv0.bdimg.com", "apisv1.bdimg.com"],
    panoVerify: "api.map.baidu.com",
    main_domain_nocdn: {baidu: map_api},
    main_domain_cdn: {
      baidu: [map_api0, map_api1, map_api2],
      webmap: [map_webmap0]
    },
    map_click: "mapclick.map.baidu.com",
    vector_traffic: "maponline0.bdimg.com"
  };
  A.w1 = {
    "0": {proto: "http://", domain: A.bZ},
    1: {proto: "https://", domain: A.wM},
    2: {proto: "https://", domain: A.wM}
  };
  window.BMAP_PROTOCOL && "https" === window.BMAP_PROTOCOL && (window.HOST_TYPE = 2);
  A.Su = window.HOST_TYPE || "0";
  A.url = A.w1[A.Su];
  A.Zp = A.url.proto + A.url.domain.baidumap + "/";
  A.cd = A.url.proto + ("2" == A.Su ? A.url.domain.main_domain_nocdn.other : A.url.domain.main_domain_nocdn.baidu) + "/";
  A.pa = A.url.proto + ("2" == A.Su ? A.url.domain.main_domain_cdn.other[0] : A.url.domain.main_domain_nocdn.baidu) + "/";
  A.pj = A.url.proto + A.url.domain.main_domain_cdn.webmap[0] + "/";
  A.PN = A.url.proto + A.url.domain.panoVerify + "/";
  A.xh = function (a, b) {
    var c, e, b = b || "";
    switch (a) {
      case "main_domain_nocdn":
        c = A.cd + b;
        break;
      case "main_domain_cdn":
        c = A.pa + b;
        break;
      default:
        e = A.url.domain[a], "[object Array]" == Object.prototype.toString.call(e) ? (c = [], x.oc.Rb(e, function (a, e) {
          c[e] = A.url.proto + a + "/" + b
        })) : c = A.url.proto + A.url.domain[a] + "/" + b
    }
    return c
  };

  function Bb(a) {
    var b = {duration: 1E3, Nc: 30, pp: 0, fc: Cb.aN, du: ba()};
    this.hg = [];
    if (a) for (var c in a) b[c] = a[c];
    this.m = b;
    if (bb(b.pp)) {
      var e = this;
      setTimeout(function () {
        e.start()
      }, b.pp)
    } else b.pp != Db && this.start()
  }

  var Db = "INFINITE";
  Bb.prototype.start = function () {
    this.lv = ib();
    this.jA = this.lv + this.m.duration;
    Eb(this)
  };
  Bb.prototype.add = function (a) {
    this.hg.push(a)
  };

  function Eb(a) {
    var b = ib();
    b >= a.jA ? (cb(a.m.Ba) && a.m.Ba(a.m.fc(1)), cb(a.m.finish) && a.m.finish(), 0 < a.hg.length && (b = a.hg[0], b.hg = [].concat(a.hg.slice(1)), b.start())) : (a.Ry = a.m.fc((b - a.lv) / a.m.duration), cb(a.m.Ba) && a.m.Ba(a.Ry), a.aG || (a.rs = setTimeout(function () {
      Eb(a)
    }, 1E3 / a.m.Nc)))
  }

  Bb.prototype.stop = function (a) {
    this.aG = q;
    for (var b = 0; b < this.hg.length; b++) this.hg[b].stop(), this.hg[b] = s;
    this.hg.length = 0;
    this.rs && (clearTimeout(this.rs), this.rs = s);
    this.m.du(this.Ry);
    a && (this.jA = this.lv, Eb(this))
  };
  Bb.prototype.cancel = ia(1);
  var Cb = {
    aN: function (a) {
      return a
    }, reverse: function (a) {
      return 1 - a
    }, mD: function (a) {
      return a * a
    }, kD: function (a) {
      return Math.pow(a, 3)
    }, bt: function (a) {
      return -(a * (a - 2))
    }, mL: function (a) {
      return Math.pow(a - 1, 3) + 1
    }, lD: function (a) {
      return 0.5 > a ? 2 * a * a : -2 * (a - 2) * a - 1
    }, c4: function (a) {
      return 0.5 > a ? 4 * Math.pow(a, 3) : 4 * Math.pow(a - 1, 3) + 1
    }, d4: function (a) {
      return (1 - Math.cos(Math.PI * a)) / 2
    }
  };
  Cb["ease-in"] = Cb.mD;
  Cb["ease-out"] = Cb.bt;
  var J = {
    yG: 34,
    zG: 21,
    AG: new M(21, 32),
    JP: new M(10, 32),
    IP: new M(24, 36),
    HP: new M(12, 36),
    wG: new M(13, 1),
    ta: A.pa + "images/",
    B5: "http://mapapip0.bdimg.com/images/",
    xG: A.pa + "images/markers_new.png",
    FP: 24,
    GP: 73,
    AC: {
      "\u5317\u4eac": {Gy: "bj", o: new L(116.403874, 39.914889)},
      "\u4e0a\u6d77": {Gy: "sh", o: new L(121.487899, 31.249162)},
      "\u6df1\u5733": {Gy: "sz", o: new L(114.025974, 22.546054)},
      "\u5e7f\u5dde": {Gy: "gz", o: new L(113.30765, 23.120049)}
    },
    fontFamily: "arial,sans-serif"
  };
  x.ga.We ? (x.extend(J, {
    YK: "url(" + J.ta + "ruler.cur),crosshair",
    Ac: "-moz-grab",
    Ld: "-moz-grabbing"
  }), x.platform.OM && (J.fontFamily = "arial,simsun,sans-serif")) : x.ga.dx || x.ga.Qy ? x.extend(J, {
    YK: "url(" + J.ta + "ruler.cur) 2 6,crosshair",
    Ac: "url(" + J.ta + "openhand.cur) 8 8,default",
    Ld: "url(" + J.ta + "closedhand.cur) 8 8,move"
  }) : x.extend(J, {
    YK: "url(" + J.ta + "ruler.cur),crosshair",
    Ac: "url(" + J.ta + "openhand.cur),default",
    Ld: "url(" + J.ta + "closedhand.cur),move"
  });

  function Fb(a, b) {
    var c = a.style;
    c.left = b[0] + "px";
    c.top = b[1] + "px"
  }

  function Gb(a) {
    0 < x.ga.oa ? a.unselectable = "on" : a.style.MozUserSelect = "none"
  }

  function Hb(a) {
    return a && a.parentNode && 11 !== a.parentNode.nodeType
  }

  function Ib(a, b) {
    x.U.fy(a, "beforeEnd", b);
    return a.lastChild
  }

  function Jb(a) {
    for (var b = {
      left: 0,
      top: 0
    }; a && a.offsetParent;) b.left += a.offsetLeft, b.top += a.offsetTop, a = a.offsetParent;
    return b
  }

  function oa(a) {
    a = window.event || a;
    a.stopPropagation ? a.stopPropagation() : a.cancelBubble = q
  }

  function Kb(a) {
    a = window.event || a;
    a.preventDefault ? a.preventDefault() : a.returnValue = t;
    return t
  }

  function pa(a) {
    oa(a);
    return Kb(a)
  }

  function Lb() {
    var a = document.documentElement, b = document.body;
    return a && (a.scrollTop || a.scrollLeft) ? [a.scrollTop, a.scrollLeft] : b ? [b.scrollTop, b.scrollLeft] : [0, 0]
  }

  function Mb(a, b) {
    if (a && b) return Math.round(Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2)))
  }

  function Nb(a, b) {
    var c = [], b = b || function (a) {
      return a
    }, e;
    for (e in a) c.push(e + "=" + b(a[e]));
    return c.join("&")
  }

  function F(a, b, c) {
    var e = document.createElement(a);
    c && (e = document.createElementNS(c, a));
    return x.U.DF(e, b || {})
  }

  function $a(a) {
    if (a.currentStyle) return a.currentStyle;
    if (a.ownerDocument && a.ownerDocument.defaultView) return a.ownerDocument.defaultView.getComputedStyle(a, s)
  }

  function cb(a) {
    return "function" === typeof a
  }

  function bb(a) {
    return "number" === typeof a
  }

  function eb(a) {
    return "string" == typeof a
  }

  function Ob(a) {
    return "undefined" != typeof a
  }

  function Pb(a) {
    return "object" == typeof a
  }

  var Qb = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

  function Rb(a) {
    for (var b = "", c = 0; c < a.length; c++) {
      var e = a.charCodeAt(c) << 1, f = e = e.toString(2);
      8 > e.length && (f = "00000000" + e, f = f.substr(e.length, 8));
      b += f
    }
    a = 5 - b.length % 5;
    e = [];
    for (c = 0; c < a; c++) e[c] = "0";
    b = e.join("") + b;
    f = [];
    for (c = 0; c < b.length / 5; c++) e = b.substr(5 * c, 5), f.push(String.fromCharCode(parseInt(e, 2) + 50));
    return f.join("") + a.toString()
  }

  function Sb(a) {
    var b = "", c, e, f = "", g, i = "", k = 0;
    g = /[^A-Za-z0-9\+\/\=]/g;
    if (!a || g.exec(a)) return a;
    a = a.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    do c = Qb.indexOf(a.charAt(k++)), e = Qb.indexOf(a.charAt(k++)), g = Qb.indexOf(a.charAt(k++)), i = Qb.indexOf(a.charAt(k++)), c = c << 2 | e >> 4, e = (e & 15) << 4 | g >> 2, f = (g & 3) << 6 | i, b += String.fromCharCode(c), 64 != g && (b += String.fromCharCode(e)), 64 != i && (b += String.fromCharCode(f)); while (k < a.length);
    return b
  }

  var O = x.lang.av;

  function K() {
    return !(!x.platform.GE && !x.platform.xZ && !x.platform.Ej)
  }

  function Ya() {
    return !(!x.platform.OM && !x.platform.HM && !x.platform.EZ)
  }

  function ib() {
    return (new Date).getTime()
  }

  function Tb(a) {
    a = a.split("//");
    if (2 <= a.length) {
      var b = a[1].split("?");
      if (1 <= b.length) {
        var c = b[0].split("/"), e = 1;
        window.urlSplitBeginIndex && (e = window.urlSplitBeginIndex);
        var f = e, e = b.length - 1, g = "/", i = c.length;
        f || (f = 0);
        e || (e = i - 1);
        g || (g = "");
        if (f > i - 1 || e > i - 1) e = ""; else {
          for (i = ""; f <= e; f++) i = f === e ? i + c[f] : i + (c[f] + g);
          e = i
        }
        return {host: b[0], origin: a[0] + "//" + c[0], path: "/" + e}
      }
    }
    return s
  }

  function Ub() {
    var a = document.body.appendChild(F("div"));
    a.innerHTML = '<v:shape id="vml_tester1" adj="1" />';
    var b = a.firstChild;
    if (!b.style) return t;
    b.style.behavior = "url(#default#VML)";
    b = b ? "object" === typeof b.adj : q;
    a.parentNode.removeChild(a);
    return b
  }

  function Vb() {
    return !!document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#Shape", "1.1")
  }

  function Wb() {
    return !!F("canvas").getContext
  }

  function Xb(a) {
    return a * Math.PI / 180
  }

  A.KZ = function () {
    var a = q, b = q, c = q, e = q, f = 0, g = 0, i = 0, k = 0;
    return {
      nR: function () {
        f += 1;
        a && (a = t, setTimeout(function () {
          Ua(5054, {pic: f});
          a = q;
          f = 0
        }, 1E4))
      }, B2: function () {
        g += 1;
        b && (b = t, setTimeout(function () {
          Ua(5055, {move: g});
          b = q;
          g = 0
        }, 1E4))
      }, D2: function () {
        i += 1;
        c && (c = t, setTimeout(function () {
          Ua(5056, {zoom: i});
          c = q;
          i = 0
        }, 1E4))
      }, C2: function (a) {
        k += a;
        e && (e = t, setTimeout(function () {
          Ua(5057, {tile: k});
          e = q;
          k = 0
        }, 5E3))
      }
    }
  }();
  A.Nq = {OG: "#83a1ff", Pq: "#808080"};

  function Yb(a, b, c) {
    b.YE || (b.YE = [], b.handle = {});
    b.YE.push({filter: c, it: a});
    b.addEventListener || (b.addEventListener = function (a, c) {
      b.attachEvent("on" + a, c)
    });
    b.handle.click || (b.addEventListener("click", function (a) {
      for (var c = a.target || a.srcElement; c != b;) {
        Zb(b.YE, function (b, i) {
          RegExp(i.filter).test(c.getAttribute("filter")) && i.it.call(c, a, c.getAttribute("filter"))
        });
        c = c.parentNode
      }
    }, t), b.handle.click = q)
  }

  function Zb(a, b) {
    for (var c = 0, e = a.length; c < e; c++) b(c, a[c])
  }

  void function (a, b, c) {
    void function (a, b, c) {
      function i(a) {
        if (!a.op) {
          for (var c = q, e = [], g = a.P_, k = 0; g && k < g.length; k++) {
            var m = g[k], n = ca[m] = ca[m] || {};
            if (n.op || n == a) e.push(n.Uc); else {
              c = t;
              if (!n.MW && (m = (ha.get("alias") || {})[m] || m + ".js", !H[m])) {
                H[m] = q;
                var o = b.createElement("script"), p = b.getElementsByTagName("script")[0];
                o.async = q;
                o.src = m;
                p.parentNode.insertBefore(o, p)
              }
              n.nz = n.nz || {};
              n.nz[a.name] = a
            }
          }
          if (c) {
            a.op = q;
            a.TK && (a.Uc = a.TK.apply(a, e));
            for (var v in a.nz) i(a.nz[v])
          }
        }
      }

      function k(a) {
        return (a || new Date) - G
      }

      function m(a, b, c) {
        if (a) {
          "string" == typeof a && (c = b, b = a, a = I);
          try {
            a == I ? (N[b] = N[b] || [], N[b].unshift(c)) : a.addEventListener ? a.addEventListener(b, c, t) : a.attachEvent && a.attachEvent("on" + b, c)
          } catch (e) {
          }
        }
      }

      function n(a, b, c) {
        if (a) {
          "string" == typeof a && (c = b, b = a, a = I);
          try {
            if (a == I) {
              var e = N[b];
              if (e) for (var f = e.length; f--;) e[f] === c && e.splice(f, 1)
            } else a.removeEventListener ? a.removeEventListener(b, c, t) : a.detachEvent && a.detachEvent("on" + b, c)
          } catch (g) {
          }
        }
      }

      function o(a) {
        var b = N[a], c = 0;
        if (b) {
          for (var e = [], f = arguments, g = 1; g < f.length; g++) e.push(f[g]);
          for (g = b.length; g--;) b[g].apply(this, e) && c++;
          return c
        }
      }

      function p(a, b) {
        if (a && b) {
          var c = new Image(1, 1), e = [], f = "img_" + +new Date, g;
          for (g in b) b[g] && e.push(g + "=" + encodeURIComponent(b[g]));
          I[f] = c;
          c.onload = c.onerror = function () {
            I[f] = c = c.onload = c.onerror = s;
            delete I[f]
          };
          c.src = a + "?" + e.join("&")
        }
      }

      function v() {
        var a = arguments, b = a[0];
        if (this.SK || /^(on|un|set|get|create)$/.test(b)) {
          for (var b = y.prototype[b], c = [], e = 1, f = a.length; e < f; e++) c.push(a[e]);
          "function" == typeof b && b.apply(this, c)
        } else this.qK.push(a)
      }

      function w(a, b) {
        var c = {}, e;
        for (e in a) a.hasOwnProperty(e) && (c[e] = a[e]);
        for (e in b) b.hasOwnProperty(e) && (c[e] = b[e]);
        return c
      }

      function y(a) {
        this.name = a;
        this.gt = {protocolParameter: {postUrl: s, protocolParameter: s}};
        this.qK = [];
        this.alog = I
      }

      function z(a) {
        a = a || "default";
        if ("*" == a) {
          var a = [], b;
          for (b in T) a.push(T[b]);
          return a
        }
        (b = T[a]) || (b = T[a] = new y(a));
        return b
      }

      var C = c.alog;
      if (!C || !C.op) {
        var D = b.all && a.attachEvent, G = C && C.OE || +new Date,
          E = a.U5 || (+new Date).toString(36) + Math.random().toString(36).substr(2, 3), B = 0, H = {},
          I = function (a) {
            var b = arguments, c, e, f, g;
            if ("define" == a || "require" == a) {
              for (e = 1; e < b.length; e++) switch (typeof b[e]) {
                case "string":
                  c = b[e];
                  break;
                case "object":
                  f = b[e];
                  break;
                case "function":
                  g = b[e]
              }
              "require" == a && (c && !f && (f = [c]), c = s);
              c = !c ? "#" + B++ : c;
              e = ca[c] = ca[c] || {};
              e.op || (e.name = c, e.P_ = f, e.TK = g, "define" == a && (e.MW = q), i(e))
            } else "function" == typeof a ? a(I) : ("" + a).replace(/^(?:([\w$_]+)\.)?(\w+)$/, function (a, c, e) {
              b[0] = e;
              v.apply(I.kG(c), b)
            })
          }, N = {}, T = {}, ca = {s3: {name: "alog", op: q, Uc: I}};
        y.prototype.start = y.prototype.create = function (a) {
          if (!this.SK) {
            "object" == typeof a && this.set(a);
            this.SK = new Date;
            for (this.ht("create", this); a = this.qK.shift();) v.apply(this, a)
          }
        };
        y.prototype.send = function (a, b) {
          var c = w({ts: k().toString(36), t: a, sid: E}, this.gt);
          if ("object" == typeof b) c = w(c, b); else {
            var e = arguments;
            switch (a) {
              case "pageview":
                e[1] && (c.page = e[1]);
                e[2] && (c.title = e[2]);
                break;
              case "event":
                e[1] && (c.eventCategory = e[1]);
                e[2] && (c.eventAction = e[2]);
                e[3] && (c.eventLabel = e[3]);
                e[4] && (c.eventValue = e[4]);
                break;
              case "timing":
                e[1] && (c.timingCategory = e[1]);
                e[2] && (c.timingVar = e[2]);
                e[3] && (c.timingValue = e[3]);
                e[4] && (c.timingLabel = e[4]);
                break;
              case "exception":
                e[1] && (c.exDescription = e[1]);
                e[2] && (c.exFatal = e[2]);
                break;
              default:
                return
            }
          }
          this.ht("send", c);
          var f;
          if (e = this.gt.protocolParameter) {
            var g = {};
            for (f in c) e[f] !== s && (g[e[f] || f] = c[f]);
            f = g
          } else f = c;
          p(this.gt.postUrl, f)
        };
        y.prototype.set = function (a, b) {
          if ("string" == typeof a) "protocolParameter" == a && (b = w({
            postUrl: s,
            protocolParameter: s
          }, b)), this.gt[a] = b; else if ("object" == typeof a) for (var c in a) this.set(c, a[c])
        };
        y.prototype.get = function (a, b) {
          var c = this.gt[a];
          "function" == typeof b && b(c);
          return c
        };
        y.prototype.ht = function (a, b) {
          return I.ht(this.name + "." + a, b)
        };
        y.prototype.V = function (a, b) {
          I.V(this.name + "." + a, b)
        };
        y.prototype.kd = function (a, b) {
          I.kd(this.name + "." + a, b)
        };
        I.name = "alog";
        I.Yb = E;
        I.op = q;
        I.timestamp = k;
        I.kd = n;
        I.V = m;
        I.ht = o;
        I.kG = z;
        I("init");
        var Z = y.prototype;
        S(Z, {start: Z.start, create: Z.create, send: Z.send, set: Z.set, get: Z.get, on: Z.V, un: Z.kd, fire: Z.ht});
        var ha = z();
        ha.set("protocolParameter", {r3: s});
        if (C) {
          Z = [].concat(C.zb || [], C.ku || []);
          C.zb = C.ku = s;
          for (var va in I) I.hasOwnProperty(va) && (C[va] = I[va]);
          I.zb = I.ku = {
            push: function (a) {
              I.apply(I, a)
            }
          };
          for (C = 0; C < Z.length; C++) I.apply(I, Z[C])
        }
        c.alog = I;
        D && m(b, "mouseup", function (a) {
          a = a.target || a.srcElement;
          1 == a.nodeType && /^ajavascript:/i.test(a.tagName + a.href)
        });
        var Ha = t;
        a.onerror = function (a, b, e, f) {
          var i = q;
          !b && /^script error/i.test(a) && (Ha ? i = t : Ha = q);
          i && c.alog("exception.send", "exception", {$t: a, NE: b, Wt: e, Ag: f});
          return t
        };
        c.alog("exception.on", "catch", function (a) {
          c.alog("exception.send", "exception", {$t: a.$t, NE: a.path, Wt: a.Wt, method: a.method, xL: "catch"})
        })
      }
    }(a, b, c);
    void function (a, b, c) {
      var i = "18_3";
      K() && (i = "18_4");
      var k = "http://static.tieba.baidu.com";
      "https:" === a.location.protocol && (k = "https://gsp0.baidu.com/5aAHeD3nKhI2p27j8IqW0jdnxx1xbK");
      var m = Math.random, k = k + "/tb/pms/img/st.gif", n = {Ih: "0.1"}, o = {Ih: "0.1"}, p = {Ih: "0.1"},
        v = {Ih: "0"};
      if (n && n.Ih && m() < n.Ih) {
        var w = c.alog.kG("monkey"), y, n = a.screen, z = b.referrer;
        w.set("ver", 5);
        w.set("pid", 241);
        n && w.set("px", n.width + "*" + n.height);
        w.set("ref", z);
        c.alog("monkey.on", "create", function () {
          y = c.alog.timestamp;
          w.set("protocolParameter", {reports: s})
        });
        c.alog("monkey.on", "send", function (a) {
          "pageview" == a.t && (a.cmd = "open");
          a.now && (a.ts = y(a.now).toString(36), a.now = "")
        });
        c.alog("monkey.create", {page: i, pid: "241", p: "18", dv: 6, postUrl: k, reports: {refer: 1}});
        c.alog("monkey.send", "pageview", {now: +new Date})
      }
      if (o && o.Ih && m() < o.Ih) {
        var C = t;
        a.onerror = function (a, b, e, f) {
          var i = q;
          !b && /^script error/i.test(a) && (C ? i = t : C = q);
          i && c.alog("exception.send", "exception", {$t: a, NE: b, Wt: e, Ag: f});
          return t
        };
        c.alog("exception.on", "catch", function (a) {
          c.alog("exception.send", "exception", {$t: a.$t, NE: a.path, Wt: a.Wt, method: a.method, xL: "catch"})
        });
        c.alog("exception.create", {postUrl: k, dv: 7, page: i, pid: "170", p: "18"})
      }
      p && (p.Ih && m() < p.Ih) && (c.alog("cus.on", "time", function (a) {
        var b = {}, e = t, f;
        if ("[object Object]" === a.toString()) {
          for (var i in a) "page" == i ? b.page = a[i] : (f = parseInt(a[i]), 0 < f && /^z_/.test(i) && (e = q, b[i] = f));
          e && c.alog("cus.send", "time", b)
        }
      }), c.alog("cus.on", "count", function (a) {
        var b = {}, e = t;
        "string" === typeof a && (a = [a]);
        if (a instanceof Array) for (var f = 0; f < a.length; f++) /^z_/.test(a[f]) ? (e = q, b[a[f]] = 1) : /^page:/.test(a[f]) && (b.page = a[f].substring(5));
        e && c.alog("cus.send", "count", b)
      }), c.alog("cus.create", {dv: 3, postUrl: k, page: i, p: "18"}));
      if (v && v.Ih && m() < v.Ih) {
        var D = ["Moz", "O", "ms", "Webkit"], G = ["-webkit-", "-moz-", "-o-", "-ms-"], E = function () {
          return typeof b.createElement !== "function" ? b.createElement(arguments[0]) : b.createElement.apply(b, arguments)
        }, B = E("dpFeatureTest").style, H = function (a) {
          return I(a, l, l)
        }, I = function (a, b, c) {
          var e = a.charAt(0).toUpperCase() + a.slice(1), f = (a + " " + D.join(e + " ") + e).split(" ");
          if (typeof b === "string" || typeof b === "undefined") return N(f, b);
          f = (a + " " + D.join(e + " ") + e).split(" ");
          a:{
            var a = f, g;
            for (g in a) if (a[g] in b) {
              if (c === t) {
                b = a[g];
                break a
              }
              g = b[a[g]];
              b = typeof g === "function" ? fnBind(g, c || b) : g;
              break a
            }
            b = t
          }
          return b
        }, N = function (a, b) {
          var c, e, f;
          e = a.length;
          for (c = 0; c < e; c++) {
            f = a[c];
            ~("" + f).indexOf("-") && (f = T(f));
            if (B[f] !== l) return b == "pfx" ? f : q
          }
          return t
        }, T = function (a) {
          return a.replace(/([a-z])-([a-z])/g, function (a, b, c) {
            return b + c.toUpperCase()
          }).replace(/^-/, "")
        }, ca = function (a, b, c) {
          if (a.indexOf("@") === 0) return atRule(a);
          a.indexOf("-") != -1 && (a = T(a));
          return !b ? I(a, "pfx") : I(a, b, c)
        }, Z = function () {
          var a = E("canvas");
          return !(!a.getContext || !a.getContext("2d"))
        }, ha = function () {
          var a = E("div");
          return "draggable" in a || "ondragstart" in a && "ondrop" in a
        }, va = function () {
          try {
            localStorage.setItem("localStorage", "localStorage");
            localStorage.removeItem("localStorage");
            return q
          } catch (a) {
            return t
          }
        }, Ha = function () {
          return "content" in b.createElement("template")
        }, ua = function () {
          return "createShadowRoot" in b.createElement("a")
        }, Za = function () {
          return "registerElement" in b
        }, Ne = function () {
          return "import" in b.createElement("link")
        }, bd = function () {
          return "getItems" in b
        }, Fj = function () {
          return "EventSource" in window
        }, Oe = function (a, b) {
          var c = new Image;
          c.onload = function () {
            b(a, c.width > 0 && c.height > 0)
          };
          c.onerror = function () {
            b(a, t)
          };
          c.src = "data:image/webp;base64," + {
            X5: "UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",
            W5: "UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==",
            alpha: "UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==",
            Bk: "UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA"
          }[a]
        }, Pe = function (a, b) {
          return dc.ri["WebP-" + a] = b
        }, Gj = function () {
          return "openDatabase" in a
        }, Hj = function () {
          return "performance" in a && "timing" in a.performance
        }, Ij = function () {
          return "performance" in a && "mark" in a.performance
        }, Jj = function () {
          return !(!Array.prototype || !Array.prototype.every || !Array.prototype.filter || !Array.prototype.forEach || !Array.prototype.indexOf || !Array.prototype.lastIndexOf || !Array.prototype.map || !Array.prototype.some || !Array.prototype.reduce || !Array.prototype.reduceRight || !Array.isArray)
        }, Kj = function () {
          return "Promise" in a && "cast" in a.Qq && "resolve" in a.Qq && "reject" in a.Qq && "all" in a.Qq && "race" in a.Qq && function () {
            var b;
            new a.Qq(function (a) {
              b = a
            });
            return typeof b === "function"
          }()
        }, Lj = function () {
          var b = !!a.i2, c = a.XMLHttpRequest && "withCredentials" in new XMLHttpRequest;
          return !!a.p2 && b && c
        }, Mj = function () {
          return "geolocation" in navigator
        }, Nj = function () {
          var b = E("canvas"), c = "probablySupportsContext" in b ? "probablySupportsContext" : "supportsContext";
          return c in b ? b[c]("webgl") || b[c]("experimental-webgl") : "WebGLRenderingContext" in a
        }, Oj = function () {
          return !!b.createElementNS && !!b.createElementNS("http://www.w3.org/2000/svg", "svg").Q3
        }, Pj = function () {
          return !!a.y2
        }, Qj = function () {
          return "WebSocket" in a && a.v2.e2 === 2
        }, Rj = function () {
          return !!b.createElement("video").canPlayType
        }, Sj = function () {
          return !!b.createElement("audio").canPlayType
        }, Tj = function () {
          return !!(a.history && "pushState" in a.history)
        }, Uj = function () {
          return !(!a.g2 || !a.h2)
        }, Vj = function () {
          return "postMessage" in window
        }, Wj = function () {
          return !!a.webkitNotifications || "Notification" in a && "permission" in a.bQ && "requestPermission" in a.bQ
        }, Xj = function () {
          for (var b = ["webkit", "moz", "o", "ms"], c = a.requestAnimationFrame, f = 0; f < b.length && !c; ++f) c = a[b[f] + "RequestAnimationFrame"];
          return !!c
        }, Yj = function () {
          return "JSON" in a && "parse" in JSON && "stringify" in JSON
        }, Zj = function () {
          return !(!ca("exitFullscreen", b, t) && !ca("cancelFullScreen", b, t))
        }, $j = function () {
          return !!ca("Intl", a)
        }, ak = function () {
          return H("flexBasis")
        }, bk = function () {
          return !!H("perspective")
        }, ck = function () {
          return H("shapeOutside")
        }, dk = function () {
          var a = E("div");
          a.style.cssText = G.join("filter:blur(2px); ");
          return !!a.style.length && (b.documentMode === l || b.documentMode > 9)
        }, ek = function () {
          return "XMLHttpRequest" in a && "withCredentials" in new XMLHttpRequest
        }, fk = function () {
          return E("progress").max !== l
        }, gk = function () {
          return E("meter").max !== l
        }, hk = function () {
          return "sendBeacon" in navigator
        }, ik = function () {
          return H("borderRadius")
        }, jk = function () {
          return H("boxShadow")
        }, kk = function () {
          var a = E("div").style;
          a.cssText = G.join("opacity:.55;");
          return /^0.55$/.test(a.opacity)
        }, lk = function () {
          return N(["textShadow"], l)
        }, mk = function () {
          return H("animationName")
        }, nk = function () {
          return H("transition")
        }, ok = function () {
          return navigator.userAgent.indexOf("Android 2.") === -1 && H("transform")
        }, dc = {
          ri: {}, sa: function (a, b, c) {
            this.ri[a] = b.apply(this, [].slice.call(arguments, 2))
          }, Jd: function (a, b) {
            a.apply(this, [].slice.call(arguments, 1))
          }, U_: function () {
            this.sa("bdrs", ik);
            this.sa("bxsd", jk);
            this.sa("opat", kk);
            this.sa("txsd", lk);
            this.sa("anim", mk);
            this.sa("trsi", nk);
            this.sa("trfm", ok);
            this.sa("flex", ak);
            this.sa("3dtr", bk);
            this.sa("shpe", ck);
            this.sa("fltr", dk);
            this.sa("cavs", Z);
            this.sa("dgdp", ha);
            this.sa("locs", va);
            this.sa("wctem", Ha);
            this.sa("wcsdd", ua);
            this.sa("wccse", Za);
            this.sa("wchti", Ne);
            this.Jd(Oe, "lossy", Pe);
            this.Jd(Oe, "lossless", Pe);
            this.Jd(Oe, "alpha", Pe);
            this.Jd(Oe, "animation", Pe);
            this.sa("wsql", Gj);
            this.sa("natm", Hj);
            this.sa("ustm", Ij);
            this.sa("arra", Jj);
            this.sa("prms", Kj);
            this.sa("xhr2", Lj);
            this.sa("wbgl", Nj);
            this.sa("geol", Mj);
            this.sa("svg", Oj);
            this.sa("work", Pj);
            this.sa("wbsk", Qj);
            this.sa("vido", Rj);
            this.sa("audo", Sj);
            this.sa("hsty", Tj);
            this.sa("file", Uj);
            this.sa("psmg", Vj);
            this.sa("wknf", Wj);
            this.sa("rqaf", Xj);
            this.sa("json", Yj);
            this.sa("flsc", Zj);
            this.sa("i18n", $j);
            this.sa("cors", ek);
            this.sa("prog", fk);
            this.sa("metr", gk);
            this.sa("becn", hk);
            this.sa("mcrd", bd);
            this.sa("esrc", Fj)
          }
        }, w = c.alog.kG("feature");
        w.V("commit", function () {
          dc.U_();
          var a = setInterval(function () {
            if ("WebP-lossy" in dc.ri && "WebP-lossless" in dc.ri && "WebP-alpha" in dc.ri && "WebP-animation" in dc.ri) {
              for (var b in dc.ri) dc.ri[b] = dc.ri[b] ? "y" : "n";
              w.send("feature", dc.ri);
              clearInterval(a)
            }
          }, 500)
        });
        c.alog("feature.create", {a4: 4, C6: k, page: i, zb: "18"});
        c.alog("feature.fire", "commit")
      }
    }(a, b, c)
  }(window, document, A);
  A.Vq = A.alog || ba();
  A.alog("cus.fire", "count", "z_loadscriptcount");
  "https:" === location.protocol && A.alog("cus.fire", "count", "z_httpscount");

  function $b(a) {
    var b = window.TILE_VERSION, c = "20190410";
    b && b.ditu && (b = b.ditu, b[a] && b[a].updateDate && (c = b[a].updateDate));
    return c
  }

  var ac = [72.6892532, 0.1939743381, 136.1168614, 54.392257],
    bc = [72.69566833, 0.1999420909, 136.1232863, 54.39791217], cc = 158,
    ec = [98.795985, 122.960792, 107.867379, 118.093451, 119.139658, 128.035888, 79.948212, 99.029524, 119.923388, 122.094977, 127.918527, 130.94789, 106.50606, 108.076783, 119.8329, 126.382207, 111.803567, 119.324928, 100.749858, 102.227985, 99.860885, 100.788921, 97.529435, 98.841564, 99.100017, 99.90035, 122.917416, 123.774367, 123.728314, 125.507211, 123.736065, 124.767299, 125.488463, 126.410675, 125.484326, 126.07764, 130.830784, 133.620042, 127.912178, 128.668957, 128.658937, 129.638599, 132.894179, 134.119086, 117.379378, 119.244569, 116.086736, 117.431212, 114.420233, 116.137458, 116.492775, 119.605527, 110.579401, 111.86488, 74.468228, 80.001908, 82.867432, 91.353788, 85.721075, 98.976964, 127.664757, 129.546833, 129.476893, 130.22449, 133.730358, 134.745235, 134.381034, 135.1178, 130.868117, 131.34409, 115.513245, 117.544751, 115.779271, 116.748045, 108.536254, 110.614326, 121.365534, 124.626434, 126.165992, 127.347013, 91.281869, 95.611754, 79.879648, 82.945041, 76.413314, 78.345207, 78.275229, 80.002329, 83.956612, 85.734098, 85.510186, 89.356499, 97.997001, 98.948845, 106.653208, 108.610811, 111.400183, 111.824179, 111.592224, 111.817136, 116.00682, 117.024631, 116.258574, 116.689291, 119.436876, 119.922961, 120.659806, 121.395479, 120.349116, 120.676014, 124.59389, 125.787788, 126.221756, 126.788962, 95.572955, 102.046581, 95.583772, 96.165551, 95.564318, 97.806095, 91.30446, 93.356438, 93.330319, 94.698145, 89.349129, 90.548677, 82.268802, 82.892025, 78.335615, 80.032266, 76.625755, 78.361413, 73.498248, 74.490992, 74.846872, 76.488771, 91.563521, 94.878444, 88.768214, 89.244787, 83.247076, 83.974127, 82.29595, 83.256003, 81.885315, 83.26249, 80.760619, 81.472404, 86.470983, 88.276988, 102.207537, 104.234614, 112.164795, 116.833667, 108.965663, 113.032246, 111.166575, 117.983363],
    fc = [22.551183, 42.284787, 17.227969, 22.738314, 41.300981, 50.749638, 30.368087, 42.332701, 21.705055, 22.696452, 42.426047, 48.944674, 21.432184, 22.651387, 50.657409, 52.92296, 42.212192, 45.206905, 21.137031, 22.57186, 21.444502, 22.586566, 23.741571, 25.301472, 22.006806, 22.56637, 38.985114, 41.346531, 40.295617, 41.338581, 39.740021, 40.351012, 40.974644, 41.331562, 40.726852, 41.067192, 44.877158, 48.018285, 41.344597, 42.451798, 42.016305, 42.443235, 45.880906, 48.214001, 45.140027, 46.792775, 45.141083, 46.400433, 45.156418, 45.748281, 47.485889, 50.071879, 42.223667, 43.469487, 37.019867, 40.668675, 42.226823, 47.321605, 27.72944, 30.469853, 48.919002, 49.650614, 48.840188, 49.443166, 46.949801, 48.382798, 47.660603, 48.472692, 42.859946, 44.913298, 47.605896, 48.445914, 48.41698, 48.909667, 42.23507, 42.914193, 52.8281, 53.585952, 50.709311, 51.662219, 42.29968, 44.399225, 42.302746, 45.391958, 34.680866, 37.03377, 30.743515, 37.07228, 28.245649, 30.408935, 47.277693, 48.504255, 25.241528, 27.780726, 42.223363, 42.548418, 43.435888, 44.696952, 44.693193, 45.00187, 48.886267, 49.326755, 49.288642, 49.632304, 50.717486, 51.314369, 52.914204, 53.33964, 52.910094, 53.115926, 52.908382, 53.258095, 51.64533, 52.408305, 42.236825, 42.699126, 43.068466, 43.898632, 42.670403, 43.082219, 44.379045, 45.187742, 44.382336, 44.981379, 47.310362, 48.06019, 45.359099, 46.814439, 40.569751, 42.047741, 40.587956, 41.41263, 38.519192, 40.185033, 35.790476, 37.029005, 26.825605, 27.763896, 27.199658, 27.751649, 29.150192, 30.381073, 29.573886, 30.065162, 30.047775, 30.384089, 30.001277, 30.388525, 48.494118, 49.173841, 22.398528, 22.601198, 7.441114, 11.505968, 3.767491, 9.005209, 12.642067, 17.410886],
    gc = 95,
    hc = [110.3961374, 105.0743788, 96.8991824, 95.61810411, 93.82412598, 91.3892353, 91.38931858, 89.08325955, 87.22469808, 86.26278402, 85.17353, 85.23741211, 82.86627441, 81.90481038, 79.59687147, 80.39829237, 79.93319363, 77.80279948, 75.2557704, 73.49357829, 73.1892532, 73.87758816, 74.4064738, 74.10215224, 75.46409695, 76.77739692, 78.28299615, 78.15499485, 78.37920654, 78.89145345, 79.69282199, 81.19938178, 81.80830295, 83.89093424, 85.94149523, 87.86447266, 89.03414958, 90.05918132, 91.10026937, 92.15733832, 93.74361735, 95.82597331, 97.95655545, 97.12363037, 98.2129739, 99.2068571, 101.6587874, 102.5239084, 102.2356106, 105.0249238, 106.0992342, 107.8617093, 111.6439372, 109.591869, 112.284586, 117.7961157, 118.9495128, 114.2076584, 118.693565, 123.1475225, 122.730705, 120.9361393, 123.4207441, 122.3787782, 122.1385425, 121.5904281, 121.1773763, 120.6805404, 120.2483355, 122.795807, 122.8759077, 121.3060262, 122.1392177, 123.7418799, 126.4177599, 128.5647409, 129.7194884, 131.2259136, 131.9950494, 133.6289931, 135.6168614, 131.3875545, 130.8743365, 128.6303223, 126.0997773, 124.4015375, 122.22161, 119.6586483, 119.7866827, 118.5685878, 116.5177976, 114.819101, 119.0812964, 116.453265, 111.7431171],
    ic = [43.2190351, 42.38053385, 43.17417589, 44.42226915, 45.09863634, 45.56708116, 47.33599718, 48.68832709, 49.62448486, 48.9482175, 48.4800472, 47.33564399, 47.43948676, 46.03452067, 45.20221788, 43.34563043, 42.32965739, 41.39690972, 40.82972331, 39.95567654, 39.25892877, 38.36098768, 38.05441569, 37.16878445, 36.38899414, 35.36126817, 34.30953451, 32.58503879, 31.56975694, 30.77800266, 30.43559814, 29.7744892, 30.0931977, 28.71103299, 27.70739665, 27.5775472, 27.01096137, 27.77857883, 27.50707954, 26.50328315, 26.70387804, 27.95548557, 27.29428901, 23.64685493, 23.62310601, 21.67493381, 20.77751465, 21.32070991, 22.1824113, 22.31232964, 22.51316054, 16.80037679, 13.19749864, 0.6939743381, 1.541660428, 10.50208252, 15.58926975, 17.89090007, 19.94928467, 22.18490153, 25.37285292, 25.61456434, 30.62532552, 31.08099284, 31.89238173, 32.50092692, 32.80325765, 34.25546956, 35.15486138, 36.90170139, 37.8348272, 37.941604, 38.6480797, 38.96797201, 40.98146918, 41.25573296, 42.07218153, 42.49132813, 44.65259766, 44.69330702, 48.62286865, 48.09383952, 49.19628499, 50.03402317, 53.27678901, 53.62976345, 53.89420546, 52.98933322, 52.01872884, 50.23210259, 50.18807048, 47.49769857, 47.34362712, 46.50502143, 45.24770128],
    jc = [98.7895, 122.954182, 107.860913, 118.087007, 119.133165, 128.029533, 79.941749, 99.023087, 119.916883, 122.08841, 127.912143, 130.941471, 106.499502, 108.070244, 119.826245, 126.375818, 111.797006, 119.318387, 100.743285, 102.221517, 99.854448, 100.782445, 97.522928, 98.835028, 99.093518, 99.893783, 122.910927, 123.767769, 123.721954, 125.50077, 123.729657, 124.760724, 125.481902, 126.404079, 125.477737, 126.071019, 130.824331, 133.613395, 127.905767, 128.662524, 128.652527, 129.6321, 132.887552, 134.11249, 117.37297, 119.237999, 116.080154, 117.424589, 114.413586, 116.130948, 116.486264, 119.598927, 110.5728, 111.858437, 74.465162, 79.995337, 82.860821, 91.347291, 85.716024, 98.970481, 127.658331, 129.540202, 129.470528, 130.21808, 133.723748, 134.738785, 134.374555, 135.111443, 130.861475, 131.337438, 115.506627, 117.538123, 115.772783, 116.741632, 108.529656, 110.60782, 121.358945, 124.619773, 126.159424, 127.340582, 91.275275, 95.605228, 79.874427, 82.938601, 76.413314, 78.338763, 78.275229, 79.995765, 83.956612, 85.727511, 85.503554, 89.349858, 97.990418, 98.942257, 106.646704, 108.604437, 111.393667, 111.817723, 111.585811, 111.810645, 116.000232, 117.018216, 116.252108, 116.682705, 119.430384, 119.916417, 120.653168, 121.38883, 120.342727, 120.669383, 124.587426, 125.781376, 126.215282, 126.782323, 95.566367, 102.040026, 95.577158, 96.159009, 95.557772, 97.799728, 91.298032, 93.350057, 93.323794, 94.691771, 89.342471, 90.542019, 82.264229, 82.885485, 78.335615, 80.025844, 76.623947, 78.355027, 73.495149, 74.484473, 74.846872, 76.482208, 91.560117, 94.871859, 88.761692, 89.23822, 83.240549, 83.967602, 82.292367, 83.2495, 81.878825, 83.256003, 80.75421, 81.465955, 86.465421, 88.270356, 102.201019, 104.228033, 112.158282, 116.827153, 108.965663, 113.025767, 111.166575, 117.97687],
    kc = [22.545421, 42.279053, 17.226272, 22.731982, 41.294917, 50.743316, 30.361986, 42.326603, 21.699185, 22.690751, 42.419757, 48.938435, 21.426505, 22.64567, 50.651745, 52.916705, 42.20641, 45.201064, 21.131326, 22.565685, 21.438288, 22.580379, 23.735785, 25.295582, 22.001087, 22.560315, 38.979333, 41.340757, 40.28938, 41.332289, 39.734164, 40.344718, 40.968803, 41.325813, 40.721073, 41.061503, 44.871533, 48.012179, 41.338366, 42.445601, 42.010343, 42.436934, 45.875217, 48.208327, 45.134237, 46.786509, 45.135376, 46.394665, 45.150734, 45.742257, 47.480099, 50.065931, 42.217982, 43.46329, 37.014057, 40.662848, 42.221079, 47.315558, 27.723432, 30.46385, 48.913298, 49.644555, 48.83396, 49.436824, 46.944059, 48.376613, 47.654503, 48.466331, 42.854333, 44.907682, 47.600253, 48.440245, 48.410926, 48.903468, 42.229292, 42.908294, 52.822466, 53.58012, 50.703491, 51.656037, 42.29378, 44.393379, 42.296912, 45.385809, 34.679282, 37.027699, 30.740622, 37.066377, 28.241967, 30.403134, 47.271949, 48.49848, 25.235818, 27.774976, 42.217425, 42.542102, 43.429763, 44.691016, 44.687044, 44.995758, 48.880431, 49.320551, 49.282865, 49.626267, 50.711607, 51.308382, 52.908547, 53.333963, 52.904419, 53.109706, 52.902338, 53.251938, 51.639701, 52.402205, 42.231045, 42.693581, 43.062756, 43.892771, 42.664519, 43.075927, 44.372942, 45.1815, 44.376327, 44.975476, 47.304623, 48.054453, 45.353174, 46.808493, 40.563653, 42.041556, 40.582164, 41.4064, 38.51618, 40.179105, 35.789745, 37.023144, 26.825402, 27.757641, 27.193806, 27.745766, 29.144229, 30.375186, 29.567889, 30.059102, 30.041938, 30.378006, 29.995047, 30.382338, 48.48834, 49.169021, 22.392816, 22.595333, 7.439914, 11.500161, 3.766676, 9.000793, 12.640512, 17.406563],
    lc = 3E3, mc = 2.0E-5, nc = 3.0E-6, oc = 0.0174532925194, pc = 0.0065, qc = 0.0060, rc = 4E4, sc = 0, tc = 3,
    uc = 1.0E-10, vc = 6370996.81, wc = 1E8;

  function xc(a, b, c) {
    for (var e = cc, f = 0; f < e; f += 2) if (a.lng >= b[f] && a.lng <= b[f + 1] && a.lat >= c[f] && a.lat <= c[f + 1]) return q;
    return t
  }

  function yc(a) {
    var b = a.lng, c = a.lat, a = Math.sqrt(b * b + c * c) + Math.sin(c * lc * oc) * mc,
      b = Math.atan2(c, b) + Math.cos(b * lc * oc) * nc;
    return {lng: a * Math.cos(b) + pc, lat: a * Math.sin(b) + qc}
  }

  function zc(a) {
    var b = Ac, c = {}, e = a.lng, f = a.lat, g = 1, i = a.lng, k = a.lat, m = e - g, n = 0, o = f + g, p = 0,
      v = e - g, w = 0, y = f - g, z = 0, C = e + g, D = 0, G = f - g, E = 0, B = e + g, H = 0, I = f + g, N = 0,
      o = m = 0, o = Bc(b, e, f), m = o.lng, o = o.lat;
    if (1.0E-6 >= Cc(m, o, i, k)) return c.lng = e, c.lat = f, c;
    for (; ;) {
      m = e - g;
      o = f + g;
      v = e - g;
      y = f - g;
      C = e + g;
      G = f - g;
      B = e + g;
      I = f + g;
      e = Bc(b, m, o);
      n = e.lng;
      p = e.lat;
      e = Bc(b, v, y);
      w = e.lng;
      z = e.lat;
      e = Bc(b, C, G);
      D = e.lng;
      E = e.lat;
      e = Bc(b, B, I);
      H = e.lng;
      N = e.lat;
      e = Cc(n, p, i, k);
      n = Cc(w, z, i, k);
      w = Cc(D, E, i, k);
      H = Cc(H, N, i, k);
      if (1.0E-6 > e) return c.lng = m, c.lat = o, c;
      if (1.0E-6 > n) return c.lng = v, c.lat = y, c;
      if (1.0E-6 > w) return c.lng = C, c.lat = G, c;
      if (1.0E-6 > H) return c.lng = B, c.lat = I, c;
      D = 1 / e;
      n = 1 / n;
      w = 1 / w;
      H = 1 / H;
      e = (m * D + v * n + C * w + B * H) / (D + n + w + H);
      f = (o * D + y * n + G * w + I * H) / (D + n + w + H);
      o = Bc(b, e, f);
      m = o.lng;
      o = o.lat;
      if (1.0E-6 >= Cc(m, o, i, k)) return c.lng = e, c.lat = f, c;
      g *= 0.6;
      if (1.0E-6 > g) {
        a:{
          c = (a.lng + 0.03 - (a.lng - 0.03)) / 1.0E-4 + 0.5;
          g = (a.lat + 0.03 - (a.lat - 0.03)) / 1.0E-4 + 0.5;
          i = a.lng * wc;
          k = a.lat * wc;
          y = 1.0E-4 * wc;
          m = i - y;
          o = i + y;
          v = k - y;
          C = k + y;
          D = n = w = H = l;
          B = n = y = G = w = H = 0;
          b(a);
          D = l;
          for (I = 0; I <= c; I++) {
            for (e = 0; e <= g; e++) if (D = b(l), H = l.lng * wc, w = l.lat * wc, n = D.lng * wc, D = D.lat * wc, !(n < m || D < v || n > o || D > C)) {
              H -= n;
              w -= D;
              n = Math.sqrt((i - n) * (i - n) + (k - D) * (k - D));
              if (1 > n) {
                c = {};
                c.lng = l.lng;
                c.lat = l.lat;
                break a
              }
              G += 1 * H / n;
              y += 1 * w / n;
              B += 1 / n
            }
            G /= B * wc;
            y /= B * wc
          }
          b = G * wc / wc;
          g = y * wc / wc;
          c = {};
          c.lng = a.lng + b;
          c.lat = a.lat + g
        }
        return c
      }
    }
  }

  function Bc(a, b, c) {
    a = a({lng: b, lat: c});
    b = {};
    b.lng = a.lng;
    b.lat = a.lat;
    return b
  }

  function Dc(a, b, c, e) {
    var f = arguments.length;
    this.Kg = {};
    this.Rg = {};
    0 !== f && 4 === f && this.normalize(a, b, c, e)
  }

  Dc.prototype.contains = function (a) {
    return a.lng > this.Kg.lng && a.lng < this.Rg.lng && a.lat > this.Kg.lat && a.lat < this.Rg.lat ? tc : Math.abs(a.lng - this.Kg.lng) < uc || Math.abs(a.lng - this.Rg.lng) < uc || Math.abs(a.lat - this.Kg.lat) < uc || Math.abs(a.y - this.Rg.lat) > uc ? 2 : sc
  };
  Dc.prototype.normalize = function (a, b, c, e) {
    a > c ? (this.Kg.lng = c, this.Rg.lng = a) : (this.Kg.lng = a, this.Rg.lng = c);
    b > e ? (this.Kg.lat = e, this.Rg.lat = b) : (this.Kg.lat = b, this.Rg.lat = e)
  };

  function Ec(a, b, c, e) {
    this.Du = {lng: a, lat: b};
    this.xx = {lng: c, lat: e};
    this.uy = new Dc(a, b, c, e)
  }

  function Fc(a, b) {
    var c = a.lat * oc, e = b.lat * oc, f = c - e, g = a.lng * oc - b.lng * oc;
    return 2 * Math.asin(Math.sqrt(Math.sin(f / 2) * Math.sin(f / 2) + Math.cos(c) * Math.cos(e) * Math.sin(g / 2) * Math.sin(g / 2))) * vc
  }

  function Cc(a, b, c, e) {
    return Math.sqrt((a - c) * (a - c) + (b - e) * (b - e))
  }

  function Gc(a, b, c) {
    return (b.lng - a.lng) * (c.lat - a.lat) - (c.lng - a.lng) * (b.lat - a.lat)
  }

  function Ac(a) {
    var b = {};
    if (a.lng < ac[0] - 0.4 || a.lat < ac[1] - 0.4 || a.lng > ac[2] + 0.4 || a.lat > ac[3] + 0.4) return b.lng = a.lng, b.lat = a.lat, b;
    if (xc(a, jc, kc)) return b = yc(a);
    for (var b = 0, c = rc, e = 0, f = new Dc, g = 0, e = 0; e < gc; ++e) ic[e] <= a.lat ? ic[(e + 1) % gc] > a.lat && 0 < Gc({
      lng: hc[e],
      lat: ic[e]
    }, {lng: hc[(e + 1) % gc], lat: ic[(e + 1) % gc]}, a) && ++g : ic[(e + 1) % gc] <= a.lat && 0 > Gc({
      lng: hc[e],
      lat: ic[e]
    }, {lng: hc[(e + 1) % gc], lat: ic[(e + 1) % gc]}, a) && --g;
    if ((0 === g ? sc : tc) === sc) {
      for (g = 0; g < gc; ++g) if (e = new Ec(hc[g], ic[g], hc[(g + 1) % gc], ic[(g + 1) % gc]), f.Kg.lng = e.uy.Kg.lng - 0.5, f.Kg.lat = e.uy.Kg.lat - 0.5, f.Rg.lng = e.uy.Rg.lng + 0.5, f.Rg.lat = e.uy.Rg.lat + 0.5, f.contains(a) !== sc) {
        var i;
        var k = e.Du.lng, m = e.Du.lat, n = e.xx.lng, o = e.xx.lat;
        i = o - m;
        var p = k - n;
        !(Math.abs(i - 0) > uc) && !(Math.abs(p - 0) > uc) ? i = e.Du : (k = n * m - k * o, m = p * a.lng - i * a.lat, n = i * i - p * p, i = {
          lng: (p * m - i * k) / n,
          lat: -(i * m + p * k) / n
        });
        p = 180;
        k = 90;
        m = -180;
        n = -90;
        n = e.Du;
        o = e.xx;
        p = n.lng < o.lng ? n.lng : o.lng;
        k = n.lat < o.lat ? n.lat : o.lat;
        m = n.lng < o.lng ? n.lng : o.lng;
        n = n.lat < o.lat ? n.lat : o.lat;
        i.lng <= m && i.lng >= p && i.lng <= n && i.lat >= k ? (e = a.lat * oc, p = a.lng * oc, k = i.lat * oc, i = i.lng * oc, m = Math.cos(e) * Math.cos(k), e = m * Math.cos(p) * Math.cos(i) + m * Math.sin(p) * Math.sin(i) + Math.sin(e) * Math.sin(k), -1 > e ? e = -1 : 1 < e && (e = 1), e = Math.acos(e) * vc) : (i = Fc(a, e.Du), e = Fc(a, e.xx), e = i < e ? i : e);
        e < c && (c = e)
      }
      c < rc && (b = (rc - c) / rc)
    } else b = 1;
    c = yc(a);
    return b = {lng: a.lng + (c.lng - a.lng) * b, lat: a.lat + (c.lat - a.lat) * b}
  }

  function Hc(a) {
    var b = {};
    if (a.lng < bc[0] - 0.4 || a.lat < bc[1] - 0.4 || a.lng > bc[2] + 0.4 || a.lat > bc[3] + 0.4) return b.lng = a.lng, b.lat = a.lat, b;
    if (xc(a, ec, fc)) {
      var b = a.lng - pc, c = a.lat - qc, a = Math.sqrt(b * b + c * c) - Math.sin(c * lc * oc) * mc,
        b = Math.atan2(c, b) - Math.cos(b * lc * oc) * nc;
      return b = {lng: a * Math.cos(b), lat: a * Math.sin(b)}
    }
    c = Ac(a);
    return a.lng === c.lng && a.lat === c.lng ? (b.lng = a.lng, b.lat = a.lat, b) : zc(a)
  }

  function ab(a, b) {
    if (3 === b && a instanceof P) {
      var c = Ac(a);
      return new L(c.lng, c.lat)
    }
    return a
  }

  function fb(a, b) {
    if (3 === b && a instanceof L) {
      var c = Hc(a);
      return new P(c.lng, c.lat)
    }
    return 5 === b && a instanceof L ? new P(a.lng, a.lat) : a
  };

  function sa(a, b) {
    if (b) {
      var c = (1E5 * Math.random()).toFixed(0);
      A._rd["_cbk" + c] = function (a) {
        a.result && a.result.error && 403 === a.result.error ? A.$p !== s && A.$p.update("\u672a\u83b7\u5f97\u767e\u5ea6\u5730\u56fe\u5546\u7528\u6388\u6743\uff0c\u53ef\u80fd\u5bfc\u81f4\u90e8\u5206\u5730\u56fe\u8bf7\u6c42\u5931\u8d25\uff0c\u8bf7\u5237\u65b0\u540e\u91cd\u8bd5\u3002") : (b && b(a), delete A._rd["_cbk" + c])
      };
      a += "&callback=BMap._rd._cbk" + c
    }
    var a = pb(a), e = F("script", {type: "text/javascript"});
    e.charset = "utf-8";
    e.src = a;
    e.addEventListener ? e.addEventListener("load", function (a) {
      a = a.target;
      a.parentNode.removeChild(a)
    }, t) : e.attachEvent && e.attachEvent("onreadystatechange", function () {
      var a = window.event.srcElement;
      a && ("loaded" == a.readyState || "complete" == a.readyState) && a.parentNode.removeChild(a)
    });
    setTimeout(function () {
      document.getElementsByTagName("head")[0].appendChild(e);
      e = s
    }, 1)
  }

  function Ic(a) {
    if (navigator.cookieEnabled) return (a = document.cookie.match(RegExp("(^| )" + a + "=([^;]*)(;|$)"))) ? unescape(a[2]) : -1;
    if (localStorage) return localStorage.getItem(a) ? localStorage.getItem(a) : -1;
    if (sessionStorage) return sessionStorage.getItem(a) ? localStorage.getItem(a) : -1
  }

  function pb(a) {
    var b = decodeURIComponent(a.substring(a.indexOf("?") + 1)), c = (new Date).getTime(),
      e = window.___abvk ? window.___abvk : Ic("SECKEY_ABVK"), f = Ic("BMAP_SECKEY"),
      a = a + "&v=3.0&seckey=" + encodeURIComponent(e + "," + f) + "&timeStamp=" + c;
    return a += Jc()(b + ("&v=3.0&seckey=" + e + "," + f + "&timeStamp=" + c))
  };var Kc = {
    map: "gnn03e",
    common: "fwqdpm",
    style: "h3qzxe",
    tile: "tjvlmr",
    groundoverlay: "esl5sz",
    pointcollection: "cj1cee",
    marker: "wdbq1a",
    symbol: "jye4du",
    canvablepath: "25141u",
    vmlcontext: "0qk0nb",
    markeranimation: "vpekaj",
    poly: "4etby4",
    draw: "kmpjgc",
    drawbysvg: "m3hong",
    drawbyvml: "vnvvua",
    drawbycanvas: "shouyd",
    infowindow: "uh3yt2",
    oppc: "xklhtu",
    opmb: "402rg5",
    menu: "iolozp",
    control: "a44vj0",
    navictrl: "1hjgvh",
    geoctrl: "u2tdmu",
    copyrightctrl: "jvx3bq",
    citylistcontrol: "qajsuy",
    scommon: "novzwi",
    local: "iubgp2",
    route: "k5oyzl",
    othersearch: "pa1hbq",
    mapclick: "fb3xkc",
    buslinesearch: "01uhlq",
    hotspot: "fyrxe5",
    autocomplete: "dyvyun",
    coordtrans: "30upjg",
    coordtransutils: "t0pvac",
    convertor: "pcnqkf",
    clayer: "abqc13",
    pservice: "tln2uw",
    pcommon: "l225ln",
    panorama: "tx1kwc",
    panoramaflash: "mskavv"
  };
  x.ez = function () {
    function a(a) {
      return e && !!c[b + a + "_" + Kc[a]]
    }

    var b = "BMap_", c = window.localStorage, e = "localStorage" in window && c !== s && c !== l;
    return {
      zZ: e, set: function (a, g) {
        if (e) {
          for (var i = b + a + "_", k = c.length, m; k--;) m = c.key(k), -1 < m.indexOf(i) && c.removeItem(m);
          try {
            c.setItem(b + a + "_" + Kc[a], g)
          } catch (n) {
            c.clear()
          }
        }
      }, get: function (f) {
        return e && a(f) ? c.getItem(b + f + "_" + Kc[f]) : t
      }, EK: a
    }
  }();

  function Wa() {
  }

  x.object.extend(Wa, {
    Tj: {PG: -1, qQ: 0, Jq: 1},
    ML: function () {
      var a = "canvablepath";
      if (!K() || !Wb()) Vb() || (Ub() ? a = "vmlcontext" : Wb());
      return {
        tile: ["style"],
        control: [],
        marker: ["symbol"],
        symbol: ["canvablepath", "common"],
        canvablepath: "canvablepath" === a ? [] : [a],
        vmlcontext: [],
        style: [],
        poly: ["marker", "drawbycanvas", "drawbysvg", "drawbyvml"],
        drawbysvg: ["draw"],
        drawbyvml: ["draw"],
        drawbycanvas: ["draw"],
        infowindow: ["common", "marker"],
        menu: [],
        oppc: [],
        opmb: [],
        scommon: [],
        local: ["scommon"],
        route: ["scommon"],
        othersearch: ["scommon"],
        autocomplete: ["scommon"],
        citylistcontrol: ["autocomplete"],
        mapclick: ["scommon"],
        buslinesearch: ["route"],
        hotspot: [],
        coordtransutils: ["coordtrans"],
        convertor: [],
        clayer: ["tile"],
        pservice: [],
        pcommon: ["style", "pservice"],
        panorama: ["pcommon"],
        panoramaflash: ["pcommon"]
      }
    },
    F6: {},
    GG: {CQ: A.pa + "getmodules?v=3.0", $U: 5E3},
    UC: t,
    Xd: {Sl: {}, Xn: [], xw: []},
    load: function (a, b, c) {
      var e = this.qb(a);
      if (e.Re == this.Tj.Jq) c && b(); else {
        if (e.Re == this.Tj.PG) {
          this.JK(a);
          this.$N(a);
          var f = this;
          f.UC == t && (f.UC = q, setTimeout(function () {
            for (var a = [], b = 0, c = f.Xd.Xn.length; b < c; b++) {
              var e = f.Xd.Xn[b], n = "";
              ka.ez.EK(e) ? n = ka.ez.get(e) : (n = "", a.push(e + "_" + Kc[e]));
              f.Xd.xw.push({pN: e, bF: n})
            }
            f.UC = t;
            f.Xd.Xn.length = 0;
            0 == a.length ? f.sL() : sa(f.GG.CQ + "&mod=" + a.join(","))
          }, 1));
          e.Re = this.Tj.qQ
        }
        e.pv.push(b)
      }
    },
    JK: function (a) {
      if (a && this.ML()[a]) for (var a = this.ML()[a], b = 0; b < a.length; b++) this.JK(a[b]), this.Xd.Sl[a[b]] || this.$N(a[b])
    },
    $N: function (a) {
      for (var b = 0; b < this.Xd.Xn.length; b++) if (this.Xd.Xn[b] == a) return;
      this.Xd.Xn.push(a)
    },
    T_: function (a, b) {
      var c = this.qb(a);
      try {
        eval(b)
      } catch (e) {
        return
      }
      c.Re = this.Tj.Jq;
      for (var f = 0, g = c.pv.length; f < g; f++) c.pv[f]();
      c.pv.length = 0
    },
    EK: function (a, b) {
      var c = this;
      c.timeout = setTimeout(function () {
        c.Xd.Sl[a].Re != c.Tj.Jq ? (c.remove(a), c.load(a, b)) : clearTimeout(c.timeout)
      }, c.GG.$U)
    },
    qb: function (a) {
      this.Xd.Sl[a] || (this.Xd.Sl[a] = {}, this.Xd.Sl[a].Re = this.Tj.PG, this.Xd.Sl[a].pv = []);
      return this.Xd.Sl[a]
    },
    remove: function (a) {
      delete this.qb(a)
    },
    $V: function (a, b) {
      for (var c = this.Xd.xw, e = q, f = 0, g = c.length; f < g; f++) "" == c[f].bF && (c[f].pN == a ? c[f].bF = b : e = t);
      e && this.sL()
    },
    sL: function () {
      for (var a = this.Xd.xw, b = 0, c = a.length; b < c; b++) this.T_(a[b].pN, a[b].bF);
      this.Xd.xw.length = 0
    }
  });

  function Q(a, b) {
    this.x = a || 0;
    this.y = b || 0;
    this.x = this.x;
    this.y = this.y
  }

  Q.prototype.Vb = function (a) {
    return a && a.x == this.x && a.y == this.y
  };

  function M(a, b) {
    this.width = a || 0;
    this.height = b || 0
  }

  M.prototype.Vb = function (a) {
    return a && this.width == a.width && this.height == a.height
  };

  function qb(a, b, c) {
    var e = new XMLHttpRequest;
    e.open("POST", a, q);
    e.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    e.timeout = 1E4;
    e.ontimeout = ba();
    e.onreadystatechange = function () {
      4 === this.readyState && 200 === this.status && c && c(e.responseText)
    };
    e.send(b)
  };(function (a) {
    function b(a, b) {
      var c = (a & 65535) + (b & 65535);
      return (a >> 16) + (b >> 16) + (c >> 16) << 16 | c & 65535
    }

    function c(a, c, e, f, g, i) {
      return b(b(b(c, a), b(f, i)) << g | b(b(c, a), b(f, i)) >>> 32 - g, e)
    }

    function e(a, b, e, f, g, i, k) {
      return c(b & e | ~b & f, a, b, g, i, k)
    }

    function f(a, b, e, f, g, i, k) {
      return c(b & f | e & ~f, a, b, g, i, k)
    }

    function g(a, b, e, f, g, i, k) {
      return c(e ^ (b | ~f), a, b, g, i, k)
    }

    function i(a, i) {
      a[i >> 5] |= 128 << i % 32;
      a[(i + 64 >>> 9 << 4) + 14] = i;
      var k, m, n, o, p, E = 1732584193, B = -271733879, H = -1732584194, I = 271733878;
      for (k = 0; k < a.length; k += 16) m = E, n = B, o = H, p = I, E = e(E, B, H, I, a[k], 7, -680876936), I = e(I, E, B, H, a[k + 1], 12, -389564586), H = e(H, I, E, B, a[k + 2], 17, 606105819), B = e(B, H, I, E, a[k + 3], 22, -1044525330), E = e(E, B, H, I, a[k + 4], 7, -176418897), I = e(I, E, B, H, a[k + 5], 12, 1200080426), H = e(H, I, E, B, a[k + 6], 17, -1473231341), B = e(B, H, I, E, a[k + 7], 22, -45705983), E = e(E, B, H, I, a[k + 8], 7, 1770035416), I = e(I, E, B, H, a[k + 9], 12, -1958414417), H = e(H, I, E, B, a[k + 10], 17, -42063), B = e(B, H, I, E, a[k + 11], 22, -1990404162), E = e(E, B, H, I, a[k + 12], 7, 1804603682), I = e(I, E, B, H, a[k + 13], 12, -40341101), H = e(H, I, E, B, a[k + 14], 17, -1502002290), B = e(B, H, I, E, a[k + 15], 22, 1236535329), E = f(E, B, H, I, a[k + 1], 5, -165796510), I = f(I, E, B, H, a[k + 6], 9, -1069501632), H = f(H, I, E, B, a[k + 11], 14, 643717713), B = f(B, H, I, E, a[k], 20, -373897302), E = f(E, B, H, I, a[k + 5], 5, -701558691), I = f(I, E, B, H, a[k + 10], 9, 38016083), H = f(H, I, E, B, a[k + 15], 14, -660478335), B = f(B, H, I, E, a[k + 4], 20, -405537848), E = f(E, B, H, I, a[k + 9], 5, 568446438), I = f(I, E, B, H, a[k + 14], 9, -1019803690), H = f(H, I, E, B, a[k + 3], 14, -187363961), B = f(B, H, I, E, a[k + 8], 20, 1163531501), E = f(E, B, H, I, a[k + 13], 5, -1444681467), I = f(I, E, B, H, a[k + 2], 9, -51403784), H = f(H, I, E, B, a[k + 7], 14, 1735328473), B = f(B, H, I, E, a[k + 12], 20, -1926607734), E = c(B ^ H ^ I, E, B, a[k + 5], 4, -378558), I = c(E ^ B ^ H, I, E, a[k + 8], 11, -2022574463), H = c(I ^ E ^ B, H, I, a[k + 11], 16, 1839030562), B = c(H ^ I ^ E, B, H, a[k + 14], 23, -35309556), E = c(B ^ H ^ I, E, B, a[k + 1], 4, -1530992060), I = c(E ^ B ^ H, I, E, a[k + 4], 11, 1272893353), H = c(I ^ E ^ B, H, I, a[k + 7], 16, -155497632), B = c(H ^ I ^ E, B, H, a[k + 10], 23, -1094730640), E = c(B ^ H ^ I, E, B, a[k + 13], 4, 681279174), I = c(E ^ B ^ H, I, E, a[k], 11, -358537222), H = c(I ^ E ^ B, H, I, a[k + 3], 16, -722521979), B = c(H ^ I ^ E, B, H, a[k + 6], 23, 76029189), E = c(B ^ H ^ I, E, B, a[k + 9], 4, -640364487), I = c(E ^ B ^ H, I, E, a[k + 12], 11, -421815835), H = c(I ^ E ^ B, H, I, a[k + 15], 16, 530742520), B = c(H ^ I ^ E, B, H, a[k + 2], 23, -995338651), E = g(E, B, H, I, a[k], 6, -198630844), I = g(I, E, B, H, a[k + 7], 10, 1126891415), H = g(H, I, E, B, a[k + 14], 15, -1416354905), B = g(B, H, I, E, a[k + 5], 21, -57434055), E = g(E, B, H, I, a[k + 12], 6, 1700485571), I = g(I, E, B, H, a[k + 3], 10, -1894986606), H = g(H, I, E, B, a[k + 10], 15, -1051523), B = g(B, H, I, E, a[k + 1], 21, -2054922799), E = g(E, B, H, I, a[k + 8], 6, 1873313359), I = g(I, E, B, H, a[k + 15], 10, -30611744), H = g(H, I, E, B, a[k + 6], 15, -1560198380), B = g(B, H, I, E, a[k + 13], 21, 1309151649), E = g(E, B, H, I, a[k + 4], 6, -145523070), I = g(I, E, B, H, a[k + 11], 10, -1120210379), H = g(H, I, E, B, a[k + 2], 15, 718787259), B = g(B, H, I, E, a[k + 9], 21, -343485551), E = b(E, m), B = b(B, n), H = b(H, o), I = b(I, p);
      return [E, B, H, I]
    }

    function k(a) {
      var b, c = "", e = 32 * a.length;
      for (b = 0; b < e; b += 8) c += String.fromCharCode(a[b >> 5] >>> b % 32 & 255);
      return c
    }

    function m(a) {
      var b, c = [];
      c[(a.length >> 2) - 1] = l;
      for (b = 0; b < c.length; b += 1) c[b] = 0;
      var e = 8 * a.length;
      for (b = 0; b < e; b += 8) c[b >> 5] |= (a.charCodeAt(b / 8) & 255) << b % 32;
      return c
    }

    function n(a) {
      var b = "", c, e;
      for (e = 0; e < a.length; e += 1) c = a.charCodeAt(e), b += "0123456789abcdef".charAt(c >>> 4 & 15) + "0123456789abcdef".charAt(c & 15);
      return b
    }

    function o(a, b) {
      var c = unescape(encodeURIComponent(a)), e = unescape(encodeURIComponent(b)), f = m(c), g = [], n = [];
      g[15] = n[15] = l;
      16 < f.length && (f = i(f, 8 * c.length));
      for (c = 0; 16 > c; c += 1) g[c] = f[c] ^ 909522486, n[c] = f[c] ^ 1549556828;
      e = i(g.concat(m(e)), 512 + 8 * e.length);
      return k(i(n.concat(e), 640))
    }

    function p(a, b, c) {
      return !b ? !c ? n(k(i(m(unescape(encodeURIComponent(a))), 8 * unescape(encodeURIComponent(a)).length))) : k(i(m(unescape(encodeURIComponent(a))), 8 * unescape(encodeURIComponent(a)).length)) : !c ? n(o(b, a)) : o(b, a)
    }

    "function" === typeof define && define.t3 ? define(function () {
      return p
    }) : "object" === typeof module && module.yX ? module.yX = p : a.md5 = p
  })(this);

  function gb() {
    function a() {
      if (1 === arguments.length && "object" === typeof arguments[0]) {
        var a = arguments[0] || {};
        for (key in a) if (!a[key] || !(g[key] && a[key] === g[key])) if (a[key] || 0 === a[key]) g[key] = a[key]
      }
      a = document.getElementById(g.Oj);
      a.parentNode.removeChild(a);
      m.Y3()
    }

    function b(a) {
      if (1 === arguments.length && "object" === typeof arguments[0]) {
        var b = arguments[0] || {};
        for (key in b) if (!b[key] || !(g[key] && b[key] === g[key])) if (b[key] || 0 === b[key]) g[key] = b[key]
      }
      (b = document.getElementById(g.Oj)) && b.parentNode && b.parentNode.removeChild(b);
      if (b = document.getElementById(g.zP)) {
        var c = b ? b : document.body, e = Math.max(c.scrollWidth, c.clientWidth),
          f = Math.max(c.scrollHeight, c.clientHeight), i = arguments[0] || {}, p = 0, E = 0;
        i.M1 || i.L1 ? c && (p = c.offsetTop || 0, E = c.offsetLeft || 0, g.ql += E, g.rl += p) : c && (p = c.offsetTop || 0, E = c.offsetLeft || 0);
        var B = document.getElementById(g.Oj), i = s;
        if (B) B.HO && (i = B.HO); else {
          B = document.createElement("div");
          B.id = g.Oj;
          B.setAttribute("style", "pointer-events: none !important; display: block !important");
          var i = "function" === typeof B.AV ? B.AV({mode: "open"}) : B, H = c.children,
            I = Math.floor(Math.random() * (H.length - 1));
          H[I] ? c.insertBefore(B, H[I]) : c.appendChild(B)
        }
        g.ol = parseInt((e - g.ql) / (g.Mn + g.Nn));
        B = parseInt((e - g.ql - g.Mn * g.ol) / g.ol);
        g.Nn = B ? g.Nn : B;
        g.pl = parseInt((f - g.rl) / (g.Ln + g.On));
        B = parseInt((f - g.rl - g.Ln * g.pl) / g.pl);
        g.On = B ? g.On : B;
        b ? (B = g.ql + g.Mn * g.ol + g.Nn * (g.ol - 1), H = g.rl + g.Ln * g.pl + g.On * (g.pl - 1)) : (B = E + g.ql + g.Mn * g.ol + g.Nn * (g.ol - 1), H = p + g.rl + g.Ln * g.pl + g.On * (g.pl - 1));
        for (var N, T = document.createDocumentFragment(), ca = 0; ca < g.pl; ca++) {
          N = b ? p + g.rl + (g.On + g.Ln) * ca : g.rl + (f - H) / 2 + (g.On + g.Ln) * ca;
          for (var Z = 0; Z < g.ol; Z++) {
            var I = b ? E + g.ql + (e - B) / 2 + (g.Mn + g.Nn) * Z : g.ql + (e - B) / 2 + (g.Mn + g.Nn) * Z,
              ha = document.createElement("div");
            ha.appendChild(document.createTextNode(g.AP));
            ha.id = g.oz + ca + Z;
            ha.style.webkitTransform = "rotate(-" + g.Vu + "deg)";
            ha.style.MozTransform = "rotate(-" + g.Vu + "deg)";
            ha.style.msTransform = "rotate(-" + g.Vu + "deg)";
            ha.style.o2 = "rotate(-" + g.Vu + "deg)";
            ha.style.transform = "rotate(-" + g.Vu + "deg)";
            ha.style.visibility = "";
            ha.style.position = "absolute";
            ha.style.left = I + "px";
            ha.style.top = N + "px";
            ha.style.overflow = "hidden";
            ha.style.zIndex = "9999999";
            ha.style.opacity = g.H1;
            ha.style.fontSize = g.K1;
            ha.style.fontFamily = g.J1;
            ha.style.color = g.I1;
            ha.style.textAlign = "center";
            ha.style.width = g.Mn + "px";
            ha.style.height = g.Ln + "px";
            ha.style.display = "block";
            ha.style["-ms-user-select"] = "none";
            T.appendChild(ha)
          }
        }
        i.appendChild(T);
        if ((a.cF === l ? g.cF : a.cF) && k) m.wN(c, n), m.wN(document.getElementById(g.Oj), n), m.wN(document.getElementById(g.Oj).HO, o)
      }
    }

    function c(a) {
      if (f) f = t; else {
        for (var c = t, e = 0, i = 0, k = 0; k < a.length; k++) {
          if (p && a[k].target && a[k].target.getAttribute) {
            var m = a[k].target.getAttribute("id");
            if (m && (m === g.Oj || 0 <= m.indexOf(g.oz))) {
              c = q;
              break
            }
          }
          for (var n = 0; n < a[k].tV.length; n++) if (m = a[k].tV[n], m = m.getAttribute("id"), p && m && (m === g.Oj || 0 <= m.indexOf(g.oz))) {
            e += 1;
            break
          }
          for (n = 0; n < a[k].M_.length; n++) if (m = a[k].M_[n], m = m.getAttribute("id"), p && m && (m === g.Oj || 0 <= m.indexOf(g.oz))) {
            i += 1;
            break
          }
        }
        if (c || !e && 0 < i) console.log("loading"), b(p)
      }
    }

    var e = {}, f = t, g = {
        Oj: "baidu_jsapi_watermark",
        oz: "baidu_jsapi_watermark_span",
        AP: "\u6d4b\u8bd5\u6c34\u5370",
        ql: 20,
        rl: 20,
        pl: 0,
        ol: 0,
        Nn: 50,
        On: 50,
        J1: "\u5fae\u8f6f\u96c5\u9ed1",
        I1: "black",
        K1: "18px",
        H1: 0.15,
        Mn: 200,
        Ln: 100,
        Vu: 15,
        M1: 0,
        L1: 0,
        zP: s,
        cF: q
      }, i = window.n2 || window.u2 || window.m2, k = i !== l, m = k ? new i(c) : s, n = {childList: q, attributes: q},
      o = {childList: q, attributes: q, subtree: q}, p;
    e.xE = function (a) {
      p = a;
      b(a);
      x.V(window, "resize", function () {
        b(a)
      })
    };
    e.load = function (a) {
      p = a;
      b(a)
    };
    e.remove = function () {
      f = q;
      a()
    };
    return e
  };
  for (var Lc = function (a, b) {
    function c(a) {
      return f(a, function (a) {
        return e(a)
      })
    }

    function e(a) {
      return g.axmim(a, "")[g.xiama][g.axmim(g.aeacm(m, "Char"), k)](a)
    }

    function f(a, b) {
      for (var c = "ccc"; c !== g.ieami;) switch (c) {
        case "mhx":
          for (c = 0; g.hlxed(c, i); c++) {
            var e = b(a[c]);
            f.push(e)
          }
          c = g.mlllh;
          break;
        case g.eedem:
          var f = [], c = "mhx";
          break;
        case g.mlllh:
          return f;
        case g.eaiha:
          var i = a.length, c = "iec"
      }
    }

    var g = {
      ieami: "laxx", hlxed: function (a, b) {
        return a < b
      }, mlllh: "dea", eedem: "iec", eaiha: "ccc", axmim: function (a, b) {
        return a + b
      }, xiama: "constructor", aeacm: function (a, b) {
        return a + b
      }, echid: function (a, b) {
        return a(b)
      }, amxxh: function (a, b) {
        return a + b
      }, meacx: function (a, b) {
        return a(b)
      }, ihadh: function (a, b, c) {
        return a(b, c)
      }, chxcd: function (a, b, c) {
        return a(b, c)
      }
    }, i, k, m, n = decodeURIComponent;
    i = "de";
    m = g.aeacm("fro", "m");
    k = g.amxxh("Co", i);
    var o = c.call(e, [39, 34, 37, 96, 60, 120, 97, 65, 98, 66, 99, 67, 100, 68, 101, 69, 102, 70, 103, 110, 109, 111, 112, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57]);
    i = f([28782, 27702, 26416, 25167, 24183], function (a) {
      return g.echid(n, a)
    });
    var p = c.call(i, [22354, 22749, 24415, 23346, 22257, 22688, 24306, 25174, 23595, 25547, 22984, 25690, 22212, 27547, 21594, 27210, 23090, 29193, 22394, 29368, 29532, 29459, 29530, 24146, 24500, 26352, 27441, 28788, 29370, 27673, 26925, 25249, 24430]),
      v = {};
    i = g.meacx(c, i);
    var w = RegExp(i.join("|"));
    for (i = 0; i < o.length; i++) v[p[i]] = o[i];
    b = g.ihadh(f, b.split(""), function (a) {
      return v[a] || a
    }).join("");
    return g.chxcd(f, b.split(w), function (a) {
      return g.echid(n, a)
    })
  }(this, "\u58a0\u735ci\u59c8l\u6730\u59c8\u735cl\u5ef2\u545a\u706e\u59c8\u59c8i\u56c4\u5ef2\u624f\u59c8\u545a\u59c8\u5ef2\u58a0\u5e77\u735c\u56c4i\u545ah\u5e77\u735c\u59c8\u5ef2\u545al\u6c36\u545ai\u735c\u5ef2\u58a0\u706eSJv\u6a4aY\u72bah\u6b31\u692dZ\u6b9bh\u72ba\u735aHS\u706e\u59c8\u735ci\u735c\u56c4\u706e\u545ah\u545a\u545a\u545a\u706e\u5ef2\u545ah\u5ef2\u545a\u6c36\u5f5f\u66f0\u6c19\u59c8\u5ef2ll\u5c2b\u5ef2\u59c8k\u5f5f\u6b31\u6b9b\u6730\u5ef2\u59c8\u58a0\u545al\u706e\u545ah\u58a0\u545al\u6c36\u5f5f\u6b31\u7209qt\u5f5f\u6b31\u6b9b\u735a\u5ef2\u72b8\u7313_\u545a\u72b8try\u5f5f\u66f0\u6c19v\u5f5f\u6b31\u6b9b\u6c36\u5ef2\u545a\u5ef2\u5ef2l\u706e\u5a32u\u72b8\u59c8ti\u7313\u72b8\u624f\u58a0\u5ef2\u59c8i\u545a\u706e\u56c4l\u56c4\u735c\u58a0\u6c36\u5f5f\u6a4a\u6c19\u5f5f\u62a1\u66f0\u5f5f\u6256\u62a1\u5f5f\u6a4a\u5f6e\u5f5f\u5f6e\u645a\u5f5f\u62a1\u5e52\u5f5f\u6a4a\u62a1\u5f5f\u6256\u6c19\u5f5f\u62a1\u5fb4\u5f5f\u6a4a\u692d\u5f5f\u5f6e\u7074\u5f5f\u63cb\u6b31\u5f5f\u6a4a\u62a1\u5f5f\u6256\u7209\u5f5f\u63cb\u692d\u5f5f\u6a4a\u72ba\u5f5f\u62a1\u72ba\u5f5f\u6256\u62a1\u5f5f\u6a4a\u6c19\u5f5f\u5f6e\u5f6e\u5f5f\u6256\u7209\u5f5f\u6a4a\u72ba\u5f5f\u5f6e\u645a\u5f5f\u63cb\u5e52\u5f5f\u6a4a\u72ba\u5f5f\u5f6e\u63cb\u5f5f\u63cb\u6a4a\u5f5f\u6a4a\u6c19\u5f5f\u5f6e\u645a\u5f5f\u62a1\u6b9b\u5f5f\u6a4a\u72ba\u5f5f\u62a1\u6256\u5f5f\u6256\u5fb4\u5f5f\u6a4a\u7074\u5f5f\u63cb\u6b9b\u5f5f\u63cb\u7209\u5f5f\u6a4a\u692d\u5f5f\u5f6e\u7074\u5f5f\u6256\u62a1\u5f5f\u6a4a\u6c19\u5f5f\u5f6e\u6b9b\u5f5f\u62a1\u6b31\u5f5f\u6a4a\u5f6e\u5f5f\u5f6e\u5f6e\u5f5f\u5f6e\u5e52\u6c36i\u545a\u545ah\u706ei\u58a0\u5ef2\u706e\u5ef2ih\u624f\u5ef2hl\u59c8\u624f\u58a0\u5ef2h\u6c36i\u735c\u545a\u6c36i\u56c4\u545aih\u6730\u545a\u545a\u735chi\u624f\u545a\u59c8\u58a0\u735c\u545a\u624f\u59c8\u59c8i\u5e77\u545arr\u7313r\u6c36\u59c8\u58a0ii\u735c\u6c36\u56c4hh\u545a\u5ef2\u706e\u5ef2\u59c8l\u58a0\u545a\u6730i\u59c8\u58a0h\u58a0\u6c36i\u545a\u5ef2\u735c\u5ef2\u5e77\u56c4\u56c4h\u58a0\u56c4\u624f\u545a\u5ef2\u545a\u58a0\u545a\u6730\u58a0\u735c\u545a\u5ef2\u545a\u6730\u58a0\u5ef2h\u545a\u59c8\u624fh\u5ef2l\u56c4\u735c\u6730ih\u545a\u59c8\u735c\u5e77\u5ef2i\u5ef2\u56c4\u59c8\u6c36\u5f5f\u6b31\u7209qt\u5f5f\u6b31\u6b9b\u735a\u5ef2\u72b8\u7313_\u59c8\u7313u\u72b8t\u5f5f\u66f0\u6c19v\u5f5f\u6b31\u6b9b\u6730\u5f5f\u66f0\u6c19\u5ef2k\u5f5f\u6b31\u6b9b\u624f\u5f5f\u66f0\u6c19\u59c8\u7313\u72b8su\u735c\u545a\u5f5f\u6b31\u6b9b\u5e77\u59c8\u5ef2il\u735c\u706e\u545a\u59c8\u56c4l\u5ef2\u706e\u5ef2l\u5ef2\u56c4l\u6c36\u56c4\u545a\u5ef2\u545a\u59c8\u6c36\u735c\u735c\u58a0\u5ef2\u56c4\u706eP\u6256NOR\u6256M\u6256\u706ei\u735c\u5ef2\u735ch\u6c36\u545a\u545ai\u59c8\u5ef2\u706e\u5ef2i\u5ef2\u545a\u545a\u6730ll\u545a\u5e77\u545a\u59c8\u56c4\u5ef2\u545a\u624f\u545ahi\u735c\u545a\u6c36\u56c4\u56c4\u545a\u706e\u5ef2i\u56c4\u706e\u5ef2\u59c8\u735c\u624f\u58a0\u545a\u545a\u6730il\u56c4\u5e77i\u59c8\u5ef2\u5e77ih\u545a\u5e77\u5ef2\u545ah\u6730\u56c4l\u735c\u6c36\u545a\u545a\u735c\u706eh\u59c8\u5ef2\u706e\u735cll\u706eh\u56c4i\u706ei\u59c8i\u624f\u735c\u59c8i\u58a0\u59c8\u706el\u58a0i\u735c\u56c4\u624f\u735c\u735c\u735ch\u56c4\u5e77\u5ef2i\u5ef2i\u545a\u624fl\u735c\u56c4\u6730\u735ch\u5ef2h\u56c4\u624f\u56c4\u59c8\u545a\u56c4\u58a0\u706e\u545al\u56c4\u59c8\u59c8\u5e77\u58a0\u735c\u58a0\u59c8\u545a\u6c36\u5ef2\u735c\u545a\u545a\u545a\u6730\u5ef2uth_k\u545ay\u5f5f\u6b31\u6b9b\u6730\u56c4\u58a0l\u59c8\u5ef2"), Mc = 188, Nc = ++Mc; --Nc;) Lc.push(Lc.shift());

  function U(a) {
    return Lc[a - 0]
  }

  var Qc = function (a) {
    for (var b = {
      cxiim: function (a, b, c) {
        return a(b, c)
      }, dhhea: U("0x0"), aclxe: function (a, b) {
        return a + b
      }, icxhx: function (a, b) {
        return a + b
      }, ieama: function (a, b) {
        return a + b
      }, ddhxd: "eac", eaexe: U("0x1"), ideih: U("0x2"), eemhi: U("0x3"), ecxme: U("0x4"), ehime: U("0x5")
    }, c = b[U("0x6")]; c !== b[U("0x7")];) switch (c) {
      case b[U("0x8")]:
        var e = 0, c = U("0x5");
        break;
      case U("0x9"):
        return function (a) {
          var c = {
            ecdla: U("0xa"), aladl: function (a, b) {
              return a(b)
            }, deaec: function (a, b) {
              return a !== b
            }, mmxad: function (a, c, e) {
              return b[U("0xb")](a, c, e)
            }, xmeae: b[U("0xc")], xahec: "dcx", haldm: function (a, c) {
              return b[U("0xd")](a, c)
            }, ihecm: function (a, c) {
              return b[U("0xe")](a, c)
            }, aiadc: function (a, c) {
              return b[U("0xf")](a, c)
            }, cailm: b[U("0x10")], aiaee: "lle", ecdae: b[U("0x11")]
          };
          e += a;
          f || (f = setTimeout(function () {
            for (var a = "dcx"; a !== c[U("0x12")];) switch (a) {
              case c[U("0x13")]:
                var b = c.haldm(c[U("0x14")](c[U("0x15")](c[U("0x16")](A.PN, U("0x17")), A.version) + U("0x18"), ra) + U("0x19"), e),
                  a = c[U("0x1a")];
                break;
              case U("0x1"):
                f = s;
                a = U("0x0");
                break;
              case c[U("0x1a")]:
                sa(b, function (a) {
                  var b = {
                    imamh: c[U("0x1b")], eeica: function (a, b) {
                      return c[U("0x1c")](a, b)
                    }
                  };
                  if (!a || a[c[U("0x1b")]] === l || c[U("0x1d")](a[U("0xa")], 0)) c[U("0x1e")](Oc, U("0x1f"), function (a) {
                    if (!a || a[b.imamh] === l || 0 !== a[b[U("0x20")]]) b[U("0x21")](Pc, U("0x1f"))
                  })
                });
                a = c[U("0x22")];
                break;
              case U("0x23"):
                e = 0, a = c[U("0x24")]
            }
          }, g))
        };
      case b[U("0x6")]:
        var f = s, c = U("0x4");
        break;
      case b[U("0x25")]:
        var g = a ? a : 5E3, c = "cci"
    }
  }();

  function Rc(a, b) {
    for (var c = {
      mcixc: function (a, b) {
        return a !== b
      },
      lximd: "xlle",
      mmmhd: U("0x26"),
      aiaie: U("0x27"),
      mhahd: U("0x28"),
      dcedx: U("0x29"),
      laaxh: U("0x2a"),
      eldcc: U("0x2b"),
      xmxce: function (a, b) {
        return a + b
      },
      leacx: function (a, b) {
        return a + b
      },
      ameee: function (a, b) {
        return a + b
      },
      dxlca: function (a, b) {
        return a + b
      },
      xmicl: U("0x2c"),
      cmlae: U("0x2d"),
      xeeee: U("0x2e"),
      cmimd: U("0x2f"),
      ccida: U("0x30"),
      eaexe: "lmd",
      cecax: U("0x31"),
      aheme: U("0x32"),
      mdieh: function (a, b) {
        return a(b)
      },
      mcael: U("0x33"),
      eimax: function (a, b) {
        return a(b)
      },
      eheee: function (a, b) {
        return a + b
      },
      xemai: function (a, b) {
        return a + b
      },
      aehae: function (a, b) {
        return a + b
      }
    }, e = U("0x26"); c[U("0x34")](e, c[U("0x35")]);) switch (e) {
      case c[U("0x36")]:
        var f, g, e = c[U("0x37")];
        break;
      case U("0x38"):
        g = b.Sp === t ? t : q;
        e = c[U("0x39")];
        break;
      case U("0x27"):
        e = !b ? c[U("0x3a")] : c.laaxh;
        break;
      case "hca":
        var i = Date.parse(new Date), e = U("0x2d");
        break;
      case c[U("0x3b")]:
        var k = c[U("0x3c")](c.leacx(c[U("0x3d")](c[U("0x3d")](U("0x3e"), c[U("0x3f")](m, n)) + "-" + i, "-"), ra), "-") + o,
          e = c[U("0x40")];
        break;
      case c[U("0x41")]:
        var m = i / 1E3, e = c.xeeee;
        break;
      case c.cmimd:
        var n = 1800, e = c[U("0x42")];
        break;
      case "ild":
        f = b.sW ? b.sW : 1;
        e = c[U("0x11")];
        break;
      case c.dcedx:
        f = 1;
        e = c[U("0x43")];
        break;
      case U("0x31"):
        g = q;
        e = c[U("0x39")];
        break;
      case c.aheme:
        return k;
      case "ihe":
        g && c[U("0x44")](Qc, f);
        e = "hdi";
        break;
      case c[U("0x45")]:
        var o = c[U("0x46")](md5, v), e = c[U("0x3b")];
        break;
      case U("0x28"):
        var p = U("0x47"), e = c[U("0x48")];
        break;
      case U("0x2e"):
        var v = c[U("0x49")](c.xemai(c.xemai(a + "-" + c[U("0x4a")](m, n) + "-" + i, "-"), ra), "-") + p,
          e = c[U("0x45")]
    }
  }

  function Oc(a, b) {
    var c = {
      acxel: function (a, b) {
        return a + b
      }, ehxel: function (a, b) {
        return a + b
      }, idmde: U("0x18"), aeaal: function (a, b) {
        return a === b
      }, axxae: function (a, b) {
        return a + b
      }, xacie: function (a, b) {
        return a + b
      }, dldmx: U("0x4b")
    };
    switch (a) {
      case "PANORAMA":
        var e = c[U("0x4c")](c[U("0x4c")](c[U("0x4d")](A.PN + U("0x4e"), A.version), c.idmde), ra);
        c[U("0x4f")](typeof b, U("0x50")) ? sa(e, b) : sa(c.axxae(c[U("0x51")](e, c[U("0x52")]), b))
    }
  }

  function Pc(a) {
    var b = {
      aadaa: function (a, b) {
        return a(b)
      }, eaexe: U("0x53")
    };
    switch (a) {
      case "PANORAMA":
        b.aadaa(alert, b[U("0x11")])
    }
  };

  function Jc() {
    function a(a) {
      return b[a - 0]
    }

    var b = function (a, b) {
      function f(a) {
        var b = {
          eexem: function (a, b) {
            return k.aaxeh(a, b)
          }
        };
        return i(a, function (a) {
          return b.eexem(g, a)
        })
      }

      function g(a) {
        return (a + "").constructor[k.aamcc(k.aamcc(o, "Char"), n)](a)
      }

      function i(a, b) {
        for (var c = k.aedal; k.ledia(c, k.hmmhe);) switch (c) {
          case "aed":
            var e = [], c = k.dmaai;
            break;
          case "ahd":
            for (c = 0; c < g; c++) {
              var f = b(a[c]);
              e.push(f)
            }
            c = "dxa";
            break;
          case "mee":
            var g = a.length, c = k.icihe;
            break;
          case k.ecaae:
            return e
        }
      }

      var k = {
        aedal: "mee", ledia: function (a, b) {
          return a !== b
        }, hmmhe: "iicx", dmaai: "ahd", icihe: "aed", ecaae: "dxa", aamcc: function (a, b) {
          return a + b
        }, aaxeh: function (a, b) {
          return a(b)
        }, hidxa: function (a, b) {
          return a(b)
        }, ahacc: function (a, b) {
          return a < b
        }, ddddm: function (a, b, c) {
          return a(b, c)
        }, aaaxe: function (a, b, c) {
          return a(b, c)
        }
      }, m, n, o, p = decodeURIComponent;
      m = "de";
      o = k.aamcc("fro", "m");
      n = k.aamcc("Co", m);
      var v = f.call(g, [39, 34, 37, 96, 60, 120, 97, 65, 98, 66, 99, 67, 100, 68, 101, 69, 102, 70, 103, 110, 109, 111, 112, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57]);
      m = i([28782, 27702, 26416, 25167, 24183], function (a) {
        return k.aaxeh(p, a)
      });
      var w = f.call(m, [22354, 22749, 24415, 23346, 22257, 22688, 24306, 25174, 23595, 25547, 22984, 25690, 22212, 27547, 21594, 27210, 23090, 29193, 22394, 29368, 29532, 29459, 29530, 24146, 24500, 26352, 27441, 28788, 29370, 27673, 26925, 25249, 24430]),
        y = {};
      m = k.hidxa(f, m);
      var z = RegExp(m.join("|"));
      for (m = 0; k.ahacc(m, v.length); m++) y[w[m]] = v[m];
      b = k.ddddm(i, b.split(""), function (a) {
        return y[a] || a
      }).join("");
      return k.aaaxe(i, b.split(z), function (a) {
        return p(a)
      })
    }(this, "\u5f5f\u66f0\u6c19si\u577a\u72b8\u5f5f\u6b31\u6b9b\u6730\u58a0lh\u6730\u5ef2i\u5ef2\u5e77H\u5e52\u6256\u5f6eP\u62a1P\u692dY\u6c19\u6256\u63cbQO\u5e52\u6730\u545a\u59c8\u545ai\u545a\u6730\u59c8\u5ef2h\u735ch\u5e77\u7209\u72baL\u66f0O\u6c19R\u6c19\u6256\u6b9b\u62a1\u5f6e\u5f6e\u5e52O\u5e77\u545a\u56c4iih\u624f\u58a0\u59c8\u58a0\u5ef2\u5ef2\u6c36l\u545a\u72b8\u577ath\u6c36l\u59c8i\u59c8\u545a\u624f\u545a\u58a0\u5ef2l\u59c8\u6730h\u58a0\u56c4\u735c\u56c4\u706e\u5ef2\u5ef2\u59c8\u56c4\u545a\u5e77l\u545a\u545a\u58a0\u706el\u545al\u5e77\u56c4\u735c\u59c8");
    (function (a, b) {
      for (var f = ++b; --f;) a.push(a.shift())
    })(b, 456);
    return function (b) {
      for (var e = {
        eceie: function (a, b) {
          return a !== b
        }, ilahl: a("0x0"), cahmh: a("0x1"), ediih: a("0x2"), xcxaa: function (a, b) {
          return a + b
        }, dmmam: a("0x3"), lcice: a("0x4"), exalc: function (a, b) {
          return a(b)
        }, hxdmd: a("0x5"), aacde: a("0x6")
      }, f = "lel"; e[a("0x7")](f, e.ilahl);) switch (f) {
        case e[a("0x8")]:
          var g = a("0x9"), f = a("0x5");
          break;
        case e[a("0xa")]:
          return e[a("0xb")](e.dmmam, i.substring(i[a("0xc")] - 12));
        case e[a("0xd")]:
          var i = e[a("0xe")](md5, md5(b + g) + k), f = "dmc";
          break;
        case e[a("0xf")]:
          var k = e[a("0x10")], f = a("0x4")
      }
    }
  };

  function ob(a, b) {
    a && (this.Ob = a, this.da = "spot" + ob.da++, b = b || {}, this.nh = b.text || "", this.aw = b.offsets ? b.offsets.slice(0) : [5, 5, 5, 5], this.ZB = b.userData || s, this.Wh = b.minZoom || s, this.Pf = b.maxZoom || s)
  }

  ob.da = 0;
  x.extend(ob.prototype, {
    za: function (a) {
      this.Wh == s && (this.Wh = a.M.kc);
      this.Pf == s && (this.Pf = a.M.qc)
    }, va: function (a) {
      if (a instanceof P || a instanceof L) this.Ob = a
    }, ma: u("Ob"), wu: da("nh"), kE: u("nh"), setUserData: da("ZB"), getUserData: u("ZB")
  });

  function Sc() {
    this.P = s;
    this.Pb = "control";
    this.Va = this.vK = q
  }

  x.lang.xa(Sc, x.lang.Ja, "Control");
  x.extend(Sc.prototype, {
    initialize: function (a) {
      this.P = a;
      if (this.R) return a.bb.appendChild(this.R), this.R
    }, Le: function (a) {
      !this.R && (this.initialize && cb(this.initialize)) && (this.R = this.initialize(a));
      this.m = this.m || {Qg: t};
      this.NB();
      this.Io();
      this.R && (this.R.Kr = this)
    }, NB: function () {
      var a = this.R;
      if (a) {
        var b = a.style;
        b.position = "absolute";
        b.zIndex = this.cr || "10";
        b.MozUserSelect = "none";
        b.WebkitTextSizeAdjust = "none";
        this.m.Qg || x.U.ib(a, "BMap_noprint");
        K() || x.V(a, "contextmenu", pa)
      }
    }, remove: function () {
      this.P = s;
      this.R && (this.R.parentNode && this.R.parentNode.removeChild(this.R), this.R = this.R.Kr = s)
    }, Ha: function () {
      this.R = Ib(this.P.bb, "<div unselectable='on'></div>");
      this.Va == t && x.U.aa(this.R);
      return this.R
    }, Io: function () {
      this.wc(this.m.anchor)
    }, wc: function (a) {
      if (this.u3 || !bb(a) || isNaN(a) || a < Tc || 3 < a) a = this.defaultAnchor;
      this.m = this.m || {Qg: t};
      this.m.Ga = this.m.Ga || this.defaultOffset;
      var b = this.m.anchor;
      this.m.anchor = a;
      if (this.R) {
        var c = this.R, e = this.m.Ga.width, f = this.m.Ga.height;
        c.style.left = c.style.top = c.style.right = c.style.bottom = "auto";
        switch (a) {
          case Tc:
            c.style.top = f + "px";
            c.style.left = e + "px";
            break;
          case Uc:
            c.style.top = f + "px";
            c.style.right = e + "px";
            break;
          case Vc:
            c.style.bottom = f + "px";
            c.style.left = e + "px";
            break;
          case 3:
            c.style.bottom = f + "px", c.style.right = e + "px"
        }
        c = ["TL", "TR", "BL", "BR"];
        x.U.rc(this.R, "anchor" + c[b]);
        x.U.ib(this.R, "anchor" + c[a])
      }
    }, PD: function () {
      return this.m.anchor
    }, getContainer: u("R"), Rd: function (a) {
      a instanceof M && (this.m = this.m || {Qg: t}, this.m.Ga = new M(a.width, a.height), this.R && this.wc(this.m.anchor))
    }, yj: function () {
      return this.m.Ga
    }, fd: u("R"), show: function () {
      this.Va != q && (this.Va = q, this.R && x.U.show(this.R))
    }, aa: function () {
      this.Va != t && (this.Va = t, this.R && x.U.aa(this.R))
    }, isPrintable: function () {
      return !!this.m.Qg
    }, Oc: function () {
      return !this.R && !this.P ? t : !!this.Va
    }
  });
  var Tc = 0, Uc = 1, Vc = 2;

  function rb(a) {
    Sc.call(this);
    a = a || {};
    this.m = {
      Qg: t,
      TF: a.showZoomInfo || q,
      anchor: a.anchor,
      Ga: a.offset,
      type: a.type,
      qX: a.enableGeolocation || t
    };
    this.defaultAnchor = K() ? 3 : Tc;
    this.defaultOffset = new M(10, 10);
    this.wc(a.anchor);
    this.Cn(a.type);
    this.Gf()
  }

  x.lang.xa(rb, Sc, "NavigationControl");
  x.extend(rb.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, Cn: function (a) {
      this.m.type = bb(a) && 0 <= a && 3 >= a ? a : 0
    }, Mp: function () {
      return this.m.type
    }, Gf: function () {
      var a = this;
      Wa.load("navictrl", function () {
        a.Ff()
      })
    }
  });

  function Wc(a) {
    Sc.call(this);
    a = a || {};
    this.m = {
      anchor: a.anchor || Vc,
      Ga: a.offset || new M(10, 30),
      C0: a.showAddressBar !== t,
      f4: a.enableAutoLocation || t,
      hN: a.locationIcon || s
    };
    var b = this;
    this.cr = 1200;
    b.z1 = [];
    this.ue = [];
    Wa.load("geoctrl", function () {
      (function e() {
        if (0 !== b.ue.length) {
          var a = b.ue.shift();
          b[a.method].apply(b, a.arguments);
          e()
        }
      })();
      b.BQ()
    });
    Ua(Na)
  }

  x.lang.xa(Wc, Sc, "GeolocationControl");
  x.extend(Wc.prototype, {
    location: function () {
      this.ue.push({method: "location", arguments: arguments})
    }, getAddressComponent: ea(s)
  });

  function Xc(a) {
    Sc.call(this);
    a = a || {};
    this.m = {Qg: t, anchor: a.anchor, Ga: a.offset};
    this.hc = [];
    this.defaultAnchor = Vc;
    this.defaultOffset = new M(5, 2);
    this.wc(a.anchor);
    this.vK = t;
    this.Gf()
  }

  x.lang.xa(Xc, Sc, "CopyrightControl");
  x.object.extend(Xc.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, Tw: function (a) {
      if (a && bb(a.id) && !isNaN(a.id)) {
        var b = {bounds: s, content: ""}, c;
        for (c in a) b[c] = a[c];
        if (a = this.Km(a.id)) for (var e in b) a[e] = b[e]; else this.hc.push(b)
      }
    }, Km: function (a) {
      for (var b = 0, c = this.hc.length; b < c; b++) if (this.hc[b].id == a) return this.hc[b]
    }, WD: u("hc"), rF: function (a) {
      for (var b = 0, c = this.hc.length; b < c; b++) this.hc[b].id == a && (r = this.hc.splice(b, 1), b--, c = this.hc.length)
    }, Gf: function () {
      var a = this;
      Wa.load("copyrightctrl", function () {
        a.Ff()
      })
    }
  });

  function tb(a) {
    Sc.call(this);
    a = a || {};
    this.m = {
      Qg: t,
      size: a.size || new M(150, 150),
      padding: 5,
      eb: a.isOpen === q ? q : t,
      Y1: 4,
      Ga: a.offset,
      anchor: a.anchor
    };
    this.defaultAnchor = 3;
    this.defaultOffset = new M(0, 0);
    this.$q = this.br = 13;
    this.wc(a.anchor);
    this.He(this.m.size);
    this.Gf()
  }

  x.lang.xa(tb, Sc, "OverviewMapControl");
  x.extend(tb.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, wc: function (a) {
      Sc.prototype.wc.call(this, a)
    }, ve: function () {
      this.ve.Go = q;
      this.m.eb = !this.m.eb;
      this.R || (this.ve.Go = t)
    }, He: function (a) {
      a instanceof M || (a = new M(150, 150));
      a.width = 0 < a.width ? a.width : 150;
      a.height = 0 < a.height ? a.height : 150;
      this.m.size = a
    }, wb: function () {
      return this.m.size
    }, eb: function () {
      return this.m.eb
    }, Gf: function () {
      var a = this;
      Wa.load("control", function () {
        a.Ff()
      })
    }
  });

  function Yc(a) {
    Sc.call(this);
    a = a || {};
    this.defaultAnchor = Tc;
    this.YV = a.canCheckSize === t ? t : q;
    this.rj = "";
    this.defaultOffset = new M(10, 10);
    this.onChangeBefore = [];
    this.onChangeAfter = [];
    this.onChangeSuccess = [];
    this.m = {Qg: t, Ga: a.offset || this.defaultOffset, anchor: a.anchor || this.defaultAnchor, expand: !!a.expand};
    a.onChangeBefore && cb(a.onChangeBefore) && this.onChangeBefore.push(a.onChangeBefore);
    a.onChangeAfter && cb(a.onChangeAfter) && this.onChangeAfter.push(a.onChangeAfter);
    a.onChangeSuccess && cb(a.onChangeSuccess) && this.onChangeSuccess.push(a.onChangeSuccess);
    this.wc(a.anchor);
    this.Gf()
  }

  x.lang.xa(Yc, Sc, "CityListControl");
  x.object.extend(Yc.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, Gf: function () {
      var a = this;
      Wa.load("citylistcontrol", function () {
        a.Ff()
      }, q)
    }
  });

  function sb(a) {
    Sc.call(this);
    a = a || {};
    this.m = {Qg: t, color: "black", ld: "metric", Ga: a.offset};
    this.defaultAnchor = Vc;
    this.defaultOffset = new M(81, 18);
    this.wc(a.anchor);
    this.gi = {
      metric: {name: "metric", LK: 1, BM: 1E3, kP: "\u7c73", lP: "\u516c\u91cc"},
      us: {name: "us", LK: 3.2808, BM: 5280, kP: "\u82f1\u5c3a", lP: "\u82f1\u91cc"}
    };
    this.gi[this.m.ld] || (this.m.ld = "metric");
    this.nJ = s;
    this.JI = {};
    this.Gf()
  }

  x.lang.xa(sb, Sc, "ScaleControl");
  x.object.extend(sb.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, dl: function (a) {
      this.m.color = a + ""
    }, D4: function () {
      return this.m.color
    }, PF: function (a) {
      this.m.ld = this.gi[a] && this.gi[a].name || this.m.ld
    }, RY: function () {
      return this.m.ld
    }, Gf: function () {
      var a = this;
      Wa.load("control", function () {
        a.Ff()
      })
    }
  });
  var Zc = 0;

  function ub(a) {
    Sc.call(this);
    a = a || {};
    this.defaultAnchor = Uc;
    this.defaultOffset = new M(10, 10);
    this.m = {
      Qg: t,
      Bh: [Qa, db, Va, Ta],
      KW: ["B_DIMENSIONAL_MAP", "B_SATELLITE_MAP", "B_NORMAL_MAP"],
      type: a.type || Zc,
      Ga: a.offset || this.defaultOffset,
      uX: q
    };
    this.wc(a.anchor);
    "[object Array]" == Object.prototype.toString.call(a.mapTypes) && (this.m.Bh = a.mapTypes.slice(0));
    this.Gf()
  }

  x.lang.xa(ub, Sc, "MapTypeControl");
  x.object.extend(ub.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, fz: function (a) {
      this.P.to = a
    }, Gf: function () {
      var a = this;
      Wa.load("control", function () {
        a.Ff()
      }, q)
    }
  });

  function $c(a) {
    Sc.call(this);
    a = a || {};
    this.m = {Qg: t, Ga: a.offset, anchor: a.anchor};
    this.fj = t;
    this.Bw = s;
    this.UI = new ad({Xe: "api"});
    this.VI = new cd(s, {Xe: "api"});
    this.defaultAnchor = Uc;
    this.defaultOffset = new M(10, 10);
    this.wc(a.anchor);
    this.Gf();
    Ua(Aa)
  }

  x.lang.xa($c, Sc, "PanoramaControl");
  x.extend($c.prototype, {
    initialize: function (a) {
      this.P = a;
      return this.R
    }, Gf: function () {
      var a = this;
      Wa.load("control", function () {
        a.Ff()
      })
    }
  });

  function dd(a) {
    x.lang.Ja.call(this);
    this.m = {bb: s, cursor: "default"};
    this.m = x.extend(this.m, a);
    this.Pb = "contextmenu";
    this.P = s;
    this.Da = [];
    this.Rf = [];
    this.Je = [];
    this.nx = this.Ls = s;
    this.Vh = t;
    var b = this;
    Wa.load("menu", function () {
      b.ob()
    })
  }

  x.lang.xa(dd, x.lang.Ja, "ContextMenu");
  x.object.extend(dd.prototype, {
    za: function (a, b) {
      this.P = a;
      this.Wl = b || s
    }, remove: function () {
      this.P = this.Wl = s
    }, zs: function (a) {
      if (a && !("menuitem" != a.Pb || "" == a.nh || 0 >= a.mj)) {
        for (var b = 0, c = this.Da.length; b < c; b++) if (this.Da[b] === a) return;
        this.Da.push(a);
        this.Rf.push(a)
      }
    }, removeItem: function (a) {
      if (a && "menuitem" == a.Pb) {
        for (var b = 0, c = this.Da.length; b < c; b++) this.Da[b] === a && (this.Da[b].remove(), this.Da.splice(b, 1), c--);
        b = 0;
        for (c = this.Rf.length; b < c; b++) this.Rf[b] === a && (this.Rf[b].remove(), this.Rf.splice(b, 1), c--)
      }
    }, jC: function () {
      this.Da.push({Pb: "divider", bk: this.Je.length});
      this.Je.push({U: s})
    }, uF: function (a) {
      if (this.Je[a]) {
        for (var b = 0, c = this.Da.length; b < c; b++) this.Da[b] && ("divider" == this.Da[b].Pb && this.Da[b].bk == a) && (this.Da.splice(b, 1), c--), this.Da[b] && ("divider" == this.Da[b].Pb && this.Da[b].bk > a) && this.Da[b].bk--;
        this.Je.splice(a, 1)
      }
    }, fd: u("R"), show: function () {
      this.Vh != q && (this.Vh = q)
    }, aa: function () {
      this.Vh != t && (this.Vh = t)
    }, h0: function (a) {
      a && (this.m.cursor = a)
    }, getItem: function (a) {
      return this.Rf[a]
    }
  });
  var ed = J.ta + "menu_zoom_in.png", fd = J.ta + "menu_zoom_out.png";

  function gd(a, b, c) {
    if (a && cb(b)) {
      x.lang.Ja.call(this);
      this.m = {width: 100, id: "", Xm: ""};
      c = c || {};
      this.m.width = 1 * c.width ? c.width : 100;
      this.m.id = c.id ? c.id : "";
      this.m.Xm = c.iconUrl ? c.iconUrl : "";
      this.nh = a + "";
      this.Qz = b;
      this.P = s;
      this.Pb = "menuitem";
      this.ns = this.Rv = this.R = this.Qh = s;
      this.Th = q;
      var e = this;
      Wa.load("menu", function () {
        e.ob()
      })
    }
  }

  x.lang.xa(gd, x.lang.Ja, "MenuItem");
  x.object.extend(gd.prototype, {
    za: function (a, b) {
      this.P = a;
      this.Qh = b
    }, remove: function () {
      this.P = this.Qh = s
    }, wu: function (a) {
      a && (this.nh = a + "")
    }, Xb: function (a) {
      a && (this.m.Xm = a)
    }, fd: u("R"), enable: function () {
      this.Th = q
    }, disable: function () {
      this.Th = t
    }
  });

  function lb(a, b) {
    a && !b && (b = a);
    this.Me = this.be = this.Se = this.de = this.nf = this.kf = s;
    a && (this.nf = new P(a.lng, a.lat), this.kf = new P(b.lng, b.lat), this.Se = a.lng, this.de = a.lat, this.Me = b.lng, this.be = b.lat)
  }

  x.object.extend(lb.prototype, {
    Gj: function () {
      return !this.nf || !this.kf
    }, Vb: function (a) {
      return !(a instanceof lb) || this.Gj() ? t : this.Be().Vb(a.Be()) && this.tf().Vb(a.tf())
    }, Be: u("nf"), tf: u("kf"), oW: function (a) {
      return !(a instanceof lb) || this.Gj() || a.Gj() ? t : a.Se > this.Se && a.Me < this.Me && a.de > this.de && a.be < this.be
    }, Hb: function () {
      return this.Gj() ? s : new P((this.Se + this.Me) / 2, (this.de + this.be) / 2)
    }, Jt: function (a) {
      if (!(a instanceof lb) || Math.max(a.Se, a.Me) < Math.min(this.Se, this.Me) || Math.min(a.Se, a.Me) > Math.max(this.Se, this.Me) || Math.max(a.de, a.be) < Math.min(this.de, this.be) || Math.min(a.de, a.be) > Math.max(this.de, this.be)) return s;
      var b = Math.max(this.Se, a.Se), c = Math.min(this.Me, a.Me), e = Math.max(this.de, a.de),
        a = Math.min(this.be, a.be);
      return new lb(new P(b, e), new P(c, a))
    }, Hs: function (a) {
      return !(a instanceof P || a instanceof L) || this.Gj() ? t : a.lng >= this.Se && a.lng <= this.Me && a.lat >= this.de && a.lat <= this.be
    }, extend: function (a) {
      if (a instanceof P || a instanceof L) {
        var b = a.lng, a = a.lat;
        this.nf || (this.nf = new P(0, 0));
        this.kf || (this.kf = new P(0, 0));
        if (!this.Se || this.Se > b) this.nf.lng = this.Se = b;
        if (!this.Me || this.Me < b) this.kf.lng = this.Me = b;
        if (!this.de || this.de > a) this.nf.lat = this.de = a;
        if (!this.be || this.be < a) this.kf.lat = this.be = a
      }
    }, gG: function () {
      return this.Gj() ? new P(0, 0) : new P(Math.abs(this.Me - this.Se), Math.abs(this.be - this.de))
    }
  });

  function P(a, b) {
    isNaN(a) && (a = Sb(a), a = isNaN(a) ? 0 : a);
    eb(a) && (a = parseFloat(a));
    isNaN(b) && (b = Sb(b), b = isNaN(b) ? 0 : b);
    eb(b) && (b = parseFloat(b));
    this.lng = a;
    this.lat = b
  }

  P.FE = function (a) {
    return a && 180 >= a.lng && -180 <= a.lng && 74 >= a.lat && -74 <= a.lat
  };
  P.prototype.Vb = function (a) {
    return a && this.lat == a.lat && this.lng == a.lng
  };

  function L(a, b) {
    isNaN(a) && (a = Sb(a), a = isNaN(a) ? 0 : a);
    eb(a) && (a = parseFloat(a));
    isNaN(b) && (b = Sb(b), b = isNaN(b) ? 0 : b);
    eb(b) && (b = parseFloat(b));
    this.lng = a;
    this.lat = b;
    this.Xe = "inner"
  }

  L.FE = function (a) {
    return a && 180 >= a.lng && -180 <= a.lng && 74 >= a.lat && -74 <= a.lat
  };
  L.prototype.Vb = function (a) {
    return a && this.lat == a.lat && this.lng == a.lng
  };

  function hd() {
  }

  hd.prototype.Lg = function () {
    aa("lngLatToPoint\u65b9\u6cd5\u672a\u5b9e\u73b0")
  };
  hd.prototype.Kj = function () {
    aa("pointToLngLat\u65b9\u6cd5\u672a\u5b9e\u73b0")
  };

  function id() {
  };var kb = {
    NK: function (a, b, c) {
      Wa.load("coordtransutils", function () {
        kb.zV(a, b, c)
      }, q)
    }, MK: function (a, b, c) {
      Wa.load("coordtransutils", function () {
        kb.yV(a, b, c)
      }, q)
    }
  };

  function jd() {
    this.Qa = [];
    var a = this;
    Wa.load("convertor", function () {
      a.zQ()
    })
  }

  x.xa(jd, x.lang.Ja, "Convertor");
  x.extend(jd.prototype, {
    translate: function (a, b, c, e) {
      this.Qa.push({method: "translate", arguments: [a, b, c, e]})
    }
  });
  S(jd.prototype, {translate: jd.prototype.translate});

  function R() {
  }

  R.prototype = new hd;
  x.extend(R, {
    TP: 6370996.81,
    TG: [1.289059486E7, 8362377.87, 5591021, 3481989.83, 1678043.12, 0],
    ev: [86, 60, 45, 30, 15, 0],
    ZP: [[1.410526172116255E-8, 8.98305509648872E-6, -1.9939833816331, 200.9824383106796, -187.2403703815547, 91.6087516669843, -23.38765649603339, 2.57121317296198, -0.03801003308653, 1.73379812E7], [-7.435856389565537E-9, 8.983055097726239E-6, -0.78625201886289, 96.32687599759846, -1.85204757529826, -59.36935905485877, 47.40033549296737, -16.50741931063887, 2.28786674699375, 1.026014486E7], [-3.030883460898826E-8, 8.98305509983578E-6, 0.30071316287616, 59.74293618442277, 7.357984074871, -25.38371002664745, 13.45380521110908, -3.29883767235584, 0.32710905363475, 6856817.37], [-1.981981304930552E-8, 8.983055099779535E-6, 0.03278182852591, 40.31678527705744, 0.65659298677277, -4.44255534477492, 0.85341911805263, 0.12923347998204, -0.04625736007561, 4482777.06], [3.09191371068437E-9, 8.983055096812155E-6, 6.995724062E-5, 23.10934304144901, -2.3663490511E-4, -0.6321817810242, -0.00663494467273, 0.03430082397953, -0.00466043876332, 2555164.4], [2.890871144776878E-9, 8.983055095805407E-6, -3.068298E-8, 7.47137025468032, -3.53937994E-6, -0.02145144861037, -1.234426596E-5, 1.0322952773E-4, -3.23890364E-6, 826088.5]],
    QG: [[-0.0015702102444, 111320.7020616939, 1704480524535203, -10338987376042340, 26112667856603880, -35149669176653700, 26595700718403920, -10725012454188240, 1800819912950474, 82.5], [8.277824516172526E-4, 111320.7020463578, 6.477955746671607E8, -4.082003173641316E9, 1.077490566351142E10, -1.517187553151559E10, 1.205306533862167E10, -5.124939663577472E9, 9.133119359512032E8, 67.5], [0.00337398766765, 111320.7020202162, 4481351.045890365, -2.339375119931662E7, 7.968221547186455E7, -1.159649932797253E8, 9.723671115602145E7, -4.366194633752821E7, 8477230.501135234, 52.5], [0.00220636496208, 111320.7020209128, 51751.86112841131, 3796837.749470245, 992013.7397791013, -1221952.21711287, 1340652.697009075, -620943.6990984312, 144416.9293806241, 37.5], [-3.441963504368392E-4, 111320.7020576856, 278.2353980772752, 2485758.690035394, 6070.750963243378, 54821.18345352118, 9540.606633304236, -2710.55326746645, 1405.483844121726, 22.5], [-3.218135878613132E-4, 111320.7020701615, 0.00369383431289, 823725.6402795718, 0.46104986909093, 2351.343141331292, 1.58060784298199, 8.77738589078284, 0.37238884252424, 7.45]],
    J4: function (a, b) {
      if (!a || !b) return 0;
      var c, e, a = this.Zb(a);
      if (!a) return 0;
      c = this.ml(a.lng);
      e = this.ml(a.lat);
      b = this.Zb(b);
      return !b ? 0 : this.Nd(c, this.ml(b.lng), e, this.ml(b.lat))
    },
    Mk: function (a, b) {
      if (!a || !b) return 0;
      a.lng = this.bE(a.lng, -180, 180);
      a.lat = this.gE(a.lat, -80, 84);
      b.lng = this.bE(b.lng, -180, 180);
      b.lat = this.gE(b.lat, -80, 84);
      return this.Nd(this.ml(a.lng), this.ml(b.lng), this.ml(a.lat), this.ml(b.lat))
    },
    Zb: function (a) {
      if (a === s || a === l) return new L(0, 0);
      var b, c;
      b = new L(Math.abs(a.lng), Math.abs(a.lat));
      for (var e = 0; e < this.TG.length; e++) if (b.lat >= this.TG[e]) {
        c = this.ZP[e];
        break
      }
      a = this.OK(a, c);
      return a = new L(a.lng, a.lat)
    },
    Sa: function (a) {
      if (a === s || a === l || 180 < a.lng || -180 > a.lng || 90 < a.lat || -90 > a.lat) return new L(0, 0);
      var b, c;
      a.lng = this.bE(a.lng, -180, 180);
      a.lat = this.gE(a.lat, -85, 85);
      b = new L(a.lng, a.lat);
      for (var e = 0; e < this.ev.length; e++) if (b.lat >= this.ev[e]) {
        c = this.QG[e];
        break
      }
      if (!c) for (e = 0; e < this.ev.length; e++) if (b.lat <= -this.ev[e]) {
        c = this.QG[e];
        break
      }
      a = this.OK(a, c);
      return a = new L(a.lng, a.lat)
    },
    OK: function (a, b) {
      if (a && b) {
        var c = b[0] + b[1] * Math.abs(a.lng), e = Math.abs(a.lat) / b[9],
          e = b[2] + b[3] * e + b[4] * e * e + b[5] * e * e * e + b[6] * e * e * e * e + b[7] * e * e * e * e * e + b[8] * e * e * e * e * e * e,
          c = c * (0 > a.lng ? -1 : 1), e = e * (0 > a.lat ? -1 : 1);
        return new L(c, e)
      }
    },
    Nd: function (a, b, c, e) {
      return this.TP * Math.acos(Math.sin(c) * Math.sin(e) + Math.cos(c) * Math.cos(e) * Math.cos(b - a))
    },
    ml: function (a) {
      return Math.PI * a / 180
    },
    o7: function (a) {
      return 180 * a / Math.PI
    },
    gE: function (a, b, c) {
      b != s && (a = Math.max(a, b));
      c != s && (a = Math.min(a, c));
      return a
    },
    bE: function (a, b, c) {
      for (; a > c;) a -= c - b;
      for (; a < b;) a += c - b;
      return a
    }
  });
  x.extend(R.prototype, {
    zi: function (a) {
      return R.Sa(a)
    }, Lg: function (a) {
      a = R.Sa(a);
      return new Q(a.lng, a.lat)
    }, Dh: function (a) {
      return R.Zb(a)
    }, Kj: function (a) {
      a = new L(a.x, a.y);
      a = R.Zb(a);
      return new P(a.lng, a.lat)
    }, vc: function (a, b, c, e, f) {
      if (a) return a = this.zi(a, f), b = this.Wb(b), new Q(Math.round((a.lng - c.lng) / b + e.width / 2), Math.round((c.lat - a.lat) / b + e.height / 2))
    }, SZ: function (a, b, c, e) {
      if (a) return b = this.Wb(b), new Q(Math.round((a.lng - c.lng) / b + e.width / 2), Math.round((c.lat - a.lat) / b + e.height / 2))
    }, cc: function (a, b, c, e, f) {
      if (a) return b = this.Wb(b), this.Dh(new L(c.lng + b * (a.x - e.width / 2), c.lat - b * (a.y - e.height / 2)), f)
    }, Hy: function (a, b, c, e) {
      if (a) return b = this.Wb(b), new L(c.lng + b * (a.x - e.width / 2), c.lat - b * (a.y - e.height / 2))
    }, Wb: function (a) {
      return Math.pow(2, 18 - a)
    }, tO: da("Ma")
  });

  function nb() {
    this.rj = "bj"
  }

  nb.prototype = new R;
  x.extend(nb.prototype, {
    zi: function (a, b) {
      return this.kR(b, R.Sa(a))
    }, Dh: function (a, b) {
      return R.Zb(this.lR(b, a))
    }, lngLatToPointFor3D: function (a, b) {
      var c = this, e = R.Sa(a);
      Wa.load("coordtrans", function () {
        var a = id.eE(c.rj || "bj", e), a = new Q(a.x, a.y);
        b && b(a)
      }, q)
    }, pointToLngLatFor3D: function (a, b) {
      var c = this, e = new P(a.x, a.y);
      Wa.load("coordtrans", function () {
        var a = id.cE(c.rj || "bj", e), a = new P(a.lng, a.lat), a = R.Zb(a);
        b && b(a)
      }, q)
    }, kR: function (a, b) {
      if (Wa.qb("coordtrans").Re == Wa.Tj.Jq) {
        var c = id.eE(a || "bj", b);
        return new P(c.x, c.y)
      }
      Wa.load("coordtrans", ba());
      return new P(0, 0)
    }, lR: function (a, b) {
      if (Wa.qb("coordtrans").Re == Wa.Tj.Jq) {
        var c = id.cE(a || "bj", b);
        return new P(c.lng, c.lat)
      }
      Wa.load("coordtrans", ba());
      return new P(0, 0)
    }, Wb: function (a) {
      return Math.pow(2, 20 - a)
    }, tO: da("Ma")
  });

  function kd() {
    this.Pb = "overlay"
  }

  x.lang.xa(kd, x.lang.Ja, "Overlay");
  kd.Sk = function (a) {
    a *= 1;
    return !a ? 0 : 1E5 * (90 - a) << 1
  };
  x.extend(kd.prototype, {
    Le: function (a) {
      if (!this.ca && cb(this.initialize) && (this.ca = this.initialize(a))) this.ca.style.WebkitUserSelect = "none";
      this.draw()
    }, initialize: function () {
      aa("initialize\u65b9\u6cd5\u672a\u5b9e\u73b0")
    }, draw: function () {
      aa("draw\u65b9\u6cd5\u672a\u5b9e\u73b0")
    }, remove: function () {
      this.ca && this.ca.parentNode && this.ca.parentNode.removeChild(this.ca);
      this.ca = s;
      this.dispatchEvent(new O("onremove"))
    }, aa: function () {
      this.ca && x.U.aa(this.ca)
    }, show: function () {
      this.ca && x.U.show(this.ca)
    }, Oc: function () {
      return !this.ca || "none" == this.ca.style.display || "hidden" == this.ca.style.visibility ? t : q
    }
  });
  A.df(function (a) {
    function b(a, b) {
      var c = F("div"), i = c.style;
      i.position = "absolute";
      i.top = i.left = i.width = i.height = "0";
      i.zIndex = b;
      a.appendChild(c);
      return c
    }

    var c = a.ba;
    c.Wc = a.Wc = b(a.platform, 200);
    a.ce.HD = b(c.Wc, 800);
    a.ce.XE = b(c.Wc, 700);
    a.ce.yL = b(c.Wc, 600);
    a.ce.PE = b(c.Wc, 500);
    a.ce.lN = b(c.Wc, 400);
    a.ce.mN = b(c.Wc, 300);
    a.ce.wP = b(c.Wc, 201);
    a.ce.Xt = b(c.Wc, 200)
  });

  function mb() {
    x.lang.Ja.call(this);
    kd.call(this);
    this.map = s;
    this.Va = q;
    this.Fb = s;
    this.FH = 0
  }

  x.lang.xa(mb, kd, "OverlayInternal");
  x.extend(mb.prototype, {
    initialize: function (a) {
      this.map = a;
      x.lang.Ja.call(this, this.da);
      return s
    }, Kx: u("map"), draw: ba(), Vj: ba(), remove: function () {
      this.map = s;
      x.lang.qx(this.da);
      kd.prototype.remove.call(this)
    }, aa: function () {
      this.Va !== t && (this.Va = t)
    }, show: function () {
      this.Va !== q && (this.Va = q)
    }, Oc: function () {
      return !this.ca ? t : !!this.Va
    }, Ta: u("ca"), sO: function (a) {
      var a = a || {}, b;
      for (b in a) this.K[b] = a[b]
    }, xq: da("zIndex"), wj: function () {
      this.K.wj = q
    }, UW: function () {
      this.K.wj = t
    }, om: da("rg"), jq: function () {
      this.rg = s
    }
  });

  function ld() {
    this.map = s;
    this.ua = {};
    this.Ie = []
  }

  A.df(function (a) {
    var b = new ld;
    b.map = a;
    a.ua = b.ua;
    a.Ie = b.Ie;
    a.addEventListener("load", function (a) {
      b.draw(a)
    });
    a.addEventListener("moveend", function (a) {
      b.draw(a)
    });
    x.ga.oa && 8 > x.ga.oa || "BackCompat" === document.compatMode ? a.addEventListener("zoomend", function (a) {
      setTimeout(function () {
        b.draw(a)
      }, 20)
    }) : a.addEventListener("zoomend", function (a) {
      b.draw(a)
    });
    a.addEventListener("maptypechange", function (a) {
      b.draw(a)
    });
    a.addEventListener("addoverlay", function (a) {
      a = a.target;
      if (a instanceof mb) b.ua[a.da] || (b.ua[a.da] = a); else {
        for (var e = t, f = 0, g = b.Ie.length; f < g; f++) if (b.Ie[f] === a) {
          e = q;
          break
        }
        e || b.Ie.push(a)
      }
    });
    a.addEventListener("removeoverlay", function (a) {
      a = a.target;
      if (a instanceof mb) delete b.ua[a.da]; else for (var e = 0, f = b.Ie.length; e < f; e++) if (b.Ie[e] === a) {
        b.Ie.splice(e, 1);
        break
      }
    });
    a.addEventListener("clearoverlays", function () {
      this.Mc();
      for (var a in b.ua) b.ua[a].K.wj && (b.ua[a].remove(), delete b.ua[a]);
      a = 0;
      for (var e = b.Ie.length; a < e; a++) b.Ie[a].enableMassClear !== t && (b.Ie[a].remove(), b.Ie[a] = s, b.Ie.splice(a, 1), a--, e--)
    });
    a.addEventListener("infowindowopen", function () {
      var a = this.Fb;
      a && (x.U.aa(a.Cc), x.U.aa(a.dc))
    });
    a.addEventListener("movestart", function () {
      this.wh() && this.wh().uJ()
    });
    a.addEventListener("moveend", function () {
      this.wh() && this.wh().iJ()
    })
  });
  ld.prototype.draw = function (a) {
    if (A.Mq) {
      var b = A.Mq.nt(this.map);
      "canvas" === b.Pb && b.canvas && b.fR(b.canvas.getContext("2d"))
    }
    for (var c in this.ua) this.ua[c].draw(a);
    x.oc.Rb(this.Ie, function (a) {
      a.draw()
    });
    this.map.ba.xb && this.map.ba.xb.va();
    A.Mq && b.LF()
  };

  function md(a) {
    mb.call(this);
    a = a || {};
    this.K = {
      strokeColor: a.strokeColor || "#3a6bdb",
      tc: a.strokeWeight || 5,
      Ad: a.strokeOpacity || 0.65,
      strokeStyle: a.strokeStyle || "solid",
      wj: a.enableMassClear === t ? t : q,
      Pk: s,
      Pm: s,
      ze: a.enableEditing === q ? q : t,
      qN: 5,
      x1: t,
      of: a.enableClicking === t ? t : q,
      wi: a.icons && 0 < a.icons.length ? a.icons : s,
      PX: a.geodesic === q ? q : t,
      TE: a.linkRight === q ? q : t
    };
    0 >= this.K.tc && (this.K.tc = 5);
    if (0 > this.K.Ad || 1 < this.K.Ad) this.K.Ad = 0.65;
    if (0 > this.K.Dg || 1 < this.K.Dg) this.K.Dg = 0.65;
    "solid" != this.K.strokeStyle && "dashed" != this.K.strokeStyle && (this.K.strokeStyle = "solid");
    this.ca = s;
    this.mv = new lb(0, 0);
    this.lf = [];
    this.uc = [];
    this.Ya = {}
  }

  x.lang.xa(md, mb, "Graph");
  md.Gx = function (a) {
    var b = [];
    if (!a) return b;
    eb(a) && x.oc.Rb(a.split(";"), function (a) {
      a = a.split(",");
      b.push(new P(a[0], a[1]))
    });
    "[object Array]" == Object.prototype.toString.apply(a) && 0 < a.length && (b = a);
    return b
  };
  md.iF = [0.09, 0.0050, 1.0E-4, 1.0E-5];
  x.extend(md.prototype, {
    initialize: function (a) {
      this.map = a;
      return s
    }, draw: ba(), fs: function (a) {
      this.lf.length = 0;
      this.ja = md.Gx(a).slice(0);
      this.Nh()
    }, Sd: function (a) {
      this.fs(a)
    }, Nh: function () {
      if (this.ja) {
        var a = this;
        a.mv = new lb;
        x.oc.Rb(this.ja, function (b) {
          a.mv.extend(b)
        })
      }
    }, Ze: u("ja"), Bn: function (a, b) {
      b && this.ja[a] && (this.lf.length = 0, this.ja[a] = new P(b.lng, b.lat), this.Nh())
    }, setStrokeColor: function (a) {
      this.K.strokeColor = a
    }, IY: function () {
      return this.K.strokeColor
    }, wq: function (a) {
      0 < a && (this.K.tc = a)
    }, cM: function () {
      return this.K.tc
    }, uq: function (a) {
      a == l || (1 < a || 0 > a) || (this.K.Ad = a)
    }, JY: function () {
      return this.K.Ad
    }, qu: function (a) {
      1 < a || 0 > a || (this.K.Dg = a)
    }, dY: function () {
      return this.K.Dg
    }, vq: function (a) {
      "solid" != a && "dashed" != a || (this.K.strokeStyle = a)
    }, bM: function () {
      return this.K.strokeStyle
    }, setFillColor: function (a) {
      this.K.fillColor = a || ""
    }, cY: function () {
      return this.K.fillColor
    }, ke: u("mv"), remove: function () {
      this.map && this.map.removeEventListener("onmousemove", this.Ov);
      mb.prototype.remove.call(this);
      this.lf.length = 0
    }, ze: function () {
      if (!(2 > this.ja.length)) {
        this.K.ze = q;
        var a = this;
        Wa.load("poly", function () {
          a.Ak()
        }, q)
      }
    }, TW: function () {
      this.K.ze = t;
      var a = this;
      Wa.load("poly", function () {
        a.li()
      }, q)
    }, $X: function () {
      return this.K.ze
    }, gY: function () {
      for (var a = [], b = 0; b < this.ja.length - 1; b++) var c = this.UV(this.ja[b], this.ja[b + 1]), a = a.concat(c);
      return a = a.concat(this.ja[this.ja.length - 1])
    }, UV: function (a, b) {
      if (a.Vb(b)) return [a];
      var c = R.Nd(Xb(a.lng), Xb(a.lat), Xb(b.lng), Xb(b.lat)), c = R.Mk(a, b);
      if (25E4 > c) return [a];
      var e = [], c = Math.round(c / 15E4), f = this.yK(a, b);
      e.push(a);
      for (var g = 0; g < c; g++) {
        var i = this.zK(a, b, g / c, f);
        e.push(i)
      }
      e.push(b);
      return e
    }, zK: function (a, b, c, e) {
      var f = Xb(a.lat), g = Xb(b.lat), a = Xb(a.lng), i = Xb(b.lng), b = Math.sin((1 - c) * e) / Math.sin(e),
        c = Math.sin(c * e) / Math.sin(e), e = b * Math.cos(f) * Math.cos(a) + c * Math.cos(g) * Math.cos(i),
        a = b * Math.cos(f) * Math.sin(a) + c * Math.cos(g) * Math.sin(i);
      return new P(180 * (Math.atan2(a, e) / Math.PI), 180 * (Math.atan2(b * Math.sin(f) + c * Math.sin(g), Math.sqrt(Math.pow(e, 2) + Math.pow(a, 2))) / Math.PI))
    }, yK: function (a, b) {
      var c = Xb(a.lat), e = Xb(b.lat);
      return Math.acos(Math.sin(c) * Math.sin(e) + Math.cos(c) * Math.cos(e) * Math.cos(Math.abs(Xb(b.lng) - Xb(a.lng))))
    }
  });

  function nd(a) {
    mb.call(this);
    this.ca = this.map = s;
    this.K = {
      width: 0,
      height: 0,
      Ga: new M(0, 0),
      opacity: 1,
      background: "transparent",
      ny: 1,
      ZM: "#000",
      JZ: "solid",
      point: s
    };
    this.sO(a);
    this.point = this.K.point
  }

  x.lang.xa(nd, mb, "Division");
  x.extend(nd.prototype, {
    Vj: function () {
      var a = this.K, b = this.content, c = ['<div class="BMap_Division" style="position:absolute;'];
      c.push("width:" + a.width + "px;display:block;");
      c.push("overflow:hidden;");
      "none" != a.borderColor && c.push("border:" + a.ny + "px " + a.JZ + " " + a.ZM + ";");
      c.push("opacity:" + a.opacity + "; filter:(opacity=" + 100 * a.opacity + ")");
      c.push("background:" + a.background + ";");
      c.push('z-index:60;">');
      c.push(b);
      c.push("</div>");
      this.ca = Ib(this.map.Zf().XE, c.join(""))
    }, initialize: function (a) {
      this.map = a;
      this.Vj();
      this.ca && x.V(this.ca, K() ? "touchstart" : "mousedown", function (a) {
        oa(a)
      });
      return this.ca
    }, draw: function () {
      var a = this.map.cf(this.K.point);
      this.K.Ga = new M(-Math.round(this.K.width / 2) - Math.round(this.K.ny), -Math.round(this.K.height / 2) - Math.round(this.K.ny));
      this.ca.style.left = a.x + this.K.Ga.width + "px";
      this.ca.style.top = a.y + this.K.Ga.height + "px"
    }, ma: function () {
      return this.K.point
    }, T2: function () {
      return this.map.Do(this.ma())
    }, va: function (a) {
      this.K.point = a;
      this.draw()
    }, i0: function (a, b) {
      this.K.width = Math.round(a);
      this.K.height = Math.round(b);
      this.ca && (this.ca.style.width = this.K.width + "px", this.ca.style.height = this.K.height + "px", this.draw())
    }
  });

  function od(a, b, c) {
    a && b && (this.imageUrl = a, this.size = b, a = new M(Math.floor(b.width / 2), Math.floor(b.height / 2)), c = c || {}, a = c.anchor || a, b = c.imageOffset || new M(0, 0), this.imageSize = c.imageSize, this.anchor = a, this.imageOffset = b, this.infoWindowAnchor = c.infoWindowAnchor || this.anchor, this.printImageUrl = c.printImageUrl || "")
  }

  x.extend(od.prototype, {
    uO: function (a) {
      a && (this.imageUrl = a)
    }, z0: function (a) {
      a && (this.printImageUrl = a)
    }, He: function (a) {
      a && (this.size = new M(a.width, a.height))
    }, wc: function (a) {
      a && (this.anchor = new M(a.width, a.height))
    }, ru: function (a) {
      a && (this.imageOffset = new M(a.width, a.height))
    }, n0: function (a) {
      a && (this.infoWindowAnchor = new M(a.width, a.height))
    }, k0: function (a) {
      a && (this.imageSize = new M(a.width, a.height))
    }, toString: ea("Icon")
  });

  function pd(a, b) {
    if (a) {
      b = b || {};
      this.style = {
        anchor: b.anchor || new M(0, 0),
        fillColor: b.fillColor || "#000",
        Dg: b.fillOpacity || 0,
        scale: b.scale || 1,
        rotation: b.rotation || 0,
        strokeColor: b.strokeColor || "#000",
        Ad: b.strokeOpacity || 1,
        tc: b.strokeWeight
      };
      this.Pb = "number" === typeof a ? a : "UserDefined";
      this.Vi = this.style.anchor;
      this.Pr = new M(0, 0);
      this.anchor = s;
      this.zB = a;
      var c = this;
      Wa.load("symbol", function () {
        c.bo()
      }, q)
    }
  }

  x.extend(pd.prototype, {
    setPath: da("zB"), setAnchor: function (a) {
      this.Vi = this.style.anchor = a
    }, setRotation: function (a) {
      this.style.rotation = a
    }, setScale: function (a) {
      this.style.scale = a
    }, setStrokeWeight: function (a) {
      this.style.tc = a
    }, setStrokeColor: function (a) {
      a = x.Fs.KC(a, this.style.Ad);
      this.style.strokeColor = a
    }, setStrokeOpacity: function (a) {
      this.style.Ad = a
    }, setFillOpacity: function (a) {
      this.style.Dg = a
    }, setFillColor: function (a) {
      this.style.fillColor = a
    }
  });

  function qd(a, b, c, e) {
    a && (this.gw = {}, this.wL = e ? !!e : t, this.ad = [], this.R0 = a instanceof pd ? a : s, this.$I = b === l ? q : !!(b.indexOf("%") + 1), this.qk = isNaN(parseFloat(b)) ? 1 : this.$I ? parseFloat(b) / 100 : parseFloat(b), this.aJ = !!(c.indexOf("%") + 1), this.repeat = c != l ? this.aJ ? parseFloat(c) / 100 : parseFloat(c) : 0)
  };

  function rd(a, b) {
    x.lang.Ja.call(this);
    this.content = a;
    this.map = s;
    b = b || {};
    this.K = {
      width: b.width || 0,
      height: b.height || 0,
      maxWidth: b.maxWidth || 730,
      Ga: b.offset || new M(0, 0),
      title: b.title || "",
      ZE: b.maxContent || "",
      uh: b.enableMaximize || t,
      ct: b.enableAutoPan === t ? t : q,
      qD: b.enableCloseOnClick === t ? t : q,
      margin: b.margin || [10, 10, 40, 10],
      GC: b.collisions || [[10, 10], [10, 10], [10, 10], [10, 10]],
      eZ: t,
      h_: b.onClosing || ea(q),
      pL: t,
      vD: b.enableParano === q ? q : t,
      message: b.message,
      yD: b.enableSearchTool === q ? q : t,
      Wx: b.headerContent || "",
      rD: b.enableContentScroll || t
    };
    if (0 != this.K.width && (220 > this.K.width && (this.K.width = 220), 730 < this.K.width)) this.K.width = 730;
    if (0 != this.K.height && (60 > this.K.height && (this.K.height = 60), 650 < this.K.height)) this.K.height = 650;
    if (0 != this.K.maxWidth && (220 > this.K.maxWidth && (this.K.maxWidth = 220), 730 < this.K.maxWidth)) this.K.maxWidth = 730;
    this.me = t;
    this.Qi = J.ta;
    this.yb = s;
    var c = this;
    Wa.load("infowindow", function () {
      c.ob()
    })
  }

  x.lang.xa(rd, x.lang.Ja, "InfoWindow");
  x.extend(rd.prototype, {
    setWidth: function (a) {
      !a && 0 != a || (isNaN(a) || 0 > a) || (0 != a && (220 > a && (a = 220), 730 < a && (a = 730)), this.K.width = a)
    }, setHeight: function (a) {
      !a && 0 != a || (isNaN(a) || 0 > a) || (0 != a && (60 > a && (a = 60), 650 < a && (a = 650)), this.K.height = a)
    }, yO: function (a) {
      !a && 0 != a || (isNaN(a) || 0 > a) || (0 != a && (220 > a && (a = 220), 730 < a && (a = 730)), this.K.maxWidth = a)
    }, Hc: function (a) {
      this.K.title = a
    }, getTitle: function () {
      return this.K.title
    }, Qc: da("content"), Lk: u("content"), tu: function (a) {
      this.K.ZE = a + ""
    }, re: ba(), ct: function () {
      this.K.ct = q
    }, disableAutoPan: function () {
      this.K.ct = t
    }, enableCloseOnClick: function () {
      this.K.qD = q
    }, disableCloseOnClick: function () {
      this.K.qD = t
    }, uh: function () {
      this.K.uh = q
    }, tx: function () {
      this.K.uh = t
    }, show: function () {
      this.Va = q
    }, aa: function () {
      this.Va = t
    }, close: function () {
      this.aa()
    }, ty: function () {
      this.me = q
    }, restore: function () {
      this.me = t
    }, Oc: function () {
      return this.eb()
    }, eb: ea(t), ma: function () {
      if (this.yb && this.yb.ma) return this.yb.ma()
    }, yj: function () {
      return this.K.Ga
    }
  });
  Pa.prototype.Vc = function (a, b) {
    if (a instanceof rd && (b instanceof P || b instanceof L)) {
      var c = this.ba;
      c.an ? c.an.va(b) : (c.an = new V(b, {
        icon: new od(J.ta + "blank.gif", {width: 1, height: 1}),
        offset: new M(0, 0),
        clickable: t
      }), c.an.fS = 1);
      this.Ra(c.an);
      c.an.Vc(a)
    }
  };
  Pa.prototype.Mc = function () {
    var a = this.ba.xb || this.ba.Ll;
    a && a.yb && a.yb.Mc()
  };
  mb.prototype.Vc = function (a) {
    this.map && (this.map.Mc(), a.Va = q, this.map.ba.Ll = a, a.yb = this, x.lang.Ja.call(a, a.da))
  };
  mb.prototype.Mc = function () {
    this.map && this.map.ba.Ll && (this.map.ba.Ll.Va = t, x.lang.qx(this.map.ba.Ll.da), this.map.ba.Ll = s)
  };

  function sd(a, b) {
    mb.call(this);
    this.content = a;
    this.ca = this.map = s;
    b = b || {};
    this.K = {
      width: 0,
      Ga: b.offset || new M(0, 0),
      Bq: {
        backgroundColor: "#fff",
        border: "1px solid #f00",
        padding: "1px",
        whiteSpace: "nowrap",
        font: "12px " + J.fontFamily,
        zIndex: "80",
        MozUserSelect: "none"
      },
      position: b.position || s,
      wj: b.enableMassClear === t ? t : q,
      of: q
    };
    0 > this.K.width && (this.K.width = 0);
    Ob(b.enableClicking) && (this.K.of = b.enableClicking);
    this.point = this.K.position;
    var c = this;
    Wa.load("marker", function () {
      c.ob()
    })
  }

  x.lang.xa(sd, mb, "Label");
  x.extend(sd.prototype, {
    ma: function () {
      return this.xo ? this.xo.ma() : this.map ? fb(this.point, this.map.M.Ma) : this.point
    }, ik: function () {
      return this.xo ? this.xo.ik() : this.point
    }, va: function (a) {
      if ((a instanceof P || a instanceof L) && !this.Lx()) this.point = this.K.position = new P(a.lng, a.lat)
    }, Qc: da("content"), KF: function (a) {
      0 <= a && 1 >= a && (this.K.opacity = a)
    }, Rd: function (a) {
      a instanceof M && (this.K.Ga = new M(a.width, a.height))
    }, yj: function () {
      return this.K.Ga
    }, Td: function (a) {
      a = a || {};
      this.K.Bq = x.extend(this.K.Bq, a)
    }, Ki: function (a) {
      return this.Td(a)
    }, Hc: function (a) {
      this.K.title = a || ""
    }, getTitle: function () {
      return this.K.title
    }, xO: function (a) {
      this.point = (this.xo = a) ? this.K.position = a.ik() : this.K.position = s
    }, Lx: function () {
      return this.xo || s
    }, Lk: u("content")
  });

  function td(a, b) {
    if (0 !== arguments.length) {
      mb.apply(this, arguments);
      b = b || {};
      this.K = {
        jb: a,
        opacity: b.opacity || 1,
        Qp: b.imageURL || "",
        Us: b.displayOnMinLevel || 1,
        wj: b.enableMassClear === t ? t : q,
        Ts: b.displayOnMaxLevel || 19,
        L0: b.stretch || t
      };
      0 === b.opacity && (this.K.opacity = 0);
      var c = this;
      Wa.load("groundoverlay", function () {
        c.ob()
      })
    }
  }

  x.lang.xa(td, mb, "GroundOverlay");
  x.extend(td.prototype, {
    setBounds: function (a) {
      this.K.jb = a
    }, getBounds: function () {
      return this.K.jb
    }, setOpacity: function (a) {
      this.K.opacity = a
    }, getOpacity: function () {
      return this.K.opacity
    }, setImageURL: function (a) {
      this.K.Qp = a
    }, getImageURL: function () {
      return this.K.Qp
    }, setDisplayOnMinLevel: function (a) {
      this.K.Us = a
    }, getDisplayOnMinLevel: function () {
      return this.K.Us
    }, setDisplayOnMaxLevel: function (a) {
      this.K.Ts = a
    }, getDisplayOnMaxLevel: function () {
      return this.K.Ts
    }
  });
  var ud = 3, vd = 4;

  function wd() {
    var a = document.createElement("canvas");
    return !(!a.getContext || !a.getContext("2d"))
  }

  function xd(a, b) {
    var c = this;
    wd() && (a === l && aa(Error("\u6ca1\u6709\u4f20\u5165points\u6570\u636e")), "[object Array]" !== Object.prototype.toString.call(a) && aa(Error("points\u6570\u636e\u4e0d\u662f\u6570\u7ec4")), b = b || {}, mb.apply(c, arguments), c.ia = {ja: a}, c.K = {
      shape: b.shape || ud,
      size: b.size || vd,
      color: b.color || "#fa937e",
      wj: q
    }, this.wB = [], this.ue = [], Wa.load("pointcollection", function () {
      for (var a = 0, b; b = c.wB[a]; a++) c[b.method].apply(c, b.arguments);
      for (a = 0; b = c.ue[a]; a++) c[b.method].apply(c, b.arguments)
    }))
  }

  x.lang.xa(xd, mb, "PointCollection");
  x.extend(xd.prototype, {
    initialize: function (a) {
      this.wB && this.wB.push({method: "initialize", arguments: arguments})
    }, setPoints: function (a) {
      this.ue && this.ue.push({method: "setPoints", arguments: arguments})
    }, setStyles: function (a) {
      this.ue && this.ue.push({method: "setStyles", arguments: arguments})
    }, clear: function () {
      this.ue && this.ue.push({method: "clear", arguments: arguments})
    }, remove: function () {
      this.ue && this.ue.push({method: "remove", arguments: arguments})
    }
  });
  var yd = new od(J.ta + "marker_red_sprite.png", new M(19, 25), {
    anchor: new M(10, 25),
    infoWindowAnchor: new M(10, 0)
  }), zd = new od(J.ta + "marker_red_sprite.png", new M(20, 11), {anchor: new M(6, 11), imageOffset: new M(-19, -13)});

  function V(a, b) {
    mb.call(this);
    b = b || {};
    this.point = a;
    this.Ma = (this.Xq = this.map = s) ? this.map.M.Ma : 5;
    this.K = {
      Ga: b.offset || new M(0, 0),
      Ce: b.icon || yd,
      fl: zd,
      title: b.title || "",
      label: s,
      tK: b.baseZIndex || 0,
      of: q,
      Q7: t,
      LE: t,
      wj: b.enableMassClear === t ? t : q,
      jc: t,
      bO: b.raiseOnDrag === q ? q : t,
      iO: t,
      Ld: b.draggingCursor || J.Ld,
      rotation: b.rotation || 0
    };
    b.icon && !b.shadow && (this.K.fl = s);
    b.enableDragging && (this.K.jc = b.enableDragging);
    Ob(b.enableClicking) && (this.K.of = b.enableClicking);
    var c = this;
    Wa.load("marker", function () {
      c.ob()
    })
  }

  V.hv = kd.Sk(-90) + 1E6;
  V.KG = V.hv + 1E6;
  x.lang.xa(V, mb, "Marker");
  x.extend(V.prototype, {
    Xb: function (a) {
      if (a instanceof od || a instanceof pd) this.K.Ce = a
    }, Ep: function () {
      return this.K.Ce
    }, Yy: function (a) {
      a instanceof od && (this.K.fl = a)
    }, getShadow: function () {
      return this.K.fl
    }, Lj: function (a) {
      this.K.label = a || s
    }, ot: function () {
      return this.K.label
    }, jc: function () {
      this.K.jc = q
    }, Ss: function () {
      this.K.jc = t
    }, ik: u("point"), ma: function () {
      return this.point instanceof P || this.point instanceof L ? this.map ? fb(this.point, this.map.M.Ma) : new P(this.point.lng, this.point.lat) : this.point
    }, va: function (a) {
      if (a instanceof P || a instanceof L) this.point = this.map ? ab(a, this.map.M.Ma) : new L(a.lng, a.lat)
    }, Li: function (a, b) {
      this.K.LE = !!a;
      a && (this.hH = b || 0)
    }, Hc: function (a) {
      this.K.title = a + ""
    }, getTitle: function () {
      return this.K.title
    }, Rd: function (a) {
      a instanceof M && (this.K.Ga = a)
    }, yj: function () {
      return this.K.Ga
    }, xn: da("Xq"), Wy: function (a) {
      this.K.rotation = a
    }, ZL: function () {
      return this.K.rotation
    }
  });

  function Ad(a) {
    this.options = a || {};
    this.l_ = this.options.paneName || "labelPane";
    this.zIndex = this.options.zIndex || 0;
    this.pW = this.options.contextType || "2d"
  }

  Ad.prototype = new kd;
  Ad.prototype.initialize = function (a) {
    this.P = a;
    var b = this.canvas = document.createElement("canvas"), c = this.canvas.getContext(this.pW);
    b.style.cssText = "position:absolute;left:0;top:0;z-index:" + this.zIndex + ";";
    Bd(this);
    Cd(c);
    a.getPanes()[this.l_].appendChild(b);
    var e = this;
    a.addEventListener("resize", function () {
      Bd(e);
      Cd(c);
      e.ob()
    });
    return this.canvas
  };

  function Bd(a) {
    var b = a.P.wb(), a = a.canvas;
    a.width = b.width;
    a.height = b.height;
    a.style.width = a.width + "px";
    a.style.height = a.height + "px"
  }

  function Cd(a) {
    var b = (window.devicePixelRatio || 1) / (a.DV || a.K7 || a.g6 || a.h6 || a.l6 || a.DV || 1), c = a.canvas.width,
      e = a.canvas.height;
    a.canvas.width = c * b;
    a.canvas.height = e * b;
    a.canvas.style.width = c + "px";
    a.canvas.style.height = e + "px";
    a.scale(b, b)
  }

  Ad.prototype.draw = function () {
    var a = this, b = arguments;
    clearTimeout(a.$0);
    a.$0 = setTimeout(function () {
      a.ob.apply(a, b)
    }, 15)
  };
  fa = Ad.prototype;
  fa.ob = function () {
    var a = this.P;
    this.canvas.style.left = -a.offsetX + "px";
    this.canvas.style.top = -a.offsetY + "px";
    this.dispatchEvent("draw");
    this.options.update && this.options.update.apply(this, arguments)
  };
  fa.Ta = u("canvas");
  fa.show = function () {
    this.canvas || this.P.Ra(this);
    this.canvas.style.display = "block"
  };
  fa.aa = function () {
    this.canvas.style.display = "none"
  };
  fa.xq = function (a) {
    this.canvas.style.zIndex = a
  };
  fa.Sk = u("zIndex");

  function Dd(a, b) {
    md.call(this, b);
    b = b || {};
    this.K.Dg = b.fillOpacity ? b.fillOpacity : 0.65;
    this.K.fillColor = "" == b.fillColor ? "" : b.fillColor ? b.fillColor : "#fff";
    this.Sd(a);
    var c = this;
    Wa.load("poly", function () {
      c.ob()
    })
  }

  x.lang.xa(Dd, md, "Polygon");
  x.extend(Dd.prototype, {
    Sd: function (a, b) {
      this.Ro = md.Gx(a).slice(0);
      var c = md.Gx(a).slice(0);
      1 < c.length && c.push(new P(c[0].lng, c[0].lat));
      md.prototype.Sd.call(this, c, b)
    }, Bn: function (a, b) {
      this.Ro[a] && (this.Ro[a] = new P(b.lng, b.lat), this.ja[a] = new P(b.lng, b.lat), 0 == a && !this.ja[0].Vb(this.ja[this.ja.length - 1]) && (this.ja[this.ja.length - 1] = new P(b.lng, b.lat)), this.Nh())
    }, Ze: function () {
      var a = this.Ro;
      0 == a.length && (a = this.ja);
      return a
    }
  });

  function Ed(a, b) {
    md.call(this, b);
    this.fs(a);
    var c = this;
    Wa.load("poly", function () {
      c.ob()
    })
  }

  x.lang.xa(Ed, md, "Polyline");

  function Fd(a, b, c) {
    this.point = a;
    this.Fa = Math.abs(b);
    Dd.call(this, [], c)
  }

  Fd.iF = [0.01, 1.0E-4, 1.0E-5, 4.0E-6];
  x.lang.xa(Fd, Dd, "Circle");
  x.extend(Fd.prototype, {
    initialize: function (a) {
      this.map = a;
      this.ja = this.Iv(this.point, this.Fa);
      this.Nh();
      return s
    }, Hb: function () {
      return this.map ? fb(this.point, this.map.M.Ma) : this.point
    }, Fv: u("point"), Af: function (a) {
      a && (this.point = a)
    }, XL: u("Fa"), Bf: function (a) {
      this.Fa = Math.abs(a)
    }, Iv: function (a, b) {
      if (!a || !b || !this.map) return [];
      for (var c = [], e = b / 6378800, f = Math.PI / 180 * a.lat, g = Math.PI / 180 * a.lng, i = 0; 360 > i; i += 9) {
        var k = Math.PI / 180 * i, m = Math.asin(Math.sin(f) * Math.cos(e) + Math.cos(f) * Math.sin(e) * Math.cos(k)),
          k = new P(((g - Math.atan2(Math.sin(k) * Math.sin(e) * Math.cos(f), Math.cos(e) - Math.sin(f) * Math.sin(m)) + Math.PI) % (2 * Math.PI) - Math.PI) * (180 / Math.PI), m * (180 / Math.PI));
        c.push(k)
      }
      e = c[0];
      c.push(new P(e.lng, e.lat));
      return c
    }
  });
  var Gd = {};

  function Hd(a) {
    this.map = a;
    this.Ij = [];
    this.Df = [];
    this.Ug = [];
    this.SV = 300;
    this.oF = 0;
    this.Mg = {};
    this.qj = {};
    this.Wk = 0;
    this.EE = q;
    this.HW = {};
    this.wo = this.jr(1);
    this.xg = this.jr(2);
    this.ng = this.jr(3);
    this.Vl = this.jr(4);
    a.platform.appendChild(this.wo);
    a.platform.appendChild(this.xg);
    a.platform.appendChild(this.Vl);
    a.platform.appendChild(this.ng);
    var b = 256 * Math.pow(2, 15), c = 3 * b, a = R.Sa(new L(180, 0)).lng, c = c - a, b = -3 * b,
      e = R.Sa(new L(-180, 0)).lng;
    this.qg = a;
    this.hh = e;
    this.Ql = c + (e - b);
    this.ih = a - e
  }

  A.df(function (a) {
    var b = new Hd(a);
    b.za();
    a.ef = b
  });
  x.extend(Hd.prototype, {
    za: function () {
      var a = this, b = a.map;
      b.addEventListener("loadcode", function () {
        a.Yp()
      });
      b.addEventListener("addtilelayer", function (b) {
        a.Te(b)
      });
      b.addEventListener("removetilelayer", function (b) {
        a.fg(b)
      });
      b.addEventListener("setmaptype", function (b) {
        a.Sg(b)
      });
      b.addEventListener("zoomstartcode", function (b) {
        a.Sc(b)
      });
      b.addEventListener("setcustomstyles", function (b) {
        a.su(b.target);
        a.dg(q)
      });
      b.addEventListener("initindoorlayer", function (b) {
        a.AE(b)
      })
    }, Yp: function () {
      var a = this;
      if (x.ga.oa) try {
        document.execCommand("BackgroundImageCache", t, q)
      } catch (b) {
      }
      this.loaded || a.dy();
      a.dg();
      this.loaded || (this.loaded = q, Wa.load("tile", function () {
        a.AQ()
      }))
    }, AE: function (a) {
      this.Tu = new Id(this);
      this.Tu.Te(new Jd(this.map, this.Tu, a.$e))
    }, dy: function () {
      for (var a = this.map.ya().jf, b = 0; b < a.length; b++) {
        var c = new Kd;
        x.extend(c, a[b]);
        this.Ij.push(c);
        c.za(this.map, this.wo)
      }
      this.su()
    }, jr: function (a) {
      var b = F("div");
      b.style.position = "absolute";
      b.style.overflow = "visible";
      b.style.left = b.style.top = "0";
      b.style.zIndex = a;
      return b
    }, Hf: function () {
      this.Wk--;
      var a = this;
      this.EE && (this.map.dispatchEvent(new O("onfirsttileloaded")), this.EE = t);
      0 == this.Wk && (this.$i && (clearTimeout(this.$i), this.$i = s), this.$i = setTimeout(function () {
        if (a.Wk == 0) {
          a.map.dispatchEvent(new O("ontilesloaded"));
          a.EE = q
        }
        a.$i = s
      }, 80))
    }, lE: function (a, b) {
      return "TILE-" + b.da + "-" + a[0] + "-" + a[1] + "-" + a[2]
    }, Zx: function (a) {
      var b = a.Ib;
      b && Hb(b) && b.parentNode.removeChild(b);
      delete this.Mg[a.name];
      a.loaded || (Ld(a), a.Ib = s, a.bn = s)
    }, hM: function (a, b, c) {
      var e = this.map, f = e.ya(), g = e.Za, i = e.Jb, k = f.Wb(g), m = this.XX(), n = m[0], o = m[1], p = m[2],
        v = m[3], w = m[4], c = "undefined" != typeof c ? c : 0, f = f.le(), m = e.da.replace(/^TANGRAM_/, "");
      for (this.Ni ? this.Ni.length = 0 : this.Ni = []; n < p; n++) for (var y = o; y < v; y++) {
        var z = n, C = y;
        this.Ni.push([z, C]);
        z = m + "_" + b + "_" + z + "_" + C + "_" + g;
        this.HW[z] = z
      }
      this.Ni.sort(function (a) {
        return function (b, c) {
          return 0.4 * Math.abs(b[0] - a[0]) + 0.6 * Math.abs(b[1] - a[1]) - (0.4 * Math.abs(c[0] - a[0]) + 0.6 * Math.abs(c[1] - a[1]))
        }
      }([w[0] - 1, w[1] - 1]));
      i = [Math.round(-i.lng / k), Math.round(i.lat / k)];
      n = -e.offsetY + e.height / 2;
      a.style.left = -e.offsetX + e.width / 2 + "px";
      a.style.top = n + "px";
      this.Ue ? this.Ue.length = 0 : this.Ue = [];
      n = 0;
      for (e = a.childNodes.length; n < e; n++) y = a.childNodes[n], y.Hr = t, this.Ue.push(y);
      if (n = this.hn) for (var D in n) delete n[D]; else this.hn = {};
      this.Ve ? this.Ve.length = 0 : this.Ve = [];
      n = 0;
      for (e = this.Ni.length; n < e; n++) {
        D = this.Ni[n][0];
        k = this.Ni[n][1];
        y = 0;
        for (o = this.Ue.length; y < o; y++) if (p = this.Ue[y], p.id == m + "_" + b + "_" + D + "_" + k + "_" + g) {
          p.Hr = q;
          this.hn[p.id] = p;
          break
        }
      }
      n = 0;
      for (e = this.Ue.length; n < e; n++) p = this.Ue[n], p.Hr || this.Ve.push(p);
      this.dG = [];
      y = (f + c) * this.map.M.devicePixelRatio;
      n = 0;
      for (e = this.Ni.length; n < e; n++) D = this.Ni[n][0], k = this.Ni[n][1], v = D * f + i[0] - c / 2, w = (-1 - k) * f + i[1] - c / 2, z = m + "_" + b + "_" + D + "_" + k + "_" + g, o = this.hn[z], p = s, o ? (p = o.style, p.left = v + "px", p.top = w + "px", o.ko || this.dG.push([D, k, o])) : (0 < this.Ve.length ? (o = this.Ve.shift(), o.getContext("2d").clearRect(-c / 2, -c / 2, y, y), p = o.style) : (o = document.createElement("canvas"), p = o.style, p.position = "absolute", p.width = f + c + "px", p.height = f + c + "px", this.CZ() && (p.WebkitTransform = "scale(1.001)"), o.setAttribute("width", y), o.setAttribute("height", y), a.appendChild(o)), o.id = z, p.left = v + "px", p.top = w + "px", -1 < z.indexOf("bg") && (v = "#F3F1EC", this.map.M.BV && (v = this.map.M.BV), p.background = v ? v : ""), this.dG.push([D, k, o])), o.style.visibility = "";
      n = 0;
      for (e = this.Ve.length; n < e; n++) this.Ve[n].style.visibility = "hidden";
      return this.dG
    }, CZ: function () {
      return /M040/i.test(navigator.userAgent)
    }, XX: function () {
      var a = this.map, b = a.ya(), c = b.mM(a.Za), e = a.Jb, f = Math.ceil(e.lng / c), g = Math.ceil(e.lat / c),
        b = b.le(), c = [f, g, (e.lng - f * c) / c * b, (e.lat - g * c) / c * b];
      return [c[0] - Math.ceil((a.width / 2 - c[2]) / b), c[1] - Math.ceil((a.height / 2 - c[3]) / b), c[0] + Math.ceil((a.width / 2 + c[2]) / b), c[1] + Math.ceil((a.height / 2 + c[3]) / b), c]
    }, F0: function (a, b, c, e) {
      var f = this;
      f.I3 = b;
      var g = this.map.ya(), i = f.lE(a, c), k = g.le(), b = [a[0] * k + b[0], (-1 - a[1]) * k + b[1]], m = this.Mg[i];
      if (this.map.ya() !== db && this.map.ya() !== Va) {
        var n = this.xm(a[0], a[2]).offsetX;
        b[0] += n;
        b.a3 = n
      }
      m && m.Ib ? (Fb(m.Ib, b), e && (e = new Q(a[0], a[1]), g = this.map.M.Ee ? this.map.M.Ee.style : "normal", e = c.getTilesUrl(e, a[2], g), m.loaded = t, Md(m, e)), m.loaded ? this.Hf() : Nd(m, function () {
        f.Hf()
      })) : (m = this.qj[i]) && m.Ib ? (c.Mb.insertBefore(m.Ib, c.Mb.lastChild), this.Mg[i] = m, Fb(m.Ib, b), e && (e = new Q(a[0], a[1]), g = this.map.M.Ee ? this.map.M.Ee.style : "normal", e = c.getTilesUrl(e, a[2], g), m.loaded = t, Md(m, e)), m.loaded ? this.Hf() : Nd(m, function () {
        f.Hf()
      })) : (m = k * Math.pow(2, g.Ye() - a[2]), new L(a[0] * m, a[1] * m), e = new Q(a[0], a[1]), g = this.map.M.Ee ? this.map.M.Ee.style : "normal", e = c.getTilesUrl(e, a[2], g), m = new Od(this, e, b, a, c), Nd(m, function () {
        f.Hf()
      }), m.vo(), this.Mg[i] = m)
    }, Hf: function () {
      this.Wk--;
      var a = this;
      0 == this.Wk && (this.$i && (clearTimeout(this.$i), this.$i = s), this.$i = setTimeout(function () {
        if (a.Wk == 0) {
          a.map.dispatchEvent(new O("ontilesloaded"));
          if (za) {
            if (wa && xa && ya) {
              var b = ib(), c = a.map.wb();
              setTimeout(function () {
                Ua(5030, {
                  load_script_time: xa - wa,
                  load_tiles_time: b - ya,
                  map_width: c.width,
                  map_height: c.height,
                  map_size: c.width * c.height
                })
              }, 1E4);
              A.Vq("cus.fire", "time", {z_imgfirstloaded: b - ya})
            }
            za = t
          }
        }
        a.$i = s
      }, 80))
    }, lE: function (a, b) {
      return this.map.ya() === Ta ? "TILE-" + b.da + "-" + this.map.ex + "-" + a[0] + "-" + a[1] + "-" + a[2] : "TILE-" + b.da + "-" + a[0] + "-" + a[1] + "-" + a[2]
    }, Zx: function (a) {
      var b = a.Ib;
      b && (Pd(b), Hb(b) && b.parentNode.removeChild(b));
      delete this.Mg[a.name];
      a.loaded || (Pd(b), Ld(a), a.Ib = s, a.bn = s)
    }, xm: function (a, b) {
      for (var c = 0, e = 6 * Math.pow(2, b - 3), f = e / 2 - 1, g = -e / 2; a > f;) a -= e, c -= this.Ql;
      for (; a < g;) a += e, c += this.Ql;
      c = Math.round(c / Math.pow(2, 18 - b));
      return {offsetX: c, Ag: a}
    }, uC: function (a) {
      for (var b = a.lng; b > this.qg;) b -= this.ih;
      for (; b < this.hh;) b += this.ih;
      a.lng = b;
      return a
    }, vC: function (a, b) {
      for (var c = 256 * Math.pow(2, 18 - b), e = Math.floor(this.qg / c), f = Math.floor(this.hh / c), c = Math.floor(this.Ql / c), g = [], i = 0; i < a.length; i++) {
        var k = a[i], m = k[0], k = k[1];
        if (m >= e) {
          var m = m + c, n = "id_" + m + "_" + k + "_" + b;
          a[n] || (a[n] = q, g.push([m, k]))
        } else m <= f && (m -= c, n = "id_" + m + "_" + k + "_" + b, a[n] || (a[n] = q, g.push([m, k])))
      }
      for (i = 0; i < g.length; i++) a.push(g[i]);
      return a
    }, dg: function (a) {
      var b = this;
      if (b.map.ya() == Ta) Wa.load("coordtrans", function () {
        b.map.Qb || (b.map.Qb = Ta.Kk(b.map.qh), b.map.ex = Ta.JL(b.map.Qb));
        b.FI()
      }, q); else {
        if (a && a) for (var c in this.qj) delete this.qj[c];
        b.FI(a)
      }
    }, FI: function (a) {
      var b = this.map.M.Yf ? this.Df : this.Ij.concat(this.Df), c = b.length, e = this.map, f = e.ya(), g = e.Jb,
        i = e.width, i = e.ya().Wb(e.Za) * i, k = g.lng + i / 2, i = this.EM(g.lng - i / 2, k);
      this.map.ya() !== db && this.map.ya() !== Va && (g = this.uC(g));
      for (var m = 0; m < c; m++) {
        var n = b[m];
        if (n.kc && e.Za < n.kc) break;
        if (n.Zw) {
          k = this.Mb = n.Mb;
          if (a) {
            var o = k;
            if (o && o.childNodes) for (var p = o.childNodes.length, v = p - 1; 0 <= v; v--) p = o.childNodes[v], o.removeChild(p), p = s
          }
          if (this.map.Vd()) {
            this.xg.style.display = "block";
            k.style.display = "none";
            this.map.dispatchEvent(new O("vectorchanged"), {isvector: q});
            continue
          } else k.style.display = "block", this.xg.style.display = "none", this.map.dispatchEvent(new O("vectorchanged"), {isvector: t})
        }
        if (!n.Z2 && !(n.jy && !this.map.Vd() || n.MM && this.map.Vd())) {
          e = this.map;
          f = e.ya();
          p = f.Aj();
          k = e.Za;
          g = e.Jb;
          f == Ta && g.Vb(new L(0, 0)) && (g = e.Jb = p.zi(e.ge, e.Qb));
          var w = f.Wb(k), p = f.mM(k), o = Math.ceil(g.lng / p), y = Math.ceil(g.lat / p), z = f.le(),
            p = [o, y, (g.lng - o * p) / p * z, (g.lat - y * p) / p * z], y = i ? 1.5 * (e.width / 2) : e.width / 2,
            v = p[0] - Math.ceil((y - p[2]) / z), o = p[1] - Math.ceil((e.height / 2 - p[3]) / z),
            y = p[0] + Math.ceil((y + p[2]) / z), C = 0;
          f === Ta && 15 == e.la() && (C = 1);
          f = p[1] + Math.ceil((e.height / 2 + p[3]) / z) + C;
          this.oK = new L(g.lng, g.lat);
          var D = this.Mg, z = -this.oK.lng / w, C = this.oK.lat / w, g = [Math.ceil(z), Math.ceil(C)], w = e.la(), G;
          for (G in D) {
            var E = D[G], B = E.info;
            (B[2] != w || B[2] == w && (v > B[0] || y <= B[0] || o > B[1] || f <= B[1])) && this.Zx(E)
          }
          D = -e.offsetX + e.width / 2;
          E = -e.offsetY + e.height / 2;
          n.Mb && (n.Mb.style.left = Math.ceil(z + D) - g[0] + "px", n.Mb.style.top = Math.ceil(C + E) - g[1] + "px", n.Mb.style.WebkitTransform = "translate3d(0,0,0)");
          z = [];
          for (e.aC = []; v < y; v++) for (C = o; C < f; C++) z.push([v, C]), e.aC.push({x: v, y: C});
          this.map.ya() !== db && this.map.ya() !== Va && (z = this.vC(z, k));
          z.sort(function (a) {
            return function (b, c) {
              return 0.4 * Math.abs(b[0] - a[0]) + 0.6 * Math.abs(b[1] - a[1]) - (0.4 * Math.abs(c[0] - a[0]) + 0.6 * Math.abs(c[1] - a[1]))
            }
          }([p[0] - 1, p[1] - 1]));
          k = Math.ceil(this.qg / (256 * Math.pow(2, 18 - w)));
          p = z.length;
          this.Wk += p;
          for (v = 0; v < p; v++) {
            if (n.IO === q && (o = this.xm(z[v][0], w), o.Ag > k - 1 || o.Ag < -k)) continue;
            this.F0([z[v][0], z[v][1], w], g, n, a)
          }
        }
      }
    }, EM: function (a, b) {
      return a < this.hh || b > this.qg
    }, Te: function (a) {
      var b = this, c = a.target;
      b.map.Vd();
      c.In && this.map.Te(c.In);
      if (c.jy) {
        for (a = 0; a < b.Ug.length; a++) if (b.Ug[a] == c) return;
        Wa.load("vector", function () {
          c.za(b.map, b.xg);
          b.Ug.push(c)
        }, q)
      } else {
        for (a = 0; a < b.Df.length; a++) if (b.Df[a] == c) return;
        c.za(this.map, this.Vl);
        b.Df.push(c)
      }
    }, fg: function (a) {
      a = a.target;
      this.map.Vd();
      a.In && this.map.fg(a.In);
      if (a.jy) for (var b = 0, c = this.Ug.length; b < c; b++) a == this.Ug[b] && this.Ug.splice(b, 1); else {
        b = 0;
        for (c = this.Df.length; b < c; b++) a == this.Df[b] && this.Df.splice(b, 1)
      }
      a.remove()
    }, Sg: function () {
      for (var a = this.Ij, b = 0, c = a.length; b < c; b++) a[b].remove();
      delete this.Mb;
      this.Ij = [];
      this.qj = this.Mg = {};
      this.dy();
      this.dg()
    }, Sc: function () {
      var a = this;
      a.Cd && x.U.aa(a.Cd);
      setTimeout(function () {
        a.dg();
        a.map.dispatchEvent(new O("onzoomend"))
      }, 10)
    }, C7: ba(), su: function (a) {
      var b = this.map.ya();
      if (!this.map.Vd() && (a ? this.map.M.O0 = a : a = this.map.M.O0, a)) for (var c = s, c = "2" == A.Su ? [A.url.proto + A.url.domain.main_domain_cdn.other[0] + "/"] : [A.url.proto + A.url.domain.main_domain_cdn.baidu[0] + "/", A.url.proto + A.url.domain.main_domain_cdn.baidu[1] + "/", A.url.proto + A.url.domain.main_domain_cdn.baidu[2] + "/"], e = 0, f; f = this.Ij[e]; e++) if (f.IO == q) {
        b.m.qc = 18;
        f.getTilesUrl = function (b, e) {
          var f = b.x, f = this.map.ef.xm(f, e).Ag, m = b.y, n = $b("normal"), o = 1;
          this.map.$x() && (o = 2);
          n = "customimage/tile?qt=customimage&x=" + f + "&y=" + m + "&z=" + e + "&udt=" + n + "&scale=" + o + "&ak=" + ra;
          n = a.styleStr ? n + ("&styles=" + encodeURIComponent(a.styleStr)) : n + ("&customid=" + a.style);
          n = c[Math.abs(f + m) % c.length] + n;
          return n = pb(n)
        };
        break
      }
    }
  });

  function Od(a, b, c, e, f) {
    this.bn = a;
    this.position = c;
    this.rv = [];
    this.name = a.lE(e, f);
    this.info = e;
    this.RJ = f.Rt();
    e = F("img");
    Gb(e);
    e.CL = t;
    var g = e.style, a = a.map.ya();
    g.position = "absolute";
    g.border = "none";
    g.width = a.le() + "px";
    g.height = a.le() + "px";
    g.left = c[0] + "px";
    g.top = c[1] + "px";
    g.maxWidth = "none";
    this.Ib = e;
    this.src = b;
    Qd && (this.Ib.style.opacity = 0);
    var i = this;
    this.Ib.onload = function () {
      A.KZ.nR();
      i.loaded = q;
      if (i.bn) {
        var a = i.bn, b = a.qj;
        if (!b[i.name]) {
          a.oF++;
          b[i.name] = i
        }
        if (i.Ib && !Hb(i.Ib) && f.Mb) {
          f.Mb.appendChild(i.Ib);
          if (x.ga.oa <= 6 && x.ga.oa > 0 && i.RJ) i.Ib.style.cssText = i.Ib.style.cssText + (';filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="' + i.src + '",sizingMethod=scale);')
        }
        var c = a.oF - a.SV, e;
        for (e in b) {
          if (c <= 0) break;
          if (!a.Mg[e]) {
            b[e].bn = s;
            var g = b[e].Ib;
            if (g && g.parentNode) {
              g.parentNode.removeChild(g);
              Pd(g)
            }
            g = s;
            b[e].Ib = s;
            delete b[e];
            a.oF--;
            c--
          }
        }
        Qd && new Bb({
          Nc: 20, duration: 200, Ba: function (a) {
            if (i.Ib && i.Ib.style) i.Ib.style.opacity = a * 1
          }, finish: function () {
            i.Ib && i.Ib.style && delete i.Ib.style.opacity
          }
        });
        Ld(i)
      }
    };
    this.Ib.onerror = function () {
      Ld(i);
      if (i.bn) {
        var a = i.bn.map.ya();
        if (a.m.BD) {
          i.error = q;
          i.Ib.src = a.m.BD;
          i.Ib && !Hb(i.Ib) && f.Mb.appendChild(i.Ib)
        }
      }
    };
    e = s
  }

  function Nd(a, b) {
    a.rv.push(b)
  }

  Od.prototype.vo = function () {
    this.Ib.src = 0 < x.ga.oa && 6 >= x.ga.oa && this.RJ ? J.ta + "blank.gif" : "" !== this.src && this.Ib.src == this.src ? this.src + "&t = " + Date.now() : this.src
  };

  function Ld(a) {
    for (var b = 0; b < a.rv.length; b++) a.rv[b]();
    a.rv.length = 0
  }

  function Pd(a) {
    if (a) {
      a.onload = a.onerror = s;
      var b = a.attributes, c, e, f;
      if (b) {
        e = b.length;
        for (c = 0; c < e; c += 1) f = b[c].name, cb(a[f]) && (a[f] = s)
      }
      if (b = a.children) {
        e = b.length;
        for (c = 0; c < e; c += 1) Pd(a.children[c])
      }
    }
  }

  function Md(a, b) {
    a.src = b;
    a.vo()
  }

  var Qd = !x.ga.oa || 8 < x.ga.oa;

  function Kd(a) {
    this.$e = a || {};
    this.rW = this.$e.copyright || s;
    this.r1 = this.$e.transparentPng || t;
    this.Zw = this.$e.baseLayer || t;
    this.zIndex = this.$e.zIndex || 0;
    this.da = Kd.XS++
  }

  Kd.XS = 0;
  x.lang.xa(Kd, x.lang.Ja, "TileLayer");
  x.extend(Kd.prototype, {
    za: function (a, b) {
      this.Zw && (this.zIndex = -100);
      this.map = a;
      if (!this.Mb) {
        var c = F("div"), e = c.style;
        e.position = "absolute";
        e.overflow = "visible";
        e.zIndex = this.zIndex;
        e.left = Math.ceil(-a.offsetX + a.width / 2) + "px";
        e.top = Math.ceil(-a.offsetY + a.height / 2) + "px";
        b.appendChild(c);
        this.Mb = c
      }
    }, remove: function () {
      this.Mb && this.Mb.parentNode && (this.Mb.innerHTML = "", this.Mb.parentNode.removeChild(this.Mb));
      delete this.Mb
    }, Rt: u("r1"), getTilesUrl: function (a, b) {
      if (this.map.ya() !== db && this.map.ya() !== Va) var c = this.map.ef.xm(a.x, b).Ag;
      var e = "";
      this.$e.tileUrlTemplate && (e = this.$e.tileUrlTemplate.replace(/\{X\}/, c), e = e.replace(/\{Y\}/, a.y), e = e.replace(/\{Z\}/, b));
      return e
    }, Km: u("rW"), ya: function () {
      return this.Wa || Qa
    }
  });

  function Rd(a) {
    Kd.call(this, a);
    this.m = a || {};
    this.MM = q;
    if (this.m.predictDate) {
      if (1 > this.m.predictDate.weekday || 7 < this.m.predictDate.weekday) this.m.predictDate = 1;
      if (0 > this.m.predictDate.hour || 23 < this.m.predictDate.hour) this.m.predictDate.hour = 0
    }
    this.ZU = A.url.proto + A.url.domain.traffic + "/traffic/"
  }

  Rd.prototype = new Kd;
  Rd.prototype.za = function (a, b) {
    Kd.prototype.za.call(this, a, b);
    this.P = a
  };
  Rd.prototype.Rt = ea(q);
  Rd.prototype.getTilesUrl = function (a, b) {
    var c = "";
    this.m.predictDate ? c = "HistoryService?day=" + (this.m.predictDate.weekday - 1) + "&hour=" + this.m.predictDate.hour + "&t=" + (new Date).getTime() + "&" : (c = "TrafficTileService?time=" + (new Date).getTime() + "&", c = this.P.M.Yf ? c + "v=016&" : c + "label=web2D&v=016&");
    var c = this.ZU + c + "level=" + b + "&x=" + a.x + "&y=" + a.y, e = 1;
    this.P.$x() && (e = 2);
    return (c + "&scaler=" + e).replace(/-(\d+)/gi, "M$1")
  };
  var Sd = [A.url.proto + A.url.domain.TILES_YUN_HOST[0] + "/georender/gss", A.url.proto + A.url.domain.TILES_YUN_HOST[1] + "/georender/gss", A.url.proto + A.url.domain.TILES_YUN_HOST[2] + "/georender/gss", A.url.proto + A.url.domain.TILES_YUN_HOST[3] + "/georender/gss"],
    Td = A.url.proto + A.url.domain.main_domain_nocdn.baidu + "/style/poi/rangestyle", Ud = 100;

  function vb(a, b) {
    Kd.call(this);
    var c = this;
    this.MM = q;
    try {
      document.createElement("canvas").getContext("2d")
    } catch (e) {
    }
    Pb(a) ? b = a || {} : (c.jo = a, b = b || {});
    b.geotableId && (c.Kf = b.geotableId);
    b.databoxId && (c.jo = b.databoxId);
    var f = A.cd + "geosearch";
    c.fb = {
      WN: b.pointDensity || Ud,
      $Y: f + "/detail/",
      aZ: f + "/v2/detail/",
      lK: b.age || 36E5,
      ku: b.q || "",
      Z0: "png",
      y5: [5, 5, 5, 5],
      GZ: {backgroundColor: "#FFFFD5", borderColor: "#808080"},
      oC: b.ak || ra,
      UO: b.tags || "",
      filter: b.filter || "",
      LO: b.sortby || "",
      rE: b.hotspotName || "tile_md_" + (1E5 * Math.random()).toFixed(0),
      nG: q
    };
    Wa.load("clayer", function () {
      c.Ed()
    })
  }

  vb.prototype = new Kd;
  vb.prototype.za = function (a, b) {
    Kd.prototype.za.call(this, a, b);
    this.P = a
  };
  vb.prototype.getTilesUrl = function (a, b) {
    var c = a.x, e = a.y, f = this.fb,
      c = Sd[Math.abs(c + e) % Sd.length] + "/image?grids=" + c + "_" + e + "_" + b + "&q=" + f.ku + "&tags=" + f.UO + "&filter=" + f.filter + "&sortby=" + f.LO + "&ak=" + this.fb.oC + "&age=" + f.lK + "&page_size=" + f.WN + "&format=" + f.Z0;
    f.nG || (f = (1E5 * Math.random()).toFixed(0), c += "&timeStamp=" + f);
    this.Kf ? c += "&geotable_id=" + this.Kf : this.jo && (c += "&databox_id=" + this.jo);
    return c
  };
  vb.prototype.enableUseCache = function () {
    this.fb.nG = q
  };
  vb.prototype.disableUseCache = function () {
    this.fb.nG = t
  };
  vb.wU = /^point\(|\)$/ig;
  vb.xU = /\s+/;
  vb.zU = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;
  var Vd = {};

  function Wd(a, b) {
    this.bd = a;
    this.DQ = 18;
    this.m = {Lu: 256, Dc: new R};
    x.extend(this.m, b || {})
  }

  var Xd = [0, 0, 0, 8, 7, 7, 6, 6, 5, 5, 4, 3, 3, 3, 2, 2, 1, 1, 0, 0, 0, 0],
    Yd = [512, 2048, 4096, 32768, 65536, 262144, 1048576, 4194304, 8388608],
    Zd = [0, 0, 0, 3, 5, 5, 7, 7, 9, 9, 10, 12, 12, 12, 15, 15, 17, 17, 19, 19, 19, 19],
    $d = [0, 0, 0, 256, 256, 512, 256, 512, 256, 512, 256, 256, 512, 1024, 256, 512, 512, 1024, 512, 1024, 2048, 4096];
  Wd.prototype = {
    getName: u("bd"), le: function (a) {
      return "na" === this.bd ? $d[a] : this.m.Lu
    }, Dp: function (a) {
      return "na" === this.bd ? Zd[a] : a
    }, Aj: function () {
      return this.m.Dc
    }, Wb: function (a) {
      return Math.pow(2, this.DQ - a)
    }, dE: function (a) {
      return "na" === this.bd ? Yd[Xd[a]] : this.Wb(a) * this.le(a)
    }, TX: function (a) {
      a = Math.floor(a);
      return "na" === this.bd ? $d[Zd[a]] : this.m.Lu
    }
  };
  var ae = {
    drawPoly: function (a, b, c, e, f, g) {
      var i = a[1];
      if (i) for (var a = a[6], k = 0; k < i.length; k++) {
        var m = i[k][0], n = f.Cj(m, "polygon", c, g);
        if (n && n.length) for (var o = i[k][1], p = 0; p < o.length; p++) {
          var v = o[p][1];
          f.Oc(v[0], c) && (v["cache" + c] || (v["cache" + c] = f.kn(v[1], c, e, a)), v = v["cache" + c], f.P.Wo(b.canvas.id, v, {
            type: "polygon",
            Yb: m,
            style: n
          }), this.lX(b, v, n, c))
        }
      }
    }, lX: function (a, b, c, e) {
      c = c[0];
      if (!c.Yb || !(6 < e && (71013 === c.Yb || 71012 === c.Yb || 71011 === c.Yb) || 6 === e && (71011 === c.Yb || 71012 === c.Yb) || 5 === e && (71011 === c.Yb || 71013 === c.Yb) || 5 > e && (71012 === c.Yb || 71013 === c.Yb))) {
        a.fillStyle = c.Ax;
        a.beginPath();
        a.moveTo(b[0], b[1]);
        for (var e = 2, f = b.length; e < f; e += 2) a.lineTo(b[e], b[e + 1]);
        a.closePath();
        c.borderWidth && (a.strokeStyle = c.cp, a.lineWidth = c.borderWidth / 2, a.stroke());
        a.fill()
      }
    }, drawGaoqingRoadBorder: function (a, b, c, e, f) {
      var g = a[1];
      if (g) for (var a = a[6], i = 0; i < g.length; i++) {
        var k = g[i][0], m = f.Cj(k, "polygon", c);
        if (m && m.length && m[0].borderWidth) for (var n = g[i][1], o = 0; o < n.length; o++) {
          var p = n[o][1];
          f.Oc(p[0], c) && (p["cache" + c] || (p["cache" + c] = f.kn(p[1], c, e, a)), p = p["cache" + c], f.P.Wo(b.canvas.id, p, {
            type: "polygon",
            Yb: k,
            style: m
          }), this.nX(b, p, m))
        }
      }
    }, drawGaoqingRoadFill: function (a, b, c, e, f) {
      var g = a[1];
      if (g) for (var a = a[6], i = 0; i < g.length; i++) {
        var k = g[i][0], m = f.Cj(k, "polygon", c);
        if (m && m.length) for (var n = g[i][1], o = 0; o < n.length; o++) {
          var p = n[o][1];
          f.Oc(p[0], c) && (p["cache" + c] || (p["cache" + c] = f.kn(p[1], c, e, a)), p = p["cache" + c], f.P.Wo(b.canvas.id, p, {
            type: "polygon",
            Yb: k,
            style: m
          }), this.oX(b, p, m))
        }
      }
    }, nX: function (a, b, c) {
      c = c[0];
      a.beginPath();
      a.moveTo(b[0], b[1]);
      for (var e = 2, f = b.length; e < f; e += 2) a.lineTo(b[e], b[e + 1]);
      a.closePath();
      a.strokeStyle = c.cp;
      a.lineWidth = c.borderWidth / 2;
      a.stroke()
    }, oX: function (a, b, c) {
      a.fillStyle = c[0].Ax;
      a.beginPath();
      a.moveTo(b[0], b[1]);
      for (var c = 2, e = b.length; c < e; c += 2) a.lineTo(b[c], b[c + 1]);
      a.closePath();
      a.fill()
    }
  };
  var be = {
    drawArrow: function (a, b, c, e, f, g) {
      b.lineWidth = 1.5;
      b.lineCap = "butt";
      b.lineJoin = "miter";
      b.strokeStyle = "rgba(153,153,153,1)";
      var i = a[7];
      if (i) {
        a = i[1];
        e = g.kn(i[0], c, e);
        for (i = 0; i < a.length; i++) if (g.Oc(a[i], c)) {
          var k = e[4 * i], m = e[4 * i + 1], n = e[4 * i + 2], o = e[4 * i + 3], p = (k + n) / 2, v = (m + o) / 2,
            n = (k - n) / f, o = (m - o) / f, k = p + n / 2, n = p - n / 2, m = v + o / 2, o = v - o / 2;
          this.dX(b, k, m, n, o)
        }
      }
    }, dX: function (a, b, c, e, f) {
      a.beginPath();
      a.moveTo(b, c);
      a.lineTo(e, f);
      a.stroke();
      c = this.TV([b, c], [e, f]);
      b = c[0];
      c = c[1];
      a.beginPath();
      a.moveTo(b[0], b[1]);
      a.lineTo(c[0], c[1]);
      a.lineTo(e, f);
      a.closePath();
      a.stroke()
    }, TV: function (a, b) {
      var c = b[0] - a[0], e = b[1] - a[1], f = 1.8 * Math.sqrt(c * c + e * e), g = b[0] + 4.8410665352790705 * (c / f),
        f = b[1] + 4.8410665352790705 * (e / f), c = Math.atan2(e, c) + Math.PI;
      return [[g + 4.8410665352790705 * Math.cos(c - 0.3), f + 4.8410665352790705 * Math.sin(c - 0.3)], [g + 4.8410665352790705 * Math.cos(c + 0.3), f + 4.8410665352790705 * Math.sin(c + 0.3)]]
    }
  };
  var ce = {
    drawHregion: function (a, b, c, e, f) {
      var g = a[1];
      if (g) for (var a = a[6], i = 0; i < g.length; i++) {
        var k = g[i][0], m = f.Cj(k, "polygon3d", c);
        if (m && m.length) for (var n = g[i][1], o = 0; o < n.length; o++) {
          var p = n[o][2];
          if (f.Oc(p[0], c)) {
            var v = p[2];
            p["cache" + c] || (p["cache" + c] = f.kn(p[1], c, e, a));
            p = p["cache" + c];
            f.P.Wo(b.canvas.id, p, {type: "polygon", Yb: k, style: m});
            this.mX(b, p, v, m)
          }
        }
      }
    }, mX: function (a, b, c, e) {
      e = e[0];
      if (!(c < e.filter)) {
        a.fillStyle = e.EX;
        a.beginPath();
        a.moveTo(b[0], b[1]);
        for (var c = 2, f = b.length; c < f; c += 2) a.lineTo(b[c], b[c + 1]);
        a.closePath();
        e.borderWidth && (a.strokeStyle = e.cp, a.lineWidth = e.borderWidth / 2, a.stroke());
        a.fill()
      }
    }
  };
  var de = {
    parse: function (a, b, c, e, f) {
      for (var g = e.P, i = g.la(), k = Math.pow(2, 18 - i), m = g.Dc.zi(g.Hb()), n = m.lng, o = m.lat, m = g.wb(), p = m.width, v = m.height, m = [], w = 0; w < a.length; w++) {
        var y = [], z = a[w].X0;
        y.x = z[0];
        y.y = z[1];
        y.P7 = z[2];
        for (var C = (z[0] * c * k - n) / k + p / 2, D = (o - (z[1] + 1) * c * k) / k + v / 2, G = 0; G < a[w].length; G++) a[w][G].SM ? this.SN(a[w][G].SM, z, e, b, c, C, D, i, k, p, v, y) : a[w][G].gZ ? this.SN(a[w][G].gZ, z, e, b, c, C, D, i, k, p, v, y, q, window.C5) : this.r_(a[w][G].HZ, z, e, b, c, C, D, i, k, p, v, y, f);
        m.push(y)
      }
      if (/collision=0/.test(location.search)) {
        a = [];
        for (w = 0; w < m.length; w++) for (G = 0; G < m[w].length; G++) a.push(m[w][G])
      } else a = this.F_(m, e.P.la());
      g.gW();
      for (w = 0; w < a.length; w++) if (c = a[w], !c.Lt) if (G = [c.bg, c.cg, c.bg, c.Di, c.Ci, c.Di, c.Ci, c.cg, c.bg, c.cg], c.style && g.Wo("poi", G, {
        type: "polygon",
        Yb: c.style.Yb,
        style: c.style
      }), "fixed" === c.type) {
        G = t;
        c.Ce && (c.style && 4 === c.direction) && (G = q);
        if (c.Ce) if (G) {
          var E = this;
          this.at(b, c, e, G, function (a) {
            for (var c = 0; c < a.Cf.length; c++) E.iL(b, a.Cf[c].he, a.Cf[c].ie, a.Cf[c].text, a.style, e)
          })
        } else this.at(b, c, e);
        if (c.style && !G) for (G = 0; G < c.Cf.length; G++) this.iL(b, c.Cf[G].he, c.Cf[G].ie, c.Cf[G].text, c.style, e)
      } else if ("line" === c.type) for (G = 0; G < c.DP.length; G++) f = c.DP[G], de.gX(b, f.he, f.ie, f.vV, f.BP, f.width, f.height, c.style, e);
      return m
    }, SN: function (a, b, c, e, f, g, i, k, m, n, o, p, v, w) {
      if (a = a[1]) for (b = 0; b < a.length; b++) {
        var y = a[b], z = y[0], C = c.Cj(z, "point", k, w), z = c.Cj(z, "pointText", k, w), y = y[1], D = s, G = 100,
          E = 0, B = 0;
        C && C[0] && (C = C[0], D = C.Ce, G = C.zoom || 100);
        z = z && z[0] ? z[0] : s;
        for (C = 0; C < y.length; C++) {
          var H = y[C][4];
          if (H && c.Oc(H[2], k)) {
            var I = Math.round(H[0] / 100) / m + g, N = f - Math.round(H[1] / 100) / m + i;
            if (v || !(-50 > I || -50 > N || I > n + 50 || N > o + 50)) {
              var T = H[7] || "",
                ca = {type: "fixed", uid: H[3] || "", name: T, Ny: H[4], Gt: s, Cf: [], Ay: [I, N], style: z};
              if (D) {
                var Z = window.iconSetInfo_high[D] || window.iconSetInfo_high["MapRes/" + D];
                if (!Z) {
                  var ha = D.charCodeAt(0);
                  48 <= ha && 57 >= ha && (Z = window.iconSetInfo_high["_" + D])
                }
                Z && (E = Z[2], B = Z[3], E = E / 2 * G / 100, B = B / 2 * G / 100, ca.Gt = {
                  he: I - E / 2,
                  ie: N - B / 2,
                  width: E,
                  height: B
                }, ca.Ce = D)
              }
              if (z) {
                H = H[5];
                "number" !== typeof H && (H = 0);
                var va = Z = 0, ha = (z.fontSize || 12) / 2, Ha = 0.2 * ha;
                e.font = de.Fx(z, c);
                var T = T.split("\\"), ua = T.length;
                ca.direction = H;
                for (var Za = 0; Za < ua; Za++) {
                  var Ne = T[Za], bd = e.measureText(Ne).width;
                  switch (H) {
                    case 3:
                      va = N - ha / 2 * ua - Ha * (ua - 1) / 2;
                      Z = I - bd - E / 2;
                      va = va + ha * Za + Ha * Za;
                      break;
                    case 1:
                      va = N - ha / 2 * ua - Ha * (ua - 1) / 2;
                      Z = I + E / 2;
                      va = va + ha * Za + Ha * Za;
                      break;
                    case 2:
                      va = N - B / 2 - ha * ua - Ha * (ua - 1) - Ha;
                      Z = I - bd / 2;
                      va = va + ha * Za + Ha * Za;
                      break;
                    case 0:
                      va = N + B / 2 + Ha / 2;
                      Z = I - bd / 2;
                      va = va + ha * Za + Ha * Za;
                      break;
                    case 4:
                      va = N - ha / 2 * ua - Ha * (ua - 1) / 2, Z = I - bd / 2, va = va + ha * Za + Ha * Za
                  }
                  ca.Cf.push({he: Z, ie: va, width: bd, height: ha, text: Ne})
                }
              }
              p.push(ca)
            }
          }
        }
      }
    }, r_: function (a, b, c, e, f, g, i, k, m, n, o, p, v) {
      b = a[7].length;
      if ((n = c.Cj(a[0], "pointText", k)) && n.length) {
        n = n[0];
        e.font = de.Fx(n, c);
        var o = n.fontSize / 2, w = a[1], y = a[2];
        if (y) {
          for (var z = y.split("").length, C = a[4], D = C.slice(0, 2), G = 2; G < C.length; G += 2) D[G] = D[G - 2] + C[G], D[G + 1] = D[G - 1] + C[G + 1];
          for (G = 2; G < C.length; G += 2) 0 === G % (2 * z) || 1 === G % (2 * z) || (D[G] = D[G - 2] + C[G] / v, D[G + 1] = D[G - 1] + C[G + 1] / v);
          for (v = 0; v < b; v++) if (c.Oc(a[7][v], k)) {
            var G = [], E = l, B = l, H = l, I = l, N = y.split("");
            a[6][v] && N.reverse();
            for (var C = 2 * v * z, C = D.slice(C, C + 2 * z), T = 0; T < z; T++) {
              var ca = a[5][z * v + T], Z = C[2 * T] / 100 / m + g, ha = f - C[2 * T + 1] / 100 / m + i, va = N[T],
                Ha = e.measureText(va).width;
              if (E === l) E = Z - Ha / 2, B = ha - o / 2, H = E + Ha, I = B + o; else {
                var ua = Z - Ha / 2, Za = ha - o / 2;
                ua < E && (E = ua);
                Za < B && (B = Za);
                ua + Ha > H && (H = ua + Ha);
                Za + o > I && (I = Za + o)
              }
              G.push({BP: va, he: Z, ie: ha, vV: ca, width: Ha, height: o})
            }
            p.push({type: "line", Ny: w, style: n, DP: G, bg: E, cg: B, Ci: H, Di: I})
          }
        }
      }
    }, at: function (a, b, c, e, f) {
      var g = b.Ce;
      if ("lanche" !== g) if (de.ay[g]) this.fL(a, b, de.ay[g], e, f); else if (c = c.QL(g)) {
        var i = new Image;
        i.setAttribute("crossOrigin", "anonymous");
        var k = this;
        i.onload = function () {
          de.ay[g] = this;
          k.fL(a, b, this, e, f);
          i.onload = s
        };
        i.src = c
      }
    }, fL: function (a, b, c, e, f) {
      var g = b.Gt, i = g.he, k = g.ie, m = s, n = s, o = q, p = b.style ? b.style.Yb : s;
      if (b.style && 62203 === p) {
        for (var v = n = m = 0; v < b.Cf.length; v++) m < b.Cf[v].width && (m = b.Cf[v].width), n += 20;
        m = Math.ceil(m) + 10
      }
      e && 519 === p && (o = t);
      m !== s && n !== s ? this.kX(a, b, c, 8, m, n) : e && o ? (m = Math.ceil(b.Cf[0].width) + 6, this.cX(a, b, c, 12, m, c.height / 2)) : a.drawImage(c, i, k, g.width, g.height);
      f && f(b)
    }, kX: function (a, b, c, e, f, g) {
      var i = b.Ay[0] - f / 2, b = b.Ay[1] - g / 2;
      0 < navigator.userAgent.indexOf("iPhone") && (b += 1);
      var k = e / 2;
      a.drawImage(c, 0, 0, e, e, i, b, k, k);
      a.drawImage(c, e, 0, 1, e, i + k, b, f - 2 * k, k);
      a.drawImage(c, c.width - e, 0, e, e, i + f - k, b, k, k);
      a.drawImage(c, 0, e, e, 1, i, b + k, k, g - 2 * k);
      a.drawImage(c, e, e, 1, 1, i + k, b + k, f - 2 * k, g - 2 * k);
      a.drawImage(c, c.width - e, e, e, 1, i + f - k, b + k, k, g - 2 * k);
      a.drawImage(c, 0, c.height - e, e, e, i, b + g - k, k, k);
      a.drawImage(c, e, c.height - e, 1, e, i + k, b + g - k, f - 2 * k, k);
      a.drawImage(c, c.width - e, c.height - e, e, e, i + f - k, b + g - k, k, k)
    }, cX: function (a, b, c, e, f, g) {
      var i = b.Ay[0] - f / 2, b = b.Ay[1] - g / 2, g = e / 2;
      a.drawImage(c, 0, 0, e, c.height, i, b, g, c.height / 2);
      a.drawImage(c, e, 0, 1, c.height, i + g, b, f - 2 * g, c.height / 2);
      a.drawImage(c, c.width - e, 0, e, c.height, i + f - g, b, g, c.height / 2)
    }, gX: function (a, b, c, e, f, g, i, k, m) {
      a.font = de.Fx(k, m);
      a.fillStyle = k.zL;
      g /= 2;
      i /= 2;
      a.save();
      a.translate(b, c);
      a.rotate(-e / 180 * Math.PI);
      0 < k.Tx && (a.lineWidth = k.Tx, a.strokeStyle = k.rM, a.strokeText(f, -g, -i));
      a.fillText(f, -g, -i);
      a.restore()
    }, iL: function (a, b, c, e, f, g) {
      a.font = de.Fx(f, g);
      a.fillStyle = f.zL;
      0 < f.Tx && (a.lineWidth = f.Tx, a.strokeStyle = f.rM, a.strokeText(e, b, c));
      a.fillText(e, b, c)
    }, Fx: function (a, b) {
      var c = a.fontSize / 2, e = 10 * a.fontWeight;
      return e = b.GE ? e + " bold" + (" " + c + "px") + ' arial, "PingFang SC", sans-serif' : e + (" " + c + "px") + " arial, sans-serif"
    }, F_: function (a, b) {
      var c = [], e = 0;
      5 === b && (e = 1);
      a.sort(function (a, b) {
        return a.x * a.y < b.x * b.y ? -1 : 1
      });
      for (var f = 0, g = a.length; f < g; f++) for (var i = a[f], k = 0, m = i.length; k < m; k++) {
        var n = i[k], o = l, p = l, v = l, w = l;
        if ("fixed" === n.type) {
          var y = n.Gt, z = n.Cf;
          y && (o = y.he, p = y.ie, v = y.he + y.width, w = y.ie + y.height);
          for (y = 0; y < z.length; y++) {
            var C = z[y];
            o !== l ? (C.he < o && (o = C.he), C.ie < p && (p = C.ie), C.he + C.width > v && (v = C.he + C.width), C.ie + C.height > w && (w = C.ie + C.height)) : (o = C.he, p = C.ie, v = C.he + C.width, w = C.ie + C.height)
          }
        } else "line" === n.type ? (o = n.bg, p = n.cg, v = n.Ci, w = n.Di) : "biaopai" === n.type && (w = n.B6, o = w.he, p = w.ie, v = w.he + w.width, w = w.ie + w.height);
        o !== l && (n.bg = o, n.cg = p, n.Ci = v, n.Di = w, c.push(n))
      }
      c.sort(function (a, b) {
        return b.Ny - a.Ny || b.bg - a.bg || b.cg - a.cg
      });
      f = 0;
      for (g = c.length; f < g; f++) {
        m = c[f];
        m.Lt = t;
        m.rK = [];
        for (k = f + 1; k < g; k++) i = c[k], m.Ci - e < i.bg || (m.bg > i.Ci - e || m.Di - e < i.cg || m.cg > i.Di - e) || m.rK.push(k)
      }
      f = 0;
      for (g = c.length; f < g; f++) if (k = c[f], k.Lt === t) {
        e = k.rK;
        k = 0;
        for (m = e.length; k < m; k++) c[e[k]].Lt = q
      }
      return c
    }, ay: {}
  };
  var ee = ["round", "butt", "square"], fe = ["miter", "round", "bevel"], ge = {
    daojiao: [{stroke: "#FF6600", tb: 1, rb: "round", sb: "round", Zc: [4, 3]}],
    daojiao_bai: [{stroke: "#f5f3f0", tb: 1, rb: "round", sb: "round", Zc: [4, 3]}],
    junhuoxian: [],
    lundu: [{stroke: "#5c91c5", tb: 1, rb: "round", sb: "round", Zc: [10, 11]}],
    shengjie: [],
    weidingguojie: [{stroke: "#aea08a", tb: 1, rb: "round", sb: "round", Zc: [4, 3]}],
    weidingguojie_guowai: [{stroke: "#a29e96", tb: 2, rb: "round", sb: "round", Zc: [4, 3]}],
    weidingguojie_guonei: [],
    weidingshengjie_guowai: []
  }, he = {
    weidingshengjie_guowai: [{stroke: "#737373", tb: 1, rb: "round", sb: "round", Zc: [4, 3]}],
    junhuoxian: [{stroke: "#DB7093", tb: 1.5, rb: "round", sb: "round", Zc: [4, 3]}],
    shengjie: [{stroke: "#737373", tb: 1, rb: "round", sb: "round", Zc: [10, 4, 5, 4]}],
    weidingguojie_guonei: [{stroke: "#b2a471", tb: 2, rb: "round", sb: "round", Zc: [4, 3]}]
  }, ie = {};

  function je(a, b, c) {
    if (/^tielu|^MapRes\/tielu/.test(a)) {
      if ("off" === window[c + "zoomFrontStyle"][b].bmapRailwayVisibility) return [];
      var e = "#ffffff", f = "#949494";
      window[c + "zoomFrontStyle"] && (window[c + "zoomFrontStyle"][b] && window[c + "zoomFrontStyle"][b].bmapRailwayStrokeColor) && (e = window[c + "zoomFrontStyle"][b].bmapRailwayStrokeColor);
      window[c + "zoomFrontStyle"] && (window[c + "zoomFrontStyle"][b] && window[c + "zoomFrontStyle"][b].bmapRailwayFillColor) && (f = window[c + "zoomFrontStyle"][b].bmapRailwayFillColor);
      if (4 <= b && 9 >= b || 10 <= b && 16 >= b) return [{
        stroke: e,
        tb: 1.5,
        rb: "butt",
        sb: "round",
        Zc: [10, 11]
      }, {stroke: f, tb: 2, rb: "round", sb: "round"}];
      if (17 <= b && 18 >= b) return [{stroke: e, tb: 2.5, rb: "butt", sb: "round", Zc: [15, 16]}, {
        stroke: f,
        tb: 5,
        rb: "round",
        sb: "round"
      }];
      if (19 <= b && 20 >= b) return [{stroke: e, tb: 4.5, rb: "butt", sb: "round", Zc: [25, 26]}, {
        stroke: f,
        tb: 5,
        rb: "round",
        sb: "round"
      }]
    } else if (0 === a.indexOf("ditie_zj") || 0 === a.indexOf("MapRes/ditie_zj")) {
      if (12 <= b && 16 >= b) return [{stroke: "#868686", tb: 1, rb: "round", sb: "round", Zc: [7, 4]}];
      if (17 <= b && 18 >= b || 19 <= b && 20 >= b) return [{
        stroke: "#6e6e6e",
        tb: 1,
        rb: "round",
        sb: "round",
        Zc: [7, 4]
      }]
    } else if (/^tongdaomian|^MapRes\/tongdaomian/.test(a)) {
      if (17 === b) return [{stroke: "#e5e5e5", tb: 4, rb: "square", sb: "round"}, {
        stroke: "#a8a8a8",
        tb: 6,
        rb: "square",
        sb: "round"
      }];
      if (18 === b) return [{stroke: "#e5e5e5", tb: 6, rb: "square", sb: "round"}, {
        stroke: "#a8a8a8",
        tb: 8,
        rb: "square",
        sb: "round"
      }];
      if (19 <= b && 21 >= b) return [{stroke: "#e5e5e5", tb: 8, rb: "square", sb: "round"}, {
        stroke: "#a8a8a8",
        tb: 10,
        rb: "square",
        sb: "round"
      }]
    } else if (/^jietizhongduan|^dixiatongdaojieti|^MapRes\/jietizhongduan|^MapRes\/dixiatongdaojieti/.test(a)) {
      if (17 === b) return [{stroke: "#e5e5e5", tb: 4, rb: "butt", sb: "round", Zc: [2, 1]}, {
        stroke: "#bebebe",
        tb: 6,
        rb: "butt",
        sb: "round"
      }];
      if (18 === b) return [{stroke: "#e5e5e5", tb: 6, rb: "butt", sb: "round", Zc: [3, 1]}, {
        stroke: "#bebebe",
        tb: 8,
        rb: "butt",
        sb: "round"
      }];
      if (19 <= b && 21 >= b) return [{
        stroke: "#e5e5e5",
        tb: 8,
        rb: "butt",
        sb: "round",
        Zc: [4, 2]
      }, {stroke: "#bebebe", tb: 10, rb: "butt", sb: "round"}]
    } else if (/^guojietianqiao|^MapRes\/guojietianqiao/.test(a)) return 18 === b ? [{
      stroke: "#ffffff",
      tb: 6,
      rb: "butt",
      sb: "round",
      Zc: [4, 2]
    }, {stroke: "#bebebe", tb: 8, rb: "butt", sb: "round"}] : [{
      stroke: "#ffffff",
      tb: 8,
      rb: "butt",
      sb: "round",
      Zc: [4, 2]
    }, {stroke: "#bebebe", tb: 10, rb: "butt", sb: "round"}];
    return ge[a] || ge[a.replace("MapRes/", "")]
  }

  var ke = {
    drawLink: function (a, b, c, e, f) {
      this.da = f.P.da;
      var g = a[1];
      g && (a = a[6], this.jP(g, c, e, b, a, f, q), this.jP(g, c, e, b, a, f, t))
    }, jP: function (a, b, c, e, f, g, i) {
      for (var k = 0; k < a.length; k++) {
        var m = a[k][0], n = g.Cj(m, "line", b);
        if (n && n.length && (!i || n[0].borderWidth)) if (!n[0].Im || je(n[0].Im, b, this.da)) for (var o = a[k][1], p = 0; p < o.length; p++) {
          var v = o[p][3];
          g.Oc(v[0], b) && (v["cache" + b] || (v["cache" + b] = g.kn(v[1], b, c, f)), v = v["cache" + b], g.P.Wo(e.canvas.id, v, {
            type: "polyline",
            Yb: m,
            style: n
          }), this.hX(e, v, n, i, b))
        }
      }
    }, drawSingleTexture: function (a, b, c, e, f) {
      var g = a[1];
      if (g) for (var a = a[6], i = 0; i < g.length; i++) {
        var k = f.Cj(g[i][0], "line", c);
        if (k && k.length) for (var m = g[i][1], n = 0; n < m.length; n++) {
          var o = m[n][11];
          if (f.Oc(o[0], c)) {
            var p;
            o["cache" + c] || (o["cache" + c] = f.kn(o[1], c, e, a));
            p = o["cache" + c];
            o = o[3];
            o *= Math.pow(2, c - f.X1[c].Tc);
            this.iX(b, p, k, o, f)
          }
        }
      }
    }, iX: function (a, b, c, e, f) {
      var g = c[0].Im, i = this;
      if (ie[g]) i.at(b, e, a, ie[g]); else if (c = f.QL(g)) {
        var k = new Image;
        k.onload = function () {
          ie[g] = k;
          i.at(b, e, a, k);
          k.onload = s
        };
        k.src = c
      }
    }, at: function (a, b, c, e) {
      var f = [a[0], a[1]], g = [a[2], a[3]], a = g[0] - f[0], g = g[1] - f[1], f = [f[0] + a / 2, f[1] + g / 2],
        i = Math.sqrt(a * a + g * g), b = b / 10, a = Math.atan2(g, a);
      c.save();
      c.translate(f[0], f[1]);
      c.rotate(Math.PI / 2 + a);
      c.drawImage(e, -b / 2, -i / 2, b, i);
      c.restore()
    }, hX: function (a, b, c, e, f) {
      c = c[0];
      if (!e && c.Im) {
        var f = je(c.Im, f, this.da), g = he[c.Im] || he[c.Im.replace("MapRes/", "")];
        if (g) {
          this.jL(a, b, c, g, q);
          return
        }
        if (f) {
          this.jL(a, b, c, f, t);
          return
        }
      }
      a.beginPath();
      a.moveTo(b[0], b[1]);
      f = 2;
      for (g = b.length; f < g; f += 2) a.lineTo(b[f], b[f + 1]);
      c.borderWidth && e ? (a.strokeStyle = c.cp, a.lineCap = ee[c.MV], a.lineJoin = fe[1], a.lineWidth = c.borderWidth / 2, a.stroke()) : e || (a.strokeStyle = c.Ax, a.lineCap = ee[c.DX], a.lineJoin = fe[1], a.lineWidth = c.vL / 2, a.stroke())
    }, jL: function (a, b, c, e, f) {
      if (c = e[1]) {
        a.strokeStyle = c.stroke;
        a.lineCap = c.rb;
        a.lineJoin = c.sb;
        a.lineWidth = c.tb;
        a.beginPath();
        a.moveTo(b[0], b[1]);
        for (var c = 2, g = b.length; c < g; c += 2) a.lineTo(b[c], b[c + 1]);
        a.stroke()
      }
      if (e = e[0]) if (e.Zc) f ? this.jX(a, b, e) : this.fX(a, b, e); else {
        a.strokeStyle = e.stroke;
        a.lineCap = e.rb;
        a.lineJoin = e.sb;
        a.lineWidth = e.tb;
        a.beginPath();
        a.moveTo(b[0], b[1]);
        c = 2;
        for (g = b.length; c < g; c += 2) a.lineTo(b[c], b[c + 1]);
        a.stroke()
      }
    }, jX: function (a, b, c) {
      a.strokeStyle = c.stroke;
      a.lineCap = c.rb;
      a.lineJoin = c.sb;
      a.lineWidth = c.tb;
      a.setLineDash(c.Zc);
      a.beginPath();
      for (c = 0; c < b.length - 2; c += 2) a.lineTo(b[c], b[c + 1]);
      a.stroke();
      a.setLineDash([])
    }, fX: function (a, b, c) {
      a.strokeStyle = c.stroke;
      a.lineCap = c.rb;
      a.lineJoin = c.sb;
      a.lineWidth = c.tb;
      var e = q, c = c.Zc[0];
      a.beginPath();
      for (var f = 0; f < b.length - 2; f += 2) {
        var g = b[f], i = b[f + 1], k = b[f + 2] - g, m = b[f + 3] - i, n = 0 !== k ? m / k : 0 < m ? 1E15 : -1E15,
          m = Math.sqrt(k * k + m * m), o = c;
        for (a.moveTo(g, i); 0.1 <= m;) {
          o > m && (o = m);
          var p = Math.sqrt(o * o / (1 + n * n));
          0 > k && (p = -p);
          g += p;
          i += n * p;
          a[e ? "lineTo" : "moveTo"](g, i);
          m -= o;
          e = !e
        }
      }
      a.stroke()
    }
  };
  var le = 3, ne = 4, oe = 7, pe = 8, qe = 15, re = 16, se = {}, te = {}, ue = {}, ve, we = {
    3: {start: 3, Tc: 3},
    4: {start: 4, Tc: 5},
    5: {start: 4, Tc: 5},
    6: {start: 6, Tc: 7},
    7: {start: 6, Tc: 7},
    8: {start: 8, Tc: 9},
    9: {start: 8, Tc: 9},
    10: {start: 10, Tc: 10},
    11: {start: 11, Tc: 12},
    12: {start: 11, Tc: 12},
    13: {start: 11, Tc: 12},
    14: {start: 14, Tc: 15},
    15: {start: 14, Tc: 15},
    16: {start: 16, Tc: 17},
    17: {start: 16, Tc: 17},
    18: {start: 18, Tc: 19},
    19: {start: 18, Tc: 19},
    20: {start: 18, Tc: 19},
    21: {start: 18, Tc: 19}
  };

  function xe(a) {
    this.P = a;
    this.Lc = a.M.devicePixelRatio;
    this.X1 = we
  }

  xe.prototype = {
    gD: function (a, b, c, e, f, g, i, k, m) {
      this.P.PO = {};
      var n = this, o = n.P.da;
      m || (m = 0);
      if (!window[o + "StyleBody"] && 100 > m) setTimeout(function () {
        n.gD(a, b, c, e, f, g, i, k, m + 1)
      }, 100); else {
        ve || (ve = k);
        var p = b.getContext("2d"), v = b.parentNode;
        v.removeChild(b);
        p.clearRect(0, 0, g, g);
        v.appendChild(b);
        v = this.Lc;
        1 < v && !b._scale && (p.scale(v, v), b._scale = q);
        p.fillStyle = this.RN("#F5F3F0");
        window[o + "zoomFrontStyle"][f].bmapLandColor && (p.fillStyle = this.RN(window[o + "zoomFrontStyle"][f].bmapLandColor));
        o = b.style.width;
        b.style.width = "0px";
        b.style.width = o;
        (o = ye.MY(c, g, i)) ? p.fillRect(o[0], o[1], o[2], o[3]) : p.fillRect(0, 0, g, g);
        if (a[0]) for (o = 0; o < a[0].length; o++) v = a[0][o], v[0] === oe && ae.drawPoly(v, p, f, g, this);
        17 <= this.P.la() ? (n.hL(a, p, f, g, i, c, e), b.ko = q) : setTimeout(function () {
          if (!b.JH) {
            n.hL(a, p, f, g, i, c, e);
            b.ko = q
          }
        }, 1)
      }
    }, hL: function (a, b, c, e) {
      var f = this.P.da;
      if (a[0]) for (var g = 0; g < a[0].length; g++) {
        var i = a[0][g], k = i[0];
        k === ne ? ke.drawLink(i, b, c, e, this) : k === re ? ke.drawLink(i, b, c, e, this) : k === qe ? (ae.drawGaoqingRoadBorder(i, b, c, e, this), ae.drawGaoqingRoadFill(i, b, c, e, this)) : 18 === k ? window[f + "zoomFrontStyle"] && (window[f + "zoomFrontStyle"][c] && "off" !== window[f + "zoomFrontStyle"][c].bmapRoadarrowVisibility) && be.drawArrow(i, b, c, e, Math.pow(2, c - we[c].Tc), this) : k === pe ? ce.drawHregion(i, b, c, e, this) : 19 === k && ke.drawSingleTexture(i, b, c, e, this)
      }
    }, gL: function (a, b, c, e, f, g, i) {
      var k = this, m = k.P.da;
      i || (i = 0);
      !window[m + "StyleBody"] && 100 > i ? setTimeout(function () {
        k.gL(a, b, c, e, f, g, i + 1)
      }, 100) : (ve || (ve = b), a.y_ = de.parse(a, c, e, this, f))
    }, Cj: function (a, b, c, e) {
      var f = a + "-" + b + "-" + c;
      if (e) return se[f] || (se[f] = this.Hg(a, b, c, e)), se[f];
      this.P.PO[f] = this.Hg(a, b, c);
      return this.P.PO[f]
    }, Hg: function (a, b, c, e) {
      var f = this.P.da, g;
      g = e || window[f + "_bmap_baseFs"];
      f = window[f + "StyleBody"];
      e = g[2];
      if ("arrow" === b) return this.n_(e[2]);
      switch (b) {
        case "point":
          e = e[0];
          f = f[0] || {};
          break;
        case "pointText":
          e = e[1];
          f = f[1] || {};
          break;
        case "line":
          e = e[3];
          f = f[3] || {};
          break;
        case "polygon":
          e = e[4];
          f = f[4] || {};
          break;
        case "polygon3d":
          e = e[5], f = f[5] || {}
      }
      var i = [], c = g[1][c - 1][0][a];
      if (!c) return i;
      for (g = 0; g < c.length; g++) {
        var k = f[c[g]] || e[c[g]];
        if (k) {
          switch (b) {
            case "polygon":
              k = this.w_(k, a);
              break;
            case "line":
              k = this.s_(k, a);
              break;
            case "pointText":
              k = this.u_(k, a);
              break;
            case "point":
              k = this.t_(k, a);
              break;
            case "polygon3d":
              k = this.v_(k, a)
          }
          k.i7 = c[g];
          i[i.length] = k
        }
      }
      return i
    }, u_: function (a, b) {
      return {
        Yb: b,
        zL: this.Pg(a[0]),
        rM: this.Pg(a[1]),
        y3: this.Pg(a[2]),
        fontSize: a[3],
        Tx: a[4],
        fontWeight: a[5],
        fontStyle: a[6],
        PW: a[7]
      }
    }, t_: function (a, b) {
      return {Yb: b, Ny: a[0], A7: a[1], Ce: a[2], cZ: a[3], j6: a[4], PW: a[5], zoom: a[6]}
    }, s_: function (a, b) {
      return {
        Yb: b,
        cp: this.Pg(a[0]),
        Ax: this.Pg(a[1]),
        borderWidth: a[2],
        vL: a[3],
        MV: a[4],
        DX: a[5],
        q5: a[6],
        r5: a[7],
        s5: a[8],
        K5: a[9],
        L5: a[10],
        NV: a[11],
        Im: a[12],
        OV: a[13],
        m4: a[14],
        I5: a[15],
        o5: a[16],
        i6: a[17],
        N6: a[18]
      }
    }, w_: function (a, b) {
      return {
        Yb: b,
        Ax: this.Pg(a[0]),
        cp: this.Pg(a[1]),
        borderWidth: a[2],
        NV: a[3],
        OV: a[4],
        J7: a[5],
        n5: a[6],
        m7: a[7],
        n7: this.Pg(a[8])
      }
    }, v_: function (a, b) {
      return {
        Yb: b,
        filter: a[0],
        cO: a[1],
        p5: a[2],
        borderWidth: a[3],
        cp: this.Pg(a[4]),
        EX: this.Pg(a[5]),
        l4: this.Pg(a[6]),
        z6: a[7]
      }
    }, n_: function (a) {
      for (var b in a) return a = a[b], {color: this.Pg(a[0]), cZ: a[1], Ce: a[2]}
    }, Pg: function (a) {
      var b = a;
      if (ue[b]) return ue[b];
      a >>>= 0;
      ue[b] = "rgba(" + (a & 255) + "," + (a >> 8 & 255) + "," + (a >> 16 & 255) + "," + (a >> 24 & 255) / 255 + ")";
      return ue[b]
    }, RN: function (a) {
      a = a.replace("#", "");
      6 === a.length && (a += "ff");
      for (var b = "rgba(", c = 0; 8 > c; c += 2) b = 6 > c ? b + (parseInt(a.slice(c, c + 2), 16) + ",") : b + (parseInt(a.slice(c, c + 2), 16) / 255 + ")");
      return b
    }, Oc: function (a, b) {
      var c;
      te[a] || (c = a.toString(2), 8 > c.length && (c = Array(8 - c.length + 1).join("0") + c), te[a] = c);
      c = te[a];
      return "1" === c[b - we[b].start]
    }, kn: function (a, b, c) {
      var e = [], b = Math.pow(2, b - we[b].Tc) / 100, f = a[0] * b, g = a[1] * b;
      e[e.length] = f;
      e[e.length] = c - g;
      for (var i = 2; i < a.length; i += 2) f += a[i] * b, g += a[i + 1] * b, e[e.length] = f, e[e.length] = c - g;
      return e
    }, QL: function (a) {
      if (a) {
        var b = a.length % ve.length, c = this.iY();
        return ve[b] + a + ".png?v=" + c.pG + "&udt=" + c.lG
      }
    }, iY: function () {
      if (this.uE) return this.uE;
      var a = "undefined" !== typeof MSV ? MSV.Z5 : {};
      return this.uE = {pG: a.version ? a.version : "001", lG: a.s1 ? a.s1 : "20150621"}
    }
  };
  O = x.lang.av;
  le = 3;
  ne = 4;
  oe = 7;
  pe = 8;
  qe = 15;
  re = 16;

  function Jd(a, b, c) {
    c = c || {};
    this.P = a;
    this.Iw = b;
    this.Lc = b.cO;
    this.fb = {
      Y0: "na",
      zIndex: 0,
      WO: c.tileUrls || {
        http: ["http://"+map_online0+"/pvd/?qt=vtile", "http://"+map_online1+"/pvd/?qt=vtile", "http://"+map_online2+"/pvd/?qt=vtile", "http://"+map_online3+"/pvd/?qt=vtile", "http://"+map_online4+"/pvd/?qt=vtile"],
        https: ["https://"+map_ss0_bdstatic+"/pvd/?qt=vtile", "https://"+map_ss1_bdstatic+"/pvd/?qt=vtile", "https://"+map_ss2_bdstatic+"/pvd/?qt=vtile", "https://"+map_ss3_bdstatic+"/pvd/?qt=vtile", "https://"+map_ss0_bdstatic+"/pvd/?qt=vtile"]
      },
      hE: c.iconUrls || ["https://"+map_ss0_bdstatic+"/sty/map_icons2x/", "https://"+map_ss1_bdstatic+"/sty/map_icons2x/"],
      RF: q
    };
    this.YB = "";
    this.tT = {};
    var c = c.urlOpts || {styles: "pl", extdata: 1, textimg: 0, mesh3d: 0, limit: 30}, e;
    for (e in c) c.hasOwnProperty(e) && (this.YB = this.YB + "&" + e + "=" + c[e]);
    this.th = {};
    this.Ms = [];
    this.St = 0;
    this.hy = t;
    this.ay = {};
    a = this.fb.Y0;
    Vd[a] ? a = Vd[a] : (b = new Wd(a, l), a = Vd[a] = b);
    this.Id = a;
    this.P.Id = this.Id
  }

  window.VectorIndoorTileLayer = "VectorIndoorTileLayer";
  fa = Jd.prototype;
  fa.za = function () {
    var a = this.P, b = a.ef;
    if (!this.So) {
      var c = b.jr(this.fb.zIndex);
      c.style.WebkitTransform = "translate3d(0px, 0px, 0)";
      this.So = c
    }
    b.ng.appendChild(this.So);
    b.D5 = c;
    if (this.fb.RF) {
      ze(this);
      var e = this;
      a.addEventListener("checkvectorclick", function (a) {
        var b;
        a:{
          b = a.offsetX;
          var c = a.offsetY, k = e.Ms.y_;
          if (k) for (var m = 0; m < k.length; m++) for (var n = k[m], o = 0; o < n.length; o++) if (a = n[o], !a.Lt && a.Gt && b > a.bg && b < a.Ci && c > a.cg && c < a.Di) {
            b = a.Gt;
            b = {type: 9, name: a.name, uid: a.uid, point: {x: b.he + b.width / 2, y: b.ie + 6}};
            break a
          }
          b = s
        }
        b && (a = new O("onvectorclick"), a.z5 = b, a.Xe = "base", this.dispatchEvent(a))
      })
    }
  };

  function ze(a) {
    var b = a.P, c = b.ef, e = a.Lc, f = b.wb(), g = f.width, f = f.height, i = F("canvas");
    i.style.cssText = "position: absolute;left:0;top:0;width:" + g + "px;height:" + f + "px;z-index:2;";
    i.width = g * e;
    i.height = f * e;
    a.ly = i;
    a.Vp = i.getContext("2d");
    a.Vp.scale(e, e);
    a.Vp.textBaseline = "top";
    c.ng.appendChild(i);
    b.dT = i
  }

  fa.NY = u("Id");
  fa.update = function (a, b) {
    b = b || {};
    this.mG = b.mG;
    b.Hm && (this.t1 = b.Hm);
    if (this.fb.RF && (b.ym && this.ym(), b.G0)) {
      var c = this.Lc, e = this.P.wb(), f = e.width, e = e.height, g = this.ly, i = g.style;
      i.width = f + "px";
      i.height = e + "px";
      g.width = f * c;
      g.height = e * c;
      this.Vp.scale(c, c);
      this.Vp.textBaseline = "top"
    }
    if (b.D7) {
      c = this.So;
      f = 0;
      for (e = c.childNodes.length; f < e; f++) c.childNodes[f].ko = t
    }
    this.ox = a;
    this.Yp(a)
  };
  fa.Yp = function (a) {
    this.Ms = [];
    var b = this.P, c = b.la(), e = b.Dc.zi(b.ge), f = this.Id.Wb(c),
      e = [Math.round(-e.lng / f), Math.round(e.lat / f)], f = this.Id.le(c), g = b.da.replace(/^TANGRAM_/, ""),
      i = this.Id.Dp(c), b = this.P, k = -b.offsetY + b.height / 2, m = this.So;
    m.style.left = -b.offsetX + b.width / 2 + "px";
    m.style.top = k + "px";
    this.Ue ? this.Ue.length = 0 : this.Ue = [];
    b = 0;
    for (k = m.childNodes.length; b < k; b++) {
      var n = m.childNodes[b];
      n.Hr = t;
      this.Ue.push(n)
    }
    if (b = this.hn) for (var o in b) delete b[o]; else this.hn = {};
    this.Ve ? this.Ve.length = 0 : this.Ve = [];
    b = 0;
    for (k = a.length; b < k; b++) {
      var n = a[b][0], p = a[b][1];
      o = ye.xm(a[b][0], a[b][2], a[b][3]);
      o.offsetX && (a[b][0] = o.Ag, a[b][4] = n, a[b][5] = o.offsetX / 2, n = o.Ag);
      o = 0;
      for (var v = this.Ue.length; o < v; o++) {
        var w = this.Ue[o];
        if (w.id === g + "_" + n + "_" + p + "_" + i + "_" + c) {
          w.Hr = q;
          this.hn[w.id] = w;
          break
        }
      }
    }
    b = 0;
    for (k = this.Ue.length; b < k; b++) w = this.Ue[b], w.Hr || (w.bC = s, delete w.bC, w.ko = t, this.Ve.push(w));
    o = [];
    v = f * this.Lc;
    b = 0;
    for (k = a.length; b < k; b++) {
      var n = a[b][0], p = a[b][1], w = a[b][4] ? a[b][4] * f + e[0] + a[b][5] : n * f + e[0], y = (-1 - p) * f + e[1],
        z = g + "_" + n + "_" + p + "_" + i + "_" + c, C = this.hn[z], D = s;
      if (C) D = C.style, D.left = w + "px", D.top = y + "px", D.width = f + "px", D.height = f + "px", C.ko ? C.cG && C.cG && this.Ms.push(C.cG) : (C.JH = q, C.bC = s, delete C.bC, o.push([n, p, C])); else {
        if (0 < this.Ve.length) {
          var C = this.Ve.shift(), G = C.getContext("2d");
          C.getAttribute("width") !== v && (C._scale = t);
          C.setAttribute("width", v);
          C.setAttribute("height", v);
          D = C.style;
          D.width = f + "px";
          D.height = f + "px";
          G.clearRect(0, 0, v, v)
        } else C = document.createElement("canvas"), D = C.style, D.position = "absolute", this.fb.backgroundColor && (D.background = this.fb.backgroundColor), D.width = f + "px", D.height = f + "px", C.setAttribute("width", v), C.setAttribute("height", v), m.appendChild(C);
        C.id = z;
        D.left = w + "px";
        D.top = y + "px";
        o.push([n, p, C])
      }
      C.style.visibility = ""
    }
    b = 0;
    for (k = this.Ve.length; b < k; b++) this.Ve[b].style.visibility = "hidden";
    if (0 === o.length) {
      Ae(this);
      a = this.P.da.replace(/^TANGRAM_/, "");
      c = this.P.la();
      e = this.Id.Dp(c);
      f = {};
      for (g = 0; g < this.ox.length; g++) i = this.ox[g], i = a + "_" + i[0] + "_" + i[1] + "_" + e + "_" + c, this.th[i] && (f[i] = this.th[i], this.mG && this.Iw.hD.gD(this.th[i].G1, this.th[i].W0, this.th[i].Ag, this.th[i].vn, this.th[i].WE, this.Id.le(this.th[i].WE), this.Id.dE(this.th[i].WE), this.fb.tE));
      this.th = f
    } else {
      this.St = o.length;
      this.hy = t;
      c = this.Id.Dp(this.P.la());
      for (e = 0; e < a.length; e++) a[e][3] = c;
      for (e = 0; e < o.length; e++) a = o[e][2], f = o[e][0], g = o[e][1], o[e][3] = c, a.ko = t, a.JH = t, Be(this, f, g, c, a)
    }
  };

  function Be(a, b, c, e, f) {
    var g = b + "_" + c + "_" + e, i = a.tT;
    if (i[g]) {
      if ("loading" === i[g].status) return
    } else i[g] = {status: "init", jO: 0};
    var k = a, m = k.P, n = [], n = "0" === A.Su ? k.fb.WO.http : k.fb.WO.https, o = Math.abs(b + c) % n.length,
      p = "x=" + b + "&y=" + c + "&z=" + e, v = Ce(a.Iw), w = v.pG, v = v.lG,
      y = "_" + (0 > b ? "_" : "") + (0 > c ? "$" : "") + parseInt(Math.abs(b) + "" + Math.abs(c) + "" + e, 10).toString(36),
      p = p + a.YB + "v=" + w + "&udt=" + v + "&fn=window." + y, w = n[o] + "&" + p,
      w = n[o] + "&param=" + window.encodeURIComponent(Rb(p));
    window[y] = function (a) {
      clearTimeout(i[g].ll);
      i[g] = s;
      if (a) {
        var n = m.la(), o;
        a:{
          for (o = 0; o < k.ox.length; o++) {
            var p = k.ox[o];
            if (p[0] === b && p[1] === c && p[3] === e) {
              o = q;
              break a
            }
          }
          o = t
        }
        if (o !== t) {
          o = new O("updateindoor");
          o.IndoorCanvas = [];
          o.IndoorCanvas.push({canvasDom: f, data: a, canvasID: f.id, ratio: k.Lc});
          m.dispatchEvent(o);
          if (m.M.Gk) {
            if (k.th[f.id] = {
              G1: a,
              W0: f,
              Ag: b,
              vn: c,
              WE: n
            }, k.Iw.hD.gD(a, f, b, c, n, k.Id.le(n), k.Id.dE(n), k.fb.tE), k.fb.RF) {
              n = [];
              n.X0 = [b, c, e];
              if (a[0]) for (o = 0; o < a[0].length; o++) a[0][o][0] === le && n.push({SM: a[0][o]});
              if (a[2]) for (o = 0; o < a[2].length; o++) n.push({HZ: a[2][o]});
              f.cG = n;
              k.Ms.push(n);
              k.hy === t && k.St--;
              (0 === k.St || k.hy === q) && Ae(k)
            }
          } else k.St--, (0 === k.St || k.hy === q) && Ae(k);
          delete window[y]
        }
      }
    };
    sa(w);
    i[g].status = "loading";
    k = a;
    i[g].ll = setTimeout(function () {
      3 > i[g].jO ? (i[g].jO++, i[g].status = "init", Be(k, b, c, e, f)) : i[g] = s
    }, 4E3)
  }

  function Ae(a) {
    if (a.ly) {
      var b = a.P;
      a.ly.style.left = -b.offsetX + "px";
      a.ly.style.top = -b.offsetY + "px";
      var c = new O("updateindoorlabel");
      c.labelCanvasDom = b.dT;
      b.dispatchEvent(c);
      if (b.M.Gk) {
        a.ym();
        var c = a.Id, e = b.la(), f = c.Dp(b.la());
        a.Iw.hD.gL(a.Ms, a.fb.tE, a.Vp, c.le(e), Math.pow(2, e - f), e);
        "moving" !== a.t1 && b.dispatchEvent(new O("ontilesloaded"))
      }
    }
  }

  fa.ym = function () {
    var a = this.P.wb(), b = this.Lc;
    this.Vp.clearRect(0, 0, a.width * b, a.height * b)
  };
  fa.remove = function () {
    var a = this.P.ef;
    this.So && a.ng.removeChild(this.So)
  };

  function Id(a) {
    this.P = a.map;
    this.jf = [];
    this.qs = {};
    this.cO = this.P.M.devicePixelRatio;
    this.hD = new xe(this.P);
    this.za()
  }

  window.VectorIndoorTileMgr = "VectorIndoorTileMgr";
  fa = Id.prototype;
  fa.za = function () {
    var a = this, b = this.P;
    b.addEventListener("addtilelayer", function (b) {
      a.Te(b.target)
    });
    b.addEventListener("removetilelayer", function (b) {
      a.fg(b.target)
    });
    setTimeout(function () {
      b.addEventListener("onmoveend", function (b) {
        "centerAndZoom" !== b.kA && a.update({Hm: "moveend"})
      });
      b.addEventListener("onmoving", function () {
        a.update({Hm: "moving"})
      });
      b.addEventListener("onzoomend", function (b) {
        "centerAndZoom" !== b.kA && a.update({ym: q, Hm: "zoomend"})
      });
      b.addEventListener("centerandzoom", function () {
        a.update({ym: q, Hm: "centerandzoom"})
      });
      b.addEventListener("onupdatestyles", function () {
        a.update({ym: q, mG: q, Hm: "updatestyles"});
        a.P.Af(a.P.Hb());
        setTimeout(function () {
          a.P.dispatchEvent(new O("onvectordrawend"))
        }, 10)
      });
      b.addEventListener("onmaptypechange", function (b) {
        b.Wa === Qa && a.update({ym: q, Hm: "maptypechange"})
      })
    }, 1);
    b.addEventListener("indoor_data_refresh", ba());
    b.addEventListener("onresize", function () {
      a.update({G0: q})
    });
    a.update()
  };
  fa.Te = function (a) {
    if (a instanceof Jd) {
      for (var b = 0; b < this.jf.length; b++) if (this.jf[b] === a) return;
      this.jf.push(a);
      a.za();
      this.P.loaded && this.update()
    }
  };
  fa.fg = function (a) {
    if (a instanceof Jd) {
      for (var b = 0; b < this.jf.length; b++) if (this.jf[b] === a) {
        this.jf.splice(b, 1);
        break
      }
      a.remove()
    }
  };
  fa.hM = function (a) {
    var b = a.getName();
    if (this.qs[b]) return this.qs[b];
    var c = this.P, e = c.la(), f = a.Dp(e), g = c.Jb, g = ye.uC(g), i = a.dE(Math.floor(e)), a = a.TX(e);
    c.da.replace(/^TANGRAM_/, "");
    var e = Math.ceil(g.lng / i), g = Math.ceil(g.lat / i), k = 0, m = 0, n = c.VX(), n = ye.WV(n, c.Jb);
    n.kf.lng > ye.qg && (c = ye.$L(f), k = Math.ceil(c / a));
    n.nf.lng < ye.hh && (c = ye.$L(f), m = Math.ceil(c / a));
    if (1.9505879362428114E7 < n.kf.lat || -1.5949096637571886E7 > n.nf.lat) n.kf.lat = 1.9505879362428114E7, n.nf.lat = -1.5949096637571886E7;
    for (var c = [Math.floor(n.nf.lng / i) - m, Math.floor(n.nf.lat / i)], o = [Math.floor(n.kf.lng / i) + k, Math.floor(n.kf.lat / i)], k = [], n = o[0] + 1, m = c[1], o = o[1] + 1, c = c[0]; c < n; c++) if (ye.iy(c, f, a) !== q) for (var p = m; p < o; p++) k.push([c, p, f, a]);
    k = ye.vC(k, f, a, i);
    k.sort(function (a) {
      return function (b, c) {
        return 0.4 * Math.abs(b[0] - a[0]) + 0.6 * Math.abs(b[1] - a[1]) - (0.4 * Math.abs(c[0] - a[0]) + 0.6 * Math.abs(c[1] - a[1]))
      }
    }([e, g]));
    this.qs[b] = k;
    return this.qs[b]
  };

  function Ce(a) {
    if (a.qG) return a.qG;
    a.qG = {pG: "001", lG: $b("normal")};
    return a.qG
  }

  fa.update = function (a) {
    this.qs = {};
    for (var b = 0; b < this.jf.length; b++) {
      var c = this.jf[b], e = this.hM(c.Id);
      c.update(e, a)
    }
  };

  function De(a, b, c) {
    this.bd = a;
    this.jf = b instanceof Kd ? [b] : b.slice(0);
    c = c || {};
    this.m = {
      a1: c.tips || "",
      QE: "",
      kc: c.minZoom || 4,
      qc: c.maxZoom || 18,
      x5: c.minZoom || 4,
      w5: c.maxZoom || 18,
      Lu: 256,
      bG: c.textColor || "black",
      BD: c.errorImageUrl || "",
      jb: new lb(new P(-21364736, -16023552), new P(23855104, 19431424)),
      Dc: c.projection || new R
    };
    1 <= this.jf.length && (this.jf[0].Zw = q);
    x.extend(this.m, c)
  }

  x.extend(De.prototype, {
    getName: u("bd"), At: function () {
      return this.m.a1
    }, R4: function () {
      return this.m.QE
    }, LY: function () {
      return this.jf[0]
    }, h5: u("jf"), le: function () {
      return this.m.Lu
    }, sf: function () {
      return this.m.kc
    }, Ye: function () {
      return this.m.qc
    }, setMaxZoom: function (a) {
      this.m.qc = a
    }, Tm: function () {
      return this.m.bG
    }, Aj: function () {
      return this.m.Dc
    }, K4: function () {
      return this.m.BD
    }, le: function () {
      return this.m.Lu
    }, Wb: function (a) {
      return Math.pow(2, 18 - a)
    }, mM: function (a) {
      return this.Wb(a) * this.le()
    }, MF: function (a) {
      this.Aj().tO(a)
    }
  });
  var Ee = [A.url.proto + A.url.domain.TILE_BASE_URLS[0], A.url.proto + A.url.domain.TILE_BASE_URLS[1], A.url.proto + A.url.domain.TILE_BASE_URLS[2], A.url.proto + A.url.domain.TILE_BASE_URLS[3]],
    Fe = [A.url.proto + A.url.domain.TILE_ONLINE_URLS[0] + "/tile/", A.url.proto + A.url.domain.TILE_ONLINE_URLS[1] + "/tile/", A.url.proto + A.url.domain.TILE_ONLINE_URLS[2] + "/tile/", A.url.proto + A.url.domain.TILE_ONLINE_URLS[3] + "/tile/"],
    Ge = {dark: "dl", light: "ll", normal: "pl"}, He = new Kd;
  He.IO = q;
  He.getTilesUrl = function (a, b, c) {
    var e = a.x, a = a.y, f = $b("normal"), g = 1, c = Ge[c];
    this.map.$x() && (g = 2);
    e = this.map.ef.xm(e, b).Ag;
    return (Fe[Math.abs(e + a) % Fe.length] + "?qt=vtile&x=" + (e + "").replace(/-/gi, "M") + "&y=" + (a + "").replace(/-/gi, "M") + "&z=" + b + "&styles=" + c + "&scaler=" + g + (6 == x.ga.oa ? "&color_dep=32&colors=50" : "") + "&udt=" + f + "&from=jsapi3_0").replace(/-(\d+)/gi, "M$1")
  };
  var Qa = new De("\u5730\u56fe", He, {tips: "\u663e\u793a\u666e\u901a\u5730\u56fe", maxZoom: 19}), Ie = new Kd;
  Ie.VO = [A.url.proto + A.url.domain.TIlE_PERSPECT_URLS[0] + "/resource/mappic/", A.url.proto + A.url.domain.TIlE_PERSPECT_URLS[1] + "/resource/mappic/", A.url.proto + A.url.domain.TIlE_PERSPECT_URLS[2] + "/resource/mappic/", A.url.proto + A.url.domain.TIlE_PERSPECT_URLS[3] + "/resource/mappic/"];
  Ie.getTilesUrl = function (a, b) {
    var c = a.x, e = a.y, f = 256 * Math.pow(2, 20 - b), e = Math.round((9998336 - f * e) / f) - 1;
    return url = this.VO[Math.abs(c + e) % this.VO.length] + this.map.Qb + "/" + this.map.ex + "/3/lv" + (21 - b) + "/" + c + "," + e + ".jpg"
  };
  var Ta = new De("\u4e09\u7ef4", Ie, {
    tips: "\u663e\u793a\u4e09\u7ef4\u5730\u56fe",
    minZoom: 15,
    maxZoom: 20,
    textColor: "white",
    projection: new nb
  });
  Ta.Wb = function (a) {
    return Math.pow(2, 20 - a)
  };
  Ta.Kk = function (a) {
    if (!a) return "";
    var b = J.AC, c;
    for (c in b) if (-1 < a.search(c)) return b[c].Gy;
    return ""
  };
  Ta.JL = function (a) {
    return {bj: 2, gz: 1, sz: 14, sh: 4}[a]
  };
  var Je = new Kd({Zw: q});
  Je.getTilesUrl = function (a, b) {
    var c = a.x, e = a.y;
    return (Ee[Math.abs(c + e) % Ee.length] + "u=x=" + c + ";y=" + e + ";z=" + b + ";v=009;type=sate&fm=46&udt=" + $b("satellite")).replace(/-(\d+)/gi, "M$1")
  };
  var db = new De("\u536b\u661f", Je, {
    tips: "\u663e\u793a\u536b\u661f\u5f71\u50cf",
    minZoom: 4,
    maxZoom: 19,
    textColor: "white"
  }), Ke = new Kd({transparentPng: q});
  Ke.getTilesUrl = function (a, b) {
    var c = a.x, e = a.y, f = $b("satelliteStreet");
    return (Fe[Math.abs(c + e) % Fe.length] + "?qt=vtile&x=" + (c + "").replace(/-/gi, "M") + "&y=" + (e + "").replace(/-/gi, "M") + "&z=" + b + "&styles=sl" + (6 == x.ga.oa ? "&color_dep=32&colors=50" : "") + "&udt=" + f).replace(/-(\d+)/gi, "M$1")
  };
  var Va = new De("\u6df7\u5408", [Je, Ke], {
    tips: "\u663e\u793a\u5e26\u6709\u8857\u9053\u7684\u536b\u661f\u5f71\u50cf",
    labelText: "\u8def\u7f51",
    minZoom: 4,
    maxZoom: 19,
    textColor: "white"
  });
  var Le = 1, W = {};
  window.b2 = W;

  function X(a, b) {
    x.lang.Ja.call(this);
    this.qd = {};
    this.yn(a);
    b = b || {};
    b.na = b.renderOptions || {};
    this.m = {
      na: {
        Aa: b.na.panel || s,
        map: b.na.map || s,
        zg: b.na.autoViewport || q,
        ou: b.na.selectFirstResult,
        Vm: b.na.highlightMode,
        jc: b.na.enableDragging || t
      },
      cu: b.onSearchComplete || ba(),
      GN: b.onMarkersSet || ba(),
      FN: b.onInfoHtmlSet || ba(),
      IN: b.onResultsHtmlSet || ba(),
      EN: b.onGetBusListComplete || ba(),
      DN: b.onGetBusLineComplete || ba(),
      BN: b.onBusListHtmlSet || ba(),
      AN: b.onBusLineHtmlSet || ba(),
      gF: b.onPolylinesSet || ba(),
      kq: b.reqFrom || ""
    };
    this.m.na.zg = "undefined" != typeof b && "undefined" != typeof b.renderOptions && "undefined" != typeof b.renderOptions.autoViewport ? b.renderOptions.autoViewport : q;
    this.m.na.Aa = x.Ic(this.m.na.Aa)
  }

  x.xa(X, x.lang.Ja);
  x.extend(X.prototype, {
    getResults: function () {
      return this.Kc ? this.Xi : this.ka
    }, enableAutoViewport: function () {
      this.m.na.zg = q
    }, disableAutoViewport: function () {
      this.m.na.zg = t
    }, yn: function (a) {
      a && (this.qd.src = a)
    }, vu: function (a) {
      this.m.cu = a || ba()
    }, setMarkersSetCallback: function (a) {
      this.m.GN = a || ba()
    }, setPolylinesSetCallback: function (a) {
      this.m.gF = a || ba()
    }, setInfoHtmlSetCallback: function (a) {
      this.m.FN = a || ba()
    }, setResultsHtmlSetCallback: function (a) {
      this.m.IN = a || ba()
    }, Rm: u("Re")
  });
  var Me = {
    VG: A.cd, lb: function (a, b, c, e, f) {
      this.I_(b);
      var g = (1E5 * Math.random()).toFixed(0);
      A._rd["_cbk" + g] = function (b) {
        b.result && b.result.error && 202 === b.result.error ? alert("\u8be5AK\u56e0\u4e3a\u6076\u610f\u884c\u4e3a\u5df2\u7ecf\u88ab\u7ba1\u7406\u5458\u5c01\u7981\uff01") : b.result && b.result.error && 403 === b.result.error ? A.$p !== s && A.$p.update("\u672a\u83b7\u5f97\u767e\u5ea6\u5730\u56fe\u5546\u7528\u6388\u6743\uff0c\u53ef\u80fd\u5bfc\u81f4\u90e8\u5206\u5730\u56fe\u8bf7\u6c42\u5931\u8d25\uff0c\u8bf7\u5237\u65b0\u540e\u91cd\u8bd5\u3002") : (c = c || {}, a && a(b, c), delete A._rd["_cbk" + g])
      };
      e = e || "";
      b = c && c.y1 ? Nb(b, encodeURI) : Nb(b, encodeURIComponent);
      this.VG = c && c.rL ? c.hO ? c.hO : A.Zp : A.cd;
      e = this.VG + e + "?" + b + "&ie=utf-8&oue=1&fromproduct=jsapi";
      f || (e += "&res=api");
      e += "&ak=" + ra;
      sa(e + ("&callback=BMap._rd._cbk" + g))
    }, I_: function (a) {
      if (a.qt) {
        var b = "";
        switch (a.qt) {
          case "bt":
            b = "z_qt|bt";
            break;
          case "nav":
            b = "z_qt|nav";
            break;
          case "walk":
            b = "z_qt|walk";
            break;
          case "bse":
            b = "z_qt|bse";
            break;
          case "nse":
            b = "z_qt|nse";
            break;
          case "drag":
            b = "z_qt|drag"
        }
        "" !== b && A.alog("cus.fire", "count", b)
      }
    }
  };
  window.s2 = Me;
  A._rd = {};
  var hb = {};
  window.r2 = hb;
  hb.sF = function (a) {
    a = a.replace(/<\/?[^>]*>/g, "");
    return a = a.replace(/[ | ]* /g, " ")
  };
  hb.o_ = function (a) {
    return a.replace(/([1-9]\d*\.\d*|0\.\d*[1-9]\d*|0?\.0+|0|[1-9]\d*),([1-9]\d*\.\d*|0\.\d*[1-9]\d*|0?\.0+|0|[1-9]\d*)(,)/g, "$1,$2;")
  };
  hb.p_ = function (a, b) {
    return a.replace(RegExp("(((-?\\d+)(\\.\\d+)?),((-?\\d+)(\\.\\d+)?);)(((-?\\d+)(\\.\\d+)?),((-?\\d+)(\\.\\d+)?);){" + b + "}", "ig"), "$1")
  };
  var Qe = 2, Re = 6, Se = 8, Te = 2, Ue = 3, Ve = 6, We = 0, Xe = "bt", Ye = "nav", Ze = "walk", $e = "bl", af = "bsl",
    bf = "ride", cf = 15, df = 18;
  A.I = window.Instance = x.lang.Uc;

  function ef(a, b, c) {
    x.lang.Ja.call(this);
    if (a) {
      this.bb = "object" == typeof a ? a : x.Ic(a);
      this.page = 1;
      this.Od = 100;
      this.pK = "pg";
      this.eg = 4;
      this.AK = b;
      this.update = q;
      a = {page: 1, s7: 100, Od: 100, eg: 4, pK: "pg", update: q};
      c || (c = a);
      for (var e in c) "undefined" != typeof c[e] && (this[e] = c[e]);
      this.Ba()
    }
  }

  x.extend(ef.prototype, {
    Ba: function () {
      this.za()
    }, za: function () {
      this.bW();
      this.bb.innerHTML = this.BW()
    }, bW: function () {
      isNaN(parseInt(this.page)) && (this.page = 1);
      isNaN(parseInt(this.Od)) && (this.Od = 1);
      1 > this.page && (this.page = 1);
      1 > this.Od && (this.Od = 1);
      this.page > this.Od && (this.page = this.Od);
      this.page = parseInt(this.page);
      this.Od = parseInt(this.Od)
    }, Y4: function () {
      location.search.match(RegExp("[?&]?" + this.pK + "=([^&]*)[&$]?", "gi"));
      this.page = RegExp.$1
    }, BW: function () {
      var a = [], b = this.page - 1, c = this.page + 1;
      a.push('<p style="margin:0;padding:0;white-space:nowrap">');
      if (!(1 > b)) {
        if (this.page >= this.eg) {
          var e;
          a.push('<span style="margin-right:3px"><a style="color:#7777cc" href="javascript:void(0)" onclick="{temp1}">\u9996\u9875</a></span>'.replace("{temp1}", "BMap.I('" + this.da + "').toPage(1);"))
        }
        a.push('<span style="margin-right:3px"><a style="color:#7777cc" href="javascript:void(0)" onclick="{temp2}">\u4e0a\u4e00\u9875</a></span>'.replace("{temp2}", "BMap.I('" + this.da + "').toPage(" + b + ");"))
      }
      if (this.page < this.eg) e = 0 == this.page % this.eg ? this.page - this.eg - 1 : this.page - this.page % this.eg + 1, b = e + this.eg - 1; else {
        e = Math.floor(this.eg / 2);
        var f = this.eg % 2 - 1, b = this.Od > this.page + e ? this.page + e : this.Od;
        e = this.page - e - f
      }
      this.page > this.Od - this.eg && this.page >= this.eg && (e = this.Od - this.eg + 1, b = this.Od);
      for (f = e; f <= b; f++) 0 < f && (f == this.page ? a.push('<span style="margin-right:3px">' + f + "</span>") : 1 <= f && f <= this.Od && (e = '<span><a style="color:#7777cc;margin-right:3px" href="javascript:void(0)" onclick="{temp3}">[' + f + "]</a></span>", a.push(e.replace("{temp3}", "BMap.I('" + this.da + "').toPage(" + f + ");"))));
      c > this.Od || a.push('<span><a style="color:#7777cc" href="javascript:void(0)" onclick="{temp4}">\u4e0b\u4e00\u9875</a></span>'.replace("{temp4}", "BMap.I('" + this.da + "').toPage(" + c + ");"));
      a.push("</p>");
      return a.join("")
    }, toPage: function (a) {
      a = a ? a : 1;
      "function" == typeof this.AK && (this.AK(a), this.page = a);
      this.update && this.Ba()
    }
  });

  function jb(a, b) {
    X.call(this, a, b);
    b = b || {};
    b.renderOptions = b.renderOptions || {};
    this.zn(b.pageCapacity);
    "undefined" != typeof b.renderOptions.selectFirstResult && !b.renderOptions.selectFirstResult ? this.$C() : this.tD();
    this.ua = [];
    this.Ef = [];
    this.La = -1;
    this.Qa = [];
    var c = this;
    Wa.load("local", function () {
      c.Tz()
    }, q)
  }

  x.xa(jb, X, "LocalSearch");
  jb.Lq = 10;
  jb.k2 = 1;
  jb.Un = 100;
  jb.JG = 2E3;
  jb.SG = 1E5;
  x.extend(jb.prototype, {
    search: function (a, b) {
      this.Qa.push({method: "search", arguments: [a, b]})
    }, wn: function (a, b, c) {
      this.Qa.push({method: "searchInBounds", arguments: [a, b, c]})
    }, qq: function (a, b, c, e) {
      this.Qa.push({method: "searchNearby", arguments: [a, b, c, e]})
    }, we: function () {
      delete this.Ka;
      delete this.Re;
      delete this.ka;
      delete this.ra;
      this.La = -1;
      this.Xa();
      this.m.na.Aa && (this.m.na.Aa.innerHTML = "")
    }, Um: ba(), tD: function () {
      this.m.na.ou = q
    }, $C: function () {
      this.m.na.ou = t
    }, zn: function (a) {
      this.m.Yk = "number" == typeof a && !isNaN(a) ? 1 > a ? jb.Lq : a > jb.Un ? jb.Lq : a : jb.Lq
    }, uf: function () {
      return this.m.Yk
    }, toString: ea("LocalSearch")
  });
  var ff = jb.prototype;
  S(ff, {
    clearResults: ff.we,
    setPageCapacity: ff.zn,
    getPageCapacity: ff.uf,
    gotoPage: ff.Um,
    searchNearby: ff.qq,
    searchInBounds: ff.wn,
    search: ff.search,
    enableFirstResultSelection: ff.tD,
    disableFirstResultSelection: ff.$C
  });

  function gf(a, b) {
    X.call(this, a, b)
  }

  x.xa(gf, X, "BaseRoute");
  x.extend(gf.prototype, {we: ba()});

  function hf(a, b) {
    X.call(this, a, b);
    b = b || {};
    this.An(b.policy);
    this.IF(b.intercityPolicy);
    this.NF(b.transitTypePolicy);
    this.zn(b.pageCapacity);
    this.Eb = Xe;
    this.Vn = Le;
    this.ua = [];
    this.La = -1;
    this.m.Fl = b.enableTraffic || t;
    this.Qa = [];
    var c = this;
    Wa.load("route", function () {
      c.Ed()
    })
  }

  hf.Un = 100;
  hf.UP = [0, 1, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 1, 1, 1];
  hf.VP = [0, 3, 4, 0, 0, 0, 5];
  x.xa(hf, gf, "TransitRoute");
  x.extend(hf.prototype, {
    An: function (a) {
      this.m.Pd = 0 <= a && 5 >= a ? a : 0
    }, IF: function (a) {
      this.m.Zm = 0 <= a && 2 >= a ? a : 0
    }, NF: function (a) {
      this.m.Fn = 0 <= a && 2 >= a ? a : 0
    }, SA: function (a, b) {
      this.Qa.push({method: "_internalSearch", arguments: [a, b]})
    }, search: function (a, b) {
      this.Qa.push({method: "search", arguments: [a, b]})
    }, zn: function (a) {
      if ("string" === typeof a && (a = parseInt(a, 10), isNaN(a))) {
        this.m.Yk = hf.Un;
        return
      }
      this.m.Yk = "number" !== typeof a ? hf.Un : 1 <= a && a <= hf.Un ? Math.round(a) : hf.Un
    }, toString: ea("TransitRoute"), i3: function (a) {
      return a.replace(/\(.*\)/, "")
    }
  });
  var jf = hf.prototype;
  S(jf, {_internalSearch: jf.SA});

  function kf(a, b) {
    X.call(this, a, b);
    this.ua = [];
    this.La = -1;
    this.Qa = [];
    var c = this, e = this.m.na;
    1 !== e.Vm && 2 !== e.Vm && (e.Vm = 1);
    this.mo = this.m.na.jc ? q : t;
    Wa.load("route", function () {
      c.Ed()
    });
    this.ey && this.ey()
  }

  kf.jQ = " \u73af\u5c9b \u65e0\u5c5e\u6027\u9053\u8def \u4e3b\u8def \u9ad8\u901f\u8fde\u63a5\u8def \u4ea4\u53c9\u70b9\u5185\u8def\u6bb5 \u8fde\u63a5\u9053\u8def \u505c\u8f66\u573a\u5185\u90e8\u9053\u8def \u670d\u52a1\u533a\u5185\u90e8\u9053\u8def \u6865 \u6b65\u884c\u8857 \u8f85\u8def \u531d\u9053 \u5168\u5c01\u95ed\u9053\u8def \u672a\u5b9a\u4e49\u4ea4\u901a\u533a\u57df POI\u8fde\u63a5\u8def \u96a7\u9053 \u6b65\u884c\u9053 \u516c\u4ea4\u4e13\u7528\u9053 \u63d0\u524d\u53f3\u8f6c\u9053".split(" ");
  x.xa(kf, gf, "DWRoute");
  x.extend(kf.prototype, {
    search: function (a, b, c) {
      this.Qa.push({method: "search", arguments: [a, b, c]})
    }
  });

  function lf(a, b) {
    kf.call(this, a, b);
    b = b || {};
    this.m.Fl = b.enableTraffic || t;
    this.An(b.policy);
    this.Eb = Ye;
    this.Vn = Ue
  }

  x.xa(lf, kf, "DrivingRoute");
  lf.prototype.An = function (a) {
    this.m.Pd = 0 <= a && 5 >= a ? a : 0
  };

  function mf(a, b) {
    kf.call(this, a, b);
    this.Eb = Ze;
    this.Vn = Te;
    this.mo = t
  }

  x.xa(mf, kf, "WalkingRoute");

  function nf(a, b) {
    kf.call(this, a, b);
    b = b || {};
    this.m.Fl = b.enableTraffic || t;
    this.oT = b.renderOptions.lineType || 0;
    this.Eb = Ye;
    this.Vn = Ue
  }

  x.xa(nf, kf, "TruckRoute");
  nf.prototype.An = function (a) {
    this.m.Pd = 0 <= a && 5 >= a ? a : 0
  };

  function of(a, b) {
    kf.call(this, a, b);
    this.Eb = bf;
    this.Vn = Ve;
    this.mo = t
  }

  x.xa(of, kf, "RidingRoute");

  function pf(a, b) {
    x.lang.Ja.call(this);
    this.ag = [];
    this.$k = [];
    this.m = b;
    this.Hj = a;
    this.map = this.m.na.map || s;
    this.pO = this.m.pO;
    this.Fb = s;
    this.Ek = 0;
    this.ZF = "";
    this.rf = 1;
    this.AD = "";
    this.lq = [0, 0, 0, 0, 0, 0, 0];
    this.cN = [];
    this.Ks = [1, 1, 1, 1, 1, 1, 1];
    this.cP = [1, 1, 1, 1, 1, 1, 1];
    this.mq = [0, 0, 0, 0, 0, 0, 0];
    this.tn = [0, 0, 0, 0, 0, 0, 0];
    this.Kb = [{B: "", Jd: 0, Gn: 0, x: 0, y: 0, sa: -1}, {B: "", Jd: 0, Gn: 0, x: 0, y: 0, sa: -1}, {
      B: "",
      Jd: 0,
      Gn: 0,
      x: 0,
      y: 0,
      sa: -1
    }, {B: "", Jd: 0, Gn: 0, x: 0, y: 0, sa: -1}, {B: "", Jd: 0, Gn: 0, x: 0, y: 0, sa: -1}, {
      B: "",
      Jd: 0,
      Gn: 0,
      x: 0,
      y: 0,
      sa: -1
    }, {B: "", Jd: 0, Gn: 0, x: 0, y: 0, sa: -1}];
    this.oi = -1;
    this.Nu = [];
    this.jG = [];
    Wa.load("route", ba())
  }

  x.lang.xa(pf, x.lang.Ja, "RouteAddr");
  var qf = navigator.userAgent;
  /ipad|iphone|ipod|iph/i.test(qf);
  var rf = /android/i.test(qf);

  function sf(a) {
    this.$e = a || {}
  }

  x.extend(sf.prototype, {
    oO: function (a, b, c) {
      var e = this;
      Wa.load("route", function () {
        e.Ed(a, b, c)
      })
    }
  });

  function tf(a) {
    this.m = {};
    x.extend(this.m, a);
    this.Qa = [];
    var b = this;
    Wa.load("othersearch", function () {
      b.Ed()
    })
  }

  x.xa(tf, x.lang.Ja, "Geocoder");
  x.extend(tf.prototype, {
    Qm: function (a, b, c) {
      this.Qa.push({method: "getPoint", arguments: [a, b, c]})
    }, Nm: function (a, b, c) {
      this.Qa.push({method: "getLocation", arguments: [a, b, c]})
    }, toString: ea("Geocoder")
  });
  var uf = tf.prototype;
  S(uf, {getPoint: uf.Qm, getLocation: uf.Nm});

  function Geolocation(a) {
    a = a || {};
    this.M = {
      timeout: a.timeout || 1E4,
      maximumAge: a.maximumAge || 6E5,
      enableHighAccuracy: a.enableHighAccuracy || t,
      Ri: a.SDKLocation || t
    };
    this.ue = [];
    var b = this;
    Wa.load("othersearch", function () {
      for (var a = 0, e; e = b.ue[a]; a++) b[e.method].apply(b, e.arguments)
    })
  }

  x.extend(Geolocation.prototype, {
    getCurrentPosition: function (a, b) {
      this.ue.push({method: "getCurrentPosition", arguments: arguments})
    }, getStatus: function () {
      return Qe
    }, enableSDKLocation: function () {
      K() && (this.M.Ri = q)
    }, disableSDKLocation: function () {
      this.M.Ri = t
    }
  });

  function vf(a) {
    a = a || {};
    a.na = a.renderOptions || {};
    this.m = {na: {map: a.na.map || s}};
    this.Qa = [];
    var b = this;
    Wa.load("othersearch", function () {
      b.Ed()
    })
  }

  x.xa(vf, x.lang.Ja, "LocalCity");
  x.extend(vf.prototype, {
    get: function (a) {
      this.Qa.push({method: "get", arguments: [a]})
    }, toString: ea("LocalCity")
  });

  function wf() {
    this.Qa = [];
    var a = this;
    Wa.load("othersearch", function () {
      a.Ed()
    })
  }

  x.xa(wf, x.lang.Ja, "Boundary");
  x.extend(wf.prototype, {
    get: function (a, b) {
      this.Qa.push({method: "get", arguments: [a, b]})
    }, toString: ea("Boundary")
  });

  function xf(a, b) {
    X.call(this, a, b);
    this.gQ = $e;
    this.iQ = cf;
    this.fQ = af;
    this.hQ = df;
    this.Qa = [];
    var c = this;
    Wa.load("buslinesearch", function () {
      c.Ed()
    })
  }

  xf.Sv = J.ta + "iw_plus.gif";
  xf.cT = J.ta + "iw_minus.gif";
  xf.VU = J.ta + "stop_icon.png";
  x.xa(xf, X);
  x.extend(xf.prototype, {
    getBusList: function (a) {
      this.Qa.push({method: "getBusList", arguments: [a]})
    }, getBusLine: function (a) {
      this.Qa.push({method: "getBusLine", arguments: [a]})
    }, setGetBusListCompleteCallback: function (a) {
      this.m.EN = a || ba()
    }, setGetBusLineCompleteCallback: function (a) {
      this.m.DN = a || ba()
    }, setBusListHtmlSetCallback: function (a) {
      this.m.BN = a || ba()
    }, setBusLineHtmlSetCallback: function (a) {
      this.m.AN = a || ba()
    }, setPolylinesSetCallback: function (a) {
      this.m.gF = a || ba()
    }
  });

  function yf(a) {
    X.call(this, a);
    a = a || {};
    this.fb = {input: a.input || s, rC: a.baseDom || s, types: a.types || [], cu: a.onSearchComplete || ba()};
    this.qd.src = a.location || "\u5168\u56fd";
    this.nj = "";
    this.wg = s;
    this.qI = "";
    this.ej();
    Ua(Ma);
    var b = this;
    Wa.load("autocomplete", function () {
      b.Ed()
    })
  }

  x.xa(yf, X, "Autocomplete");
  x.extend(yf.prototype, {
    ej: ba(), show: ba(), aa: ba(), OF: function (a) {
      this.fb.types = a
    }, yn: function (a) {
      this.qd.src = a
    }, search: da("nj"), Ty: da("qI"), vu: function (a) {
      this.fb.cu = a
    }
  });
  var Xa;

  function Sa(a, b) {
    function c() {
      f.m.visible ? ("inter" === f.Oe && Ya() && f.m.haveBreakId && f.m.indoorExitControl === q ? x.U.show(f.Er) : x.U.aa(f.Er), this.vd && this.m.closeControl && this.If && this.P && this.P.Ta() === this.R ? x.U.show(f.If) : x.U.aa(f.If), this.m.forceCloseControl && x.U.show(f.If)) : (x.U.aa(f.If), x.U.aa(f.Er))
    }

    this.R = "string" == typeof a ? x.fa(a) : a;
    this.da = zf++;
    this.m = {
      enableScrollWheelZoom: q,
      panoramaRenderer: Ra() ? "javascript" : "flash",
      swfSrc: A.xh("main_domain_nocdn", "res/swf/") + "APILoader.swf",
      visible: q,
      indoorExitControl: q,
      indoorFloorControl: t,
      linksControl: q,
      clickOnRoad: q,
      navigationControl: q,
      closeControl: q,
      indoorSceneSwitchControl: q,
      albumsControl: t,
      albumsControlOptions: {},
      copyrightControlOptions: {},
      forceCloseControl: t,
      haveBreakId: t
    };
    var b = b || {}, e;
    for (e in b) this.m[e] = b[e];
    b.closeControl === q && (this.m.forceCloseControl = q);
    b.useWebGL === t && Ra(t);
    this.Oa = {heading: 0, pitch: 0};
    this.uo = [];
    this.Ob = this.hb = s;
    this.tk = this.Br();
    this.ua = [];
    this.Sc = 1;
    this.Oe = this.AT = this.$g = "";
    this.Ne = {};
    this.Uf = s;
    this.kh = [];
    this.Tr = [];
    "cvsRender" == this.tk || Ra() ? (this.mk = 90, this.ok = -90) : "cssRender" == this.tk && (this.mk = 45, this.ok = -45);
    this.Xr = t;
    var f = this, g = (1E5 * Math.random()).toFixed(0);
    A._rd = A._rd || {};
    A._rd["_cbk" + g] = function (a) {
      if (!a || a.error === l || a.error !== 0) Pc("PANORAMA"); else {
        this.tk === "flashRender" ? Wa.load("panoramaflash", function () {
          f.ej()
        }, q) : Wa.load("panorama", function () {
          f.ob()
        }, q);
        b.Xe == "api" ? Ua(Ia) : Ua(Ja)
      }
      delete A._rd["_cbk" + g]
    };
    this.vo = function () {
      Oc("PANORAMA", "BMap._rd._cbk" + g);
      this.vo = ba()
    };
    this.m.mT !== q && (this.vo(), A.Vq("cus.fire", "count", "z_loadpanoramacount"));
    this.gU(this.R);
    this.addEventListener("id_changed", function () {
      Ua(Ga, {from: b.Xe})
    });
    this.vQ();
    this.addEventListener("indoorexit_options_changed", c);
    this.addEventListener("scene_type_changed", c);
    this.addEventListener("onclose_options_changed", c);
    this.addEventListener("onvisible_changed", c)
  }

  var Af = 4, Bf = 1, Cf = 5, zf = 0;
  x.lang.xa(Sa, x.lang.Ja, "Panorama");
  x.extend(Sa.prototype, {
    vQ: function () {
      var a = this, b = this.If = F("div");
      b.className = "pano_close";
      b.style.cssText = "z-index: 1201;display: none";
      b.title = "\u9000\u51fa\u5168\u666f";
      b.onclick = function () {
        a.aa()
      };
      this.R.appendChild(b);
      var c = this.Er = F("a");
      c.className = "pano_pc_indoor_exit";
      c.style.cssText = "z-index: 1201;display: none";
      c.innerHTML = '<span style="float:right;margin-right:12px;">\u51fa\u53e3</span>';
      c.title = "\u9000\u51fa\u5ba4\u5185\u666f";
      c.onclick = function () {
        a.yp()
      };
      this.R.appendChild(c);
      window.ActiveXObject && !document.addEventListener && (b.style.backgroundColor = "rgb(37,37,37)", c.style.backgroundColor = "rgb(37,37,37)")
    },
    yp: ba(),
    gU: function (a) {
      var b, c;
      b = a.style;
      c = $a(a).position;
      "absolute" != c && "relative" != c && (b.position = "relative", b.zIndex = 0);
      if ("absolute" === c || "relative" === c) if (a = $a(a).zIndex, !a || "auto" === a) b.zIndex = 0
    },
    nY: u("uo"),
    ac: u("hb"),
    OY: u("Cw"),
    FO: u("Cw"),
    ma: u("Ob"),
    Na: u("Oa"),
    la: u("Sc"),
    Fg: u("$g"),
    a5: function () {
      return this.e3 || []
    },
    T4: u("AT"),
    zt: u("Oe"),
    Xy: function (a) {
      a !== this.Oe && (this.Oe = a, this.dispatchEvent(new O("onscene_type_changed")))
    },
    EO: function (a) {
      a !== Cf && (Cf = a)
    },
    zO: function (a) {
      a !== Af && (Af = a)
    },
    Gc: function (a, b, c) {
      "object" === typeof b && (c = b, b = l);
      a != this.hb && (this.Ol = this.hb, this.Pl = this.Ob, this.hb = a, this.Oe = b || "street", this.Ob = s, c && c.pov && this.zd(c.pov))
    },
    va: function (a) {
      a.Vb(this.Ob) || (this.Ol = this.hb, this.Pl = this.Ob, this.Ob = a, this.hb = s)
    },
    zd: function (a) {
      if (a) {
        var a = this.Oa.pitch, b = this.Oa.heading, b = this.TC(b);
        a > this.mk ? a = this.mk : a < this.ok && (a = this.ok);
        this.Xr = q;
        this.Oa.pitch = a;
        this.Oa.heading = b
      }
    },
    w0: function (a, b) {
      this.ok = 0 <= a ? 0 : a;
      this.mk = 0 >= b ? 0 : b
    },
    TC: function (a) {
      return a - 360 * Math.floor(a / 360)
    },
    Xc: function (a) {
      a != this.Sc && (a > Af && (a = Af), a < Bf && (a = Bf), a != this.Sc && (this.Sc = a), "cssRender" === this.tk && this.zd(this.Oa))
    },
    LB: function () {
      if (this.P) for (var a = this.P.Nx(), b = 0; b < a.length; b++) (a[b] instanceof V || a[b] instanceof sd) && a[b].point && this.ua.push(a[b])
    },
    JF: da("P"),
    uu: function (a) {
      this.Uf = a || "none"
    },
    Mj: function (a) {
      for (var b in a) {
        if ("object" == typeof a[b]) for (var c in a[b]) this.m[b][c] = a[b][c]; else this.m[b] = a[b];
        a.closeControl === q && (this.m.forceCloseControl = q);
        a.closeControl === t && (this.m.forceCloseControl = t);
        switch (b) {
          case "linksControl":
            this.dispatchEvent(new O("onlinks_visible_changed"));
            break;
          case "clickOnRoad":
            this.dispatchEvent(new O("onclickonroad_changed"));
            break;
          case "navigationControl":
            this.dispatchEvent(new O("onnavigation_visible_changed"));
            break;
          case "indoorSceneSwitchControl":
            this.dispatchEvent(new O("onindoor_default_switch_mode_changed"));
            break;
          case "albumsControl":
            this.dispatchEvent(new O("onalbums_visible_changed"));
            break;
          case "albumsControlOptions":
            this.dispatchEvent(new O("onalbums_options_changed"));
            break;
          case "copyrightControlOptions":
            this.dispatchEvent(new O("oncopyright_options_changed"));
            break;
          case "closeControl":
            this.dispatchEvent(new O("onclose_options_changed"));
            break;
          case "indoorExitControl":
            this.dispatchEvent(new O("onindoorexit_options_changed"));
            break;
          case "indoorFloorControl":
            this.dispatchEvent(new O("onindoorfloor_options_changed"))
        }
      }
    },
    Uk: function () {
      this.Xl.style.visibility = "hidden"
    },
    az: function () {
      this.Xl.style.visibility = "visible"
    },
    tX: function () {
      this.m.enableScrollWheelZoom = q
    },
    VW: function () {
      this.m.enableScrollWheelZoom = t
    },
    show: function () {
      this.m.visible = q
    },
    aa: function () {
      this.m.visible = t
    },
    Br: function () {
      return Ya() && !K() && "javascript" != this.m.panoramaRenderer ? "flashRender" : !K() && Wb() ? "cvsRender" : "cssRender"
    },
    Ra: function (a) {
      this.Ne[a.rd] = a
    },
    Lb: function (a) {
      delete this.Ne[a]
    },
    Sx: function () {
      return this.m.visible
    },
    vh: function () {
      return new M(this.R.clientWidth, this.R.clientHeight)
    },
    Ta: u("R"),
    GL: function () {
      var a = A.xh("baidumap", "?"), b = this.ac();
      if (b) {
        var b = {
          panotype: this.zt(),
          heading: this.Na().heading,
          pitch: this.Na().pitch,
          pid: b,
          panoid: b,
          from: "api"
        }, c;
        for (c in b) a += c + "=" + b[c] + "&"
      }
      return a.slice(0, -1)
    },
    Xx: function () {
      this.Mj({copyrightControlOptions: {logoVisible: t}})
    },
    SF: function () {
      this.Mj({copyrightControlOptions: {logoVisible: q}})
    },
    iC: function (a) {
      function b(a, b) {
        return function () {
          a.Tr.push({oN: b, nN: arguments})
        }
      }

      for (var c = a.getPanoMethodList(), e = "", f = 0, g = c.length; f < g; f++) e = c[f], this[e] = b(this, e);
      this.kh.push(a)
    },
    tF: function (a) {
      for (var b = this.kh.length; b--;) this.kh[b] === a && this.kh.splice(b, 1)
    },
    HF: ba()
  });
  var Df = Sa.prototype;
  S(Df, {
    setId: Df.Gc,
    setPosition: Df.va,
    setPov: Df.zd,
    setZoom: Df.Xc,
    setOptions: Df.Mj,
    getId: Df.ac,
    getPosition: Df.ma,
    getPov: Df.Na,
    getZoom: Df.la,
    getLinks: Df.nY,
    getBaiduMapUrl: Df.GL,
    hideMapLogo: Df.Xx,
    showMapLogo: Df.SF,
    enableDoubleClickZoom: Df.h4,
    disableDoubleClickZoom: Df.X3,
    enableScrollWheelZoom: Df.tX,
    disableScrollWheelZoom: Df.VW,
    show: Df.show,
    hide: Df.aa,
    addPlugin: Df.iC,
    removePlugin: Df.tF,
    getVisible: Df.Sx,
    addOverlay: Df.Ra,
    removeOverlay: Df.Lb,
    getSceneType: Df.zt,
    setPanoramaPOIType: Df.uu,
    exitInter: Df.yp,
    setInteractiveState: Df.HF
  });
  S(window, {
    BMAP_PANORAMA_POI_HOTEL: "hotel",
    BMAP_PANORAMA_POI_CATERING: "catering",
    BMAP_PANORAMA_POI_MOVIE: "movie",
    BMAP_PANORAMA_POI_TRANSIT: "transit",
    BMAP_PANORAMA_POI_INDOOR_SCENE: "indoor_scene",
    BMAP_PANORAMA_POI_NONE: "none",
    BMAP_PANORAMA_INDOOR_SCENE: "inter",
    BMAP_PANORAMA_STREET_SCENE: "street"
  });

  function Ef() {
    x.lang.Ja.call(this);
    this.rd = "PanoramaOverlay_" + this.da;
    this.W = s;
    this.Va = q
  }

  x.lang.xa(Ef, x.lang.Ja, "PanoramaOverlayBase");
  x.extend(Ef.prototype, {
    W4: u("rd"), za: function () {
      aa("initialize\u65b9\u6cd5\u672a\u5b9e\u73b0")
    }, remove: function () {
      aa("remove\u65b9\u6cd5\u672a\u5b9e\u73b0")
    }, Tf: function () {
      aa("_setOverlayProperty\u65b9\u6cd5\u672a\u5b9e\u73b0")
    }
  });

  function Ff(a, b) {
    Ef.call(this);
    var c = {position: s, altitude: 2, displayDistance: q}, b = b || {}, e;
    for (e in b) c[e] = b[e];
    this.Ob = c.position;
    this.Zj = a;
    this.Wq = c.altitude;
    this.IR = c.displayDistance;
    this.bG = c.color;
    this.uM = c.hoverColor;
    this.backgroundColor = c.backgroundColor;
    this.sK = c.backgroundHoverColor;
    this.borderColor = c.borderColor;
    this.wK = c.borderHoverColor;
    this.fontSize = c.fontSize;
    this.padding = c.padding;
    this.vE = c.imageUrl;
    this.size = c.size;
    this.De = c.image;
    this.width = c.width;
    this.height = c.height;
    this.fZ = c.imageData;
    this.borderWidth = c.borderWidth
  }

  x.lang.xa(Ff, Ef, "PanoramaLabel");
  x.extend(Ff.prototype, {
    z4: u("borderWidth"),
    getImageData: u("fZ"),
    Tm: u("bG"),
    O4: u("uM"),
    v4: u("backgroundColor"),
    w4: u("sK"),
    x4: u("borderColor"),
    y4: u("wK"),
    M4: u("fontSize"),
    X4: u("padding"),
    P4: u("vE"),
    wb: u("size"),
    Hx: u("De"),
    va: function (a) {
      this.Ob = a;
      this.Tf("position", a)
    },
    ma: u("Ob"),
    Qc: function (a) {
      this.Zj = a;
      this.Tf("content", a)
    },
    Lk: u("Zj"),
    BF: function (a) {
      this.Wq = a;
      this.Tf("altitude", a)
    },
    Bp: u("Wq"),
    Na: function () {
      var a = this.ma(), b = s, c = s;
      this.W && (c = this.W.ma());
      if (a && c) if (a.Vb(c)) b = this.W.Na(); else {
        b = {};
        b.heading = Gf(a.lng - c.lng, a.lat - c.lat) || 0;
        var a = b, c = this.Bp(), e = this.po();
        a.pitch = Math.round(180 * (Math.atan(c / e) / Math.PI)) || 0
      }
      return b
    },
    po: function () {
      var a = 0, b, c;
      this.W && (b = this.W.ma(), (c = this.ma()) && !c.Vb(b) && (a = R.Mk(b, c)));
      return a
    },
    aa: function () {
      aa("hide\u65b9\u6cd5\u672a\u5b9e\u73b0")
    },
    show: function () {
      aa("show\u65b9\u6cd5\u672a\u5b9e\u73b0")
    },
    Tf: ba()
  });
  var Hf = Ff.prototype;
  S(Hf, {
    setPosition: Hf.va,
    getPosition: Hf.ma,
    setContent: Hf.Qc,
    getContent: Hf.Lk,
    setAltitude: Hf.BF,
    getAltitude: Hf.Bp,
    getPov: Hf.Na,
    show: Hf.show,
    hide: Hf.aa
  });

  function If(a, b) {
    Ef.call(this);
    var c = {icon: "", title: "", panoInfo: s, altitude: 2}, b = b || {}, e;
    for (e in b) c[e] = b[e];
    this.Ob = a;
    this.nI = c.icon;
    this.MJ = c.title;
    this.Wq = c.altitude;
    this.ST = c.panoInfo;
    this.Oa = {heading: 0, pitch: 0}
  }

  x.lang.xa(If, Ef, "PanoramaMarker");
  x.extend(If.prototype, {
    va: function (a) {
      this.Ob = a;
      this.Tf("position", a)
    }, ma: u("Ob"), Hc: function (a) {
      this.MJ = a;
      this.Tf("title", a)
    }, Kp: u("MJ"), Xb: function (a) {
      this.nI = icon;
      this.Tf("icon", a)
    }, Ep: u("nI"), BF: function (a) {
      this.Wq = a;
      this.Tf("altitude", a)
    }, Bp: u("Wq"), fE: u("ST"), Na: function () {
      var a = s;
      if (this.W) {
        var a = this.W.ma(), b = this.ma(), a = Gf(b.lng - a.lng, b.lat - a.lat);
        isNaN(a) && (a = 0);
        a = {heading: a, pitch: 0}
      } else a = this.Oa;
      return a
    }, Tf: ba()
  });
  var Jf = If.prototype;
  S(Jf, {
    setPosition: Jf.va,
    getPosition: Jf.ma,
    setTitle: Jf.Hc,
    getTitle: Jf.Kp,
    setAltitude: Jf.BF,
    getAltitude: Jf.Bp,
    getPanoInfo: Jf.fE,
    getIcon: Jf.Ep,
    setIcon: Jf.Xb,
    getPov: Jf.Na
  });

  function Gf(a, b) {
    var c = 0;
    if (0 !== a && 0 !== b) {
      var c = 180 * (Math.atan(a / b) / Math.PI), e = 0;
      0 < a && 0 > b && (e = 90);
      0 > a && 0 > b && (e = 180);
      0 > a && 0 < b && (e = 270);
      c = (c + 90) % 90 + e
    } else 0 === a ? c = 0 > b ? 180 : 0 : 0 === b && (c = 0 < a ? 90 : 270);
    return Math.round(c)
  }

  function Ra(a) {
    if ("boolean" === typeof Kf) return Kf;
    if (a === t || !window.WebGLRenderingContext) return Kf = t;
    if (x.platform.Ej) {
      a = 0;
      try {
        var b = s, c = navigator.userAgent.toLowerCase();
        0 < c.indexOf("android") && (b = (c.match(/android [\d._]+/gi) + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, "."), b = parseInt(b.split(".")[0], 10));
        a = b
      } catch (e) {
        console.error("\u83b7\u53d6\u5b89\u5353\u7248\u672c\u5931\u8d25 => " + e)
      }
      if (5 > a) return Kf = t
    }
    c = document.createElement("canvas");
    a = s;
    try {
      a = c.getContext("webgl")
    } catch (f) {
      Kf = t
    }
    return Kf = a === s ? t : q
  }

  var Kf;

  function Lf() {
    if ("boolean" === typeof Mf) return Mf;
    Mf = q;
    if (x.platform.GE) return q;
    var a = navigator.userAgent;
    return -1 < a.indexOf("Chrome") || -1 < a.indexOf("SAMSUNG-GT-I9508") ? q : Mf = t
  }

  var Mf;

  function cd(a, b) {
    this.W = a || s;
    var c = this;
    c.W && c.ha();
    Wa.load("pservice", function () {
      c.aR()
    });
    "api" == (b || {}).Xe ? Ua(Ka) : Ua(La);
    this.Dd = {
      getPanoramaById: [],
      getPanoramaByLocation: [],
      getVisiblePOIs: [],
      getRecommendPanosById: [],
      getPanoramaVersions: [],
      checkPanoSupportByCityCode: [],
      getPanoramaByPOIId: [],
      getCopyrightProviders: []
    }
  }

  A.Zk(function (a) {
    "flashRender" !== a.Br() && new cd(a, {Xe: "api"})
  });
  x.extend(cd.prototype, {
    ha: function () {
      function a(a) {
        if (a) {
          if (a.id != b.Cw) {
            b.FO(a.id);
            b.ia = a;
            Lf() || b.dispatchEvent(new O("onthumbnail_complete"));
            b.hb != s && (b.Pl = b._position);
            for (var c in a) if (a.hasOwnProperty(c)) switch (b["_" + c] = a[c], c) {
              case "position":
                b.Ob = a[c];
                break;
              case "id":
                b.hb = a[c];
                break;
              case "links":
                b.uo = a[c];
                break;
              case "zoom":
                b.Sc = a[c]
            }
            if (b.Pl) {
              var g = b.Pl, i = b._position;
              c = g.lat;
              var k = i.lat, m = Xb(k - c), g = Xb(i.lng - g.lng);
              c = Math.sin(m / 2) * Math.sin(m / 2) + Math.cos(Xb(c)) * Math.cos(Xb(k)) * Math.sin(g / 2) * Math.sin(g / 2);
              b.GH = 6371E3 * 2 * Math.atan2(Math.sqrt(c), Math.sqrt(1 - c))
            }
            c = new O("ondataload");
            b.show();
            c.data = a;
            b.dispatchEvent(c);
            b.dispatchEvent(new O("onposition_changed"));
            b.dispatchEvent(new O("onlinks_changed"));
            b.dispatchEvent(new O("oncopyright_changed"), {copyright: a.copyright});
            a.vm ? (b.Mj({haveBreakId: q}), Ya() && b.m.closeControl && x.U.show(b.Er)) : x.U.aa(b.Er)
          }
        } else b.hb = b.Ol, b.Ob = b.Pl, b.dispatchEvent(new O("onnoresult"))
      }

      var b = this.W, c = this;
      b.addEventListener("id_changed", function () {
        A.mz("y");
        c.Ip(b.ac(), a)
      });
      b.addEventListener("iid_changed", function () {
        A.mz("y");
        c.vg(cd.yl + "qt=idata&iid=" + b.IA + "&fn=", function (b) {
          if (b && b.result && 0 == b.result.error) {
            var b = b.content[0].interinfo, f = {};
            f.vm = b.BreakID;
            for (var g = b.Defaultfloor, i = s, k = 0; k < b.Floors.length; k++) if (b.Floors[k].Floor == g) {
              i = b.Floors[k];
              break
            }
            f.id = i.StartID || i.Points[0].PID;
            c.Ip(f.id, a, f)
          }
        }, q)
      });
      b.addEventListener("position_changed_inner", function () {
        A.mz("y");
        c.zj(b.ma(), a)
      })
    }, Ip: function (a, b) {
      this.Dd.getPanoramaById.push(arguments)
    }, zj: function (a, b, c) {
      this.Dd.getPanoramaByLocation.push(arguments)
    }, pE: function (a, b, c, e) {
      this.Dd.getVisiblePOIs.push(arguments)
    }, Qx: function (a, b) {
      this.Dd.getRecommendPanosById.push(arguments)
    }, Px: function (a) {
      this.Dd.getPanoramaVersions.push(arguments)
    }, yC: function (a, b) {
      this.Dd.checkPanoSupportByCityCode.push(arguments)
    }, Ox: function (a, b) {
      this.Dd.getPanoramaByPOIId.push(arguments)
    }, KL: function (a) {
      this.Dd.getCopyrightProviders.push(arguments)
    }
  });
  var Nf = cd.prototype;
  S(Nf, {getPanoramaById: Nf.Ip, getPanoramaByLocation: Nf.zj, getPanoramaByPOIId: Nf.Ox});

  function ad(a) {
    Kd.call(this);
    "api" == (a || {}).Xe ? Ua(Ea) : Ua(Fa)
  }

  ad.$G = A.xh("pano", "");
  ad.prototype = new Kd;
  ad.prototype.getTilesUrl = function (a, b) {
    var c = ad.$G[(a.x + a.y) % ad.$G.length] + "?udt=20150114&qt=tile&styles=pl&x=" + a.x + "&y=" + a.y + "&z=" + b;
    x.ga.oa && 6 >= x.ga.oa && (c += "&color_dep=32");
    var e = Tb(c);
    e ? (e = Rc(e.path, {Sp: t}), c += "&" + e) : c = s;
    return c
  };
  ad.prototype.Rt = ea(q);
  Of.ae = new R;

  function Of() {
  }

  x.extend(Of, {
    WW: function (a, b, c) {
      c = x.lang.Uc(c);
      b = {data: b};
      "position_changed" == a && (b.data = Of.ae.Kj(new Q(b.data.mercatorX, b.data.mercatorY)));
      c.dispatchEvent(new O("on" + a), b)
    }
  });
  var Pf = Of;
  S(Pf, {dispatchFlashEvent: Pf.WW});
  var Qf = {XP: 50};
  Qf.fv = A.xh("pano")[0];
  Qf.cv = {width: 220, height: 60};
  x.extend(Qf, {
    DM: function (a, b, c, e) {
      if (!b || !c || !c.lngLat || !c.panoInstance) e(); else {
        this.Co === l && (this.Co = new cd(s, {Xe: "api"}));
        var f = this;
        this.Co.yC(b, function (b) {
          b ? f.Co.zj(c.lngLat, Qf.XP, function (b) {
            if (b && b.id) {
              var g = b.id, m = b.Fh, b = b.Gh, n = cd.ae.Lg(c.lngLat), o = f.FS(n, {x: m, y: b}),
                m = f.VL(g, o, 0, Qf.cv.width, Qf.cv.height);
              a.content = f.GS(a.content, m, c.titleTip, c.beforeDomId);
              a.addEventListener("open", function () {
                ka.V(x.Ic("infoWndPano"), "click", function () {
                  c.panoInstance.Gc(g);
                  c.panoInstance.show();
                  c.panoInstance.zd({heading: o, pitch: 0})
                })
              })
            }
            e()
          }) : e()
        })
      }
    }, GS: function (a, b, c, e) {
      var c = c || "", f;
      !e || !a.split(e)[0] ? (e = a, a = "") : (e = a.split(e)[0], f = e.lastIndexOf("<"), e = a.substring(0, f), a = a.substring(f));
      f = [];
      var g = Qf.cv.width, i = Qf.cv.height;
      f.push(e);
      f.push("<div id='infoWndPano' class='panoInfoBox' style='height:" + i + "px;width:" + g + "px; margin-top: -19px;'>");
      f.push("<img class='pano_thumnail_img' width='" + g + "' height='" + i + "' border='0' alt='" + c + "\u5916\u666f' title='" + c + "\u5916\u666f' src='" + b + "' onerror='Pano.PanoEntranceUtil.thumbnailNotFound(this, " + g + ", " + i + ");' />");
      f.push("<div class='panoInfoBoxTitleBg' style='width:" + g + "px;'></div><a href='javascript:void(0)' class='panoInfoBoxTitleContent' >\u8fdb\u5165\u5168\u666f&gt;&gt;</a>");
      f.push("</div>");
      f.push(a);
      return f.join("")
    }, FS: function (a, b) {
      var c = 90 - 180 * Math.atan2(a.y - b.y, a.x - b.x) / Math.PI;
      0 > c && (c += 360);
      return c
    }, VL: function (a, b, c, e, f) {
      var g = Qf.fv + "?qt=pr3d&fovy=75&quality=80&panoid={panoId}&heading={panoHeading}&pitch={panoPitch}&width={width}&height={height}",
        i = {panoId: a, panoHeading: b || 0, panoPitch: c || 0, width: e, height: f},
        g = g.replace(/\{(.*?)\}/g, function (a, b) {
          return i[b]
        });
      return (a = Tb(g)) ? (a = Rc(a.path, {Sp: t}), g + ("&" + a)) : s
    }
  });
  var Rf = document, Sf = Math, Tf = Rf.createElement("div").style, Uf;
  a:{
    for (var Vf = ["t", "webkitT", "MozT", "msT", "OT"], Wf, Xf = 0, Yf = Vf.length; Xf < Yf; Xf++) if (Wf = Vf[Xf] + "ransform", Wf in Tf) {
      Uf = Vf[Xf].substr(0, Vf[Xf].length - 1);
      break a
    }
    Uf = t
  }
  var Zf = Uf ? "-" + Uf.toLowerCase() + "-" : "", ag = $f("transform"), cg = $f("transitionProperty"),
    dg = $f("transitionDuration"), eg = $f("transformOrigin"), fg = $f("transitionTimingFunction"),
    gg = $f("transitionDelay"), rf = /android/gi.test(navigator.appVersion),
    hg = /iphone|ipad/gi.test(navigator.appVersion), ig = /hp-tablet/gi.test(navigator.appVersion),
    jg = $f("perspective") in Tf, kg = "ontouchstart" in window && !ig, lg = Uf !== t, mg = $f("transition") in Tf,
    ng = "onorientationchange" in window ? "orientationchange" : "resize", og = kg ? "touchstart" : "mousedown",
    pg = kg ? "touchmove" : "mousemove", qg = kg ? "touchend" : "mouseup", rg = kg ? "touchcancel" : "mouseup",
    sg = Uf === t ? t : {
      "": "transitionend",
      webkit: "webkitTransitionEnd",
      Moz: "transitionend",
      O: "otransitionend",
      ms: "MSTransitionEnd"
    }[Uf],
    tg = window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame || function (a) {
      return setTimeout(a, 1)
    },
    ug = window.cancelRequestAnimationFrame || window.L7 || window.webkitCancelRequestAnimationFrame || window.mozCancelRequestAnimationFrame || window.oCancelRequestAnimationFrame || window.msCancelRequestAnimationFrame || clearTimeout,
    vg = jg ? " translateZ(0)" : "";

  function wg(a, b) {
    var c = this, e;
    c.Pn = "object" == typeof a ? a : Rf.getElementById(a);
    c.Pn.style.overflow = "hidden";
    c.Sb = c.Pn.children[0];
    c.options = {
      Op: q,
      Hn: q,
      x: 0,
      y: 0,
      dp: q,
      PV: t,
      vy: q,
      UE: q,
      nl: q,
      Oi: t,
      f1: 0,
      bx: t,
      Ux: q,
      vi: q,
      Pi: q,
      GD: rf,
      Yx: hg,
      CX: hg && jg,
      yF: "",
      zoom: t,
      tl: 1,
      Gq: 4,
      YW: 2,
      CP: "scroll",
      Cu: t,
      dz: 1,
      HN: s,
      zN: function (a) {
        a.preventDefault()
      },
      KN: s,
      yN: s,
      JN: s,
      xN: s,
      zy: s,
      LN: s,
      CN: s,
      fq: s,
      MN: s,
      dq: s
    };
    for (e in b) c.options[e] = b[e];
    c.x = c.options.x;
    c.y = c.options.y;
    c.options.nl = lg && c.options.nl;
    c.options.vi = c.options.Op && c.options.vi;
    c.options.Pi = c.options.Hn && c.options.Pi;
    c.options.zoom = c.options.nl && c.options.zoom;
    c.options.Oi = mg && c.options.Oi;
    c.options.zoom && rf && (vg = "");
    c.Sb.style[cg] = c.options.nl ? Zf + "transform" : "top left";
    c.Sb.style[dg] = "0";
    c.Sb.style[eg] = "0 0";
    c.options.Oi && (c.Sb.style[fg] = "cubic-bezier(0.33,0.66,0.66,1)");
    c.options.nl ? c.Sb.style[ag] = "translate(" + c.x + "px," + c.y + "px)" + vg : c.Sb.style.cssText += ";position:absolute;top:" + c.y + "px;left:" + c.x + "px";
    c.options.Oi && (c.options.GD = q);
    c.refresh();
    c.ha(ng, window);
    c.ha(og);
    !kg && "none" != c.options.CP && (c.ha("DOMMouseScroll"), c.ha("mousewheel"));
    c.options.bx && (c.aW = setInterval(function () {
      c.ZQ()
    }, 500));
    this.options.Ux && (Event.prototype.stopImmediatePropagation || (document.body.removeEventListener = function (a, b, c) {
      var e = Node.prototype.removeEventListener;
      a === "click" ? e.call(document.body, a, b.tM || b, c) : e.call(document.body, a, b, c)
    }, document.body.addEventListener = function (a, b, c) {
      var e = Node.prototype.addEventListener;
      a === "click" ? e.call(document.body, a, b.tM || (b.tM = function (a) {
        a.H_ || b(a)
      }), c) : e.call(document.body, a, b, c)
    }), c.ha("click", document.body, q))
  }

  wg.prototype = {
    enabled: q, x: 0, y: 0, Nj: [], scale: 1, OC: 0, PC: 0, af: [], yf: [], qC: s, pz: 0, handleEvent: function (a) {
      switch (a.type) {
        case og:
          if (!kg && 0 !== a.button) break;
          this.vw(a);
          break;
        case pg:
          this.CT(a);
          break;
        case qg:
        case rg:
          this.Bv(a);
          break;
        case ng:
          this.EB();
          break;
        case "DOMMouseScroll":
        case "mousewheel":
          this.gV(a);
          break;
        case sg:
          this.cV(a);
          break;
        case "click":
          this.iR(a)
      }
    }, ZQ: function () {
      !this.Eh && (!this.ul && !(this.sm || this.Sy == this.Sb.offsetWidth * this.scale && this.pq == this.Sb.offsetHeight * this.scale)) && this.refresh()
    }, lw: function (a) {
      var b;
      this[a + "Scrollbar"] ? (this[a + "ScrollbarWrapper"] || (b = Rf.createElement("div"), this.options.yF ? b.className = this.options.yF + a.toUpperCase() : b.style.cssText = "position:absolute;z-index:100;" + ("h" == a ? "height:7px;bottom:1px;left:2px;right:" + (this.Pi ? "7" : "2") + "px" : "width:7px;bottom:" + (this.vi ? "7" : "2") + "px;top:2px;right:1px"), b.style.cssText += ";pointer-events:none;" + Zf + "transition-property:opacity;" + Zf + "transition-duration:" + (this.options.CX ? "350ms" : "0") + ";overflow:hidden;opacity:" + (this.options.Yx ? "0" : "1"), this.Pn.appendChild(b), this[a + "ScrollbarWrapper"] = b, b = Rf.createElement("div"), this.options.yF || (b.style.cssText = "position:absolute;z-index:100;background:rgba(0,0,0,0.5);border:1px solid rgba(255,255,255,0.9);" + Zf + "background-clip:padding-box;" + Zf + "box-sizing:border-box;" + ("h" == a ? "height:100%" : "width:100%") + ";" + Zf + "border-radius:3px;border-radius:3px"), b.style.cssText += ";pointer-events:none;" + Zf + "transition-property:" + Zf + "transform;" + Zf + "transition-timing-function:cubic-bezier(0.33,0.66,0.66,1);" + Zf + "transition-duration:0;" + Zf + "transform: translate(0,0)" + vg, this.options.Oi && (b.style.cssText += ";" + Zf + "transition-timing-function:cubic-bezier(0.33,0.66,0.66,1)"), this[a + "ScrollbarWrapper"].appendChild(b), this[a + "ScrollbarIndicator"] = b), "h" == a ? (this.pM = this.qM.clientWidth, this.YY = Sf.max(Sf.round(this.pM * this.pM / this.Sy), 8), this.XY.style.width = this.YY + "px") : (this.sP = this.tP.clientHeight, this.C1 = Sf.max(Sf.round(this.sP * this.sP / this.pq), 8), this.B1.style.height = this.C1 + "px"), this.FB(a, q)) : this[a + "ScrollbarWrapper"] && (lg && (this[a + "ScrollbarIndicator"].style[ag] = ""), this[a + "ScrollbarWrapper"].parentNode.removeChild(this[a + "ScrollbarWrapper"]), this[a + "ScrollbarWrapper"] = s, this[a + "ScrollbarIndicator"] = s)
    }, EB: function () {
      var a = this;
      setTimeout(function () {
        a.refresh()
      }, rf ? 200 : 0)
    }, Wr: function (a, b) {
      this.ul || (a = this.Op ? a : 0, b = this.Hn ? b : 0, this.options.nl ? this.Sb.style[ag] = "translate(" + a + "px," + b + "px) scale(" + this.scale + ")" + vg : (a = Sf.round(a), b = Sf.round(b), this.Sb.style.left = a + "px", this.Sb.style.top = b + "px"), this.x = a, this.y = b, this.FB("h"), this.FB("v"))
    }, FB: function (a, b) {
      var c = "h" == a ? this.x : this.y;
      this[a + "Scrollbar"] && (c *= this[a + "ScrollbarProp"], 0 > c ? (this.options.GD || (c = this[a + "ScrollbarIndicatorSize"] + Sf.round(3 * c), 8 > c && (c = 8), this[a + "ScrollbarIndicator"].style["h" == a ? "width" : "height"] = c + "px"), c = 0) : c > this[a + "ScrollbarMaxScroll"] && (this.options.GD ? c = this[a + "ScrollbarMaxScroll"] : (c = this[a + "ScrollbarIndicatorSize"] - Sf.round(3 * (c - this[a + "ScrollbarMaxScroll"])), 8 > c && (c = 8), this[a + "ScrollbarIndicator"].style["h" == a ? "width" : "height"] = c + "px", c = this[a + "ScrollbarMaxScroll"] + (this[a + "ScrollbarIndicatorSize"] - c))), this[a + "ScrollbarWrapper"].style[gg] = "0", this[a + "ScrollbarWrapper"].style.opacity = b && this.options.Yx ? "0" : "1", this[a + "ScrollbarIndicator"].style[ag] = "translate(" + ("h" == a ? c + "px,0)" : "0," + c + "px)") + vg)
    }, iR: function (a) {
      if (a.aS === q) return this.eC = a.target, this.zx = Date.now(), q;
      if (this.eC && this.zx) {
        if (600 < Date.now() - this.zx) return this.zx = this.eC = s, q
      } else {
        for (var b = a.target; b != this.Sb && b != document.body;) b = b.parentNode;
        if (b == document.body) return q
      }
      for (b = a.target; 1 != b.nodeType;) b = b.parentNode;
      b = b.tagName.toLowerCase();
      if ("select" != b && "input" != b && "textarea" != b) return a.stopImmediatePropagation ? a.stopImmediatePropagation() : a.H_ = q, a.stopPropagation(), a.preventDefault(), this.zx = this.eC = s, t
    }, vw: function (a) {
      var b = kg ? a.touches[0] : a, c, e;
      if (this.enabled) {
        this.options.zN && this.options.zN.call(this, a);
        (this.options.Oi || this.options.zoom) && this.QJ(0);
        this.ul = this.sm = this.Eh = t;
        this.YC = this.XC = this.Ow = this.Nw = this.cD = this.bD = 0;
        this.options.zoom && (kg && 1 < a.touches.length) && (e = Sf.abs(a.touches[0].pageX - a.touches[1].pageX), c = Sf.abs(a.touches[0].pageY - a.touches[1].pageY), this.h1 = Sf.sqrt(e * e + c * c), this.By = Sf.abs(a.touches[0].pageX + a.touches[1].pageX - 2 * this.sG) / 2 - this.x, this.Cy = Sf.abs(a.touches[0].pageY + a.touches[1].pageY - 2 * this.tG) / 2 - this.y, this.options.fq && this.options.fq.call(this, a));
        if (this.options.vy && (this.options.nl ? (c = getComputedStyle(this.Sb, s)[ag].replace(/[^0-9\-.,]/g, "").split(","), e = +(c[12] || c[4]), c = +(c[13] || c[5])) : (e = +getComputedStyle(this.Sb, s).left.replace(/[^0-9-]/g, ""), c = +getComputedStyle(this.Sb, s).top.replace(/[^0-9-]/g, "")), e != this.x || c != this.y)) this.options.Oi ? this.ee(sg) : ug(this.qC), this.Nj = [], this.Wr(e, c), this.options.zy && this.options.zy.call(this);
        this.Pw = this.x;
        this.Qw = this.y;
        this.Gu = this.x;
        this.Hu = this.y;
        this.Fh = b.pageX;
        this.Gh = b.pageY;
        this.startTime = a.timeStamp || Date.now();
        this.options.KN && this.options.KN.call(this, a);
        this.ha(pg, window);
        this.ha(qg, window);
        this.ha(rg, window)
      }
    }, CT: function (a) {
      var b = kg ? a.touches[0] : a, c = b.pageX - this.Fh, e = b.pageY - this.Gh, f = this.x + c, g = this.y + e,
        i = a.timeStamp || Date.now();
      this.options.yN && this.options.yN.call(this, a);
      if (this.options.zoom && kg && 1 < a.touches.length) f = Sf.abs(a.touches[0].pageX - a.touches[1].pageX), g = Sf.abs(a.touches[0].pageY - a.touches[1].pageY), this.g1 = Sf.sqrt(f * f + g * g), this.ul = q, b = 1 / this.h1 * this.g1 * this.scale, b < this.options.tl ? b = 0.5 * this.options.tl * Math.pow(2, b / this.options.tl) : b > this.options.Gq && (b = 2 * this.options.Gq * Math.pow(0.5, this.options.Gq / b)), this.Wp = b / this.scale, f = this.By - this.By * this.Wp + this.x, g = this.Cy - this.Cy * this.Wp + this.y, this.Sb.style[ag] = "translate(" + f + "px," + g + "px) scale(" + b + ")" + vg, this.options.MN && this.options.MN.call(this, a); else {
        this.Fh = b.pageX;
        this.Gh = b.pageY;
        if (0 < f || f < this.qe) f = this.options.dp ? this.x + c / 2 : 0 <= f || 0 <= this.qe ? 0 : this.qe;
        if (g > this.wf || g < this.yd) g = this.options.dp ? this.y + e / 2 : g >= this.wf || 0 <= this.yd ? this.wf : this.yd;
        this.bD += c;
        this.cD += e;
        this.Nw = Sf.abs(this.bD);
        this.Ow = Sf.abs(this.cD);
        6 > this.Nw && 6 > this.Ow || (this.options.UE && (this.Nw > this.Ow + 5 ? (g = this.y, e = 0) : this.Ow > this.Nw + 5 && (f = this.x, c = 0)), this.Eh = q, this.Wr(f, g), this.XC = 0 < c ? -1 : 0 > c ? 1 : 0, this.YC = 0 < e ? -1 : 0 > e ? 1 : 0, 300 < i - this.startTime && (this.startTime = i, this.Gu = this.x, this.Hu = this.y), this.options.JN && this.options.JN.call(this, a))
      }
    }, Bv: function (a) {
      if (!(kg && 0 !== a.touches.length)) {
        var b = this, c = kg ? a.changedTouches[0] : a, e, f, g = {Ia: 0, time: 0}, i = {Ia: 0, time: 0},
          k = (a.timeStamp || Date.now()) - b.startTime;
        e = b.x;
        f = b.y;
        b.ee(pg, window);
        b.ee(qg, window);
        b.ee(rg, window);
        b.options.xN && b.options.xN.call(b, a);
        if (b.ul) e = b.scale * b.Wp, e = Math.max(b.options.tl, e), e = Math.min(b.options.Gq, e), b.Wp = e / b.scale, b.scale = e, b.x = b.By - b.By * b.Wp + b.x, b.y = b.Cy - b.Cy * b.Wp + b.y, b.Sb.style[dg] = "200ms", b.Sb.style[ag] = "translate(" + b.x + "px," + b.y + "px) scale(" + b.scale + ")" + vg, b.ul = t, b.refresh(), b.options.dq && b.options.dq.call(b, a); else {
          if (b.Eh) {
            if (300 > k && b.options.vy) {
              g = e ? b.EI(e - b.Gu, k, -b.x, b.Sy - b.Wu + b.x, b.options.dp ? b.Wu : 0) : g;
              i = f ? b.EI(f - b.Hu, k, -b.y, 0 > b.yd ? b.pq - b.Qn + b.y - b.wf : 0, b.options.dp ? b.Qn : 0) : i;
              e = b.x + g.Ia;
              f = b.y + i.Ia;
              if (0 < b.x && 0 < e || b.x < b.qe && e < b.qe) g = {Ia: 0, time: 0};
              if (b.y > b.wf && f > b.wf || b.y < b.yd && f < b.yd) i = {Ia: 0, time: 0}
            }
            g.Ia || i.Ia ? (c = Sf.max(Sf.max(g.time, i.time), 10), b.options.Cu && (g = e - b.Pw, i = f - b.Qw, Sf.abs(g) < b.options.dz && Sf.abs(i) < b.options.dz ? b.scrollTo(b.Pw, b.Qw, 200) : (g = b.DJ(e, f), e = g.x, f = g.y, c = Sf.max(g.time, c))), b.scrollTo(Sf.round(e), Sf.round(f), c)) : b.options.Cu ? (g = e - b.Pw, i = f - b.Qw, Sf.abs(g) < b.options.dz && Sf.abs(i) < b.options.dz ? b.scrollTo(b.Pw, b.Qw, 200) : (g = b.DJ(b.x, b.y), (g.x != b.x || g.y != b.y) && b.scrollTo(g.x, g.y, g.time))) : b.Fo(200)
          } else {
            if (kg) if (b.$K && b.options.zoom) clearTimeout(b.$K), b.$K = s, b.options.fq && b.options.fq.call(b, a), b.zoom(b.Fh, b.Gh, 1 == b.scale ? b.options.YW : 1), b.options.dq && setTimeout(function () {
              b.options.dq.call(b, a)
            }, 200); else if (this.options.Ux) {
              for (e = c.target; 1 != e.nodeType;) e = e.parentNode;
              f = e.tagName.toLowerCase();
              "select" != f && "input" != f && "textarea" != f ? (f = Rf.createEvent("MouseEvents"), f.initMouseEvent("click", q, q, a.view, 1, c.screenX, c.screenY, c.clientX, c.clientY, a.ctrlKey, a.altKey, a.shiftKey, a.metaKey, 0, s), f.aS = q, e.dispatchEvent(f)) : e.focus()
            }
            b.Fo(400)
          }
          b.options.LN && b.options.LN.call(b, a)
        }
      }
    }, Fo: function (a) {
      var b = 0 <= this.x ? 0 : this.x < this.qe ? this.qe : this.x,
        c = this.y >= this.wf || 0 < this.yd ? this.wf : this.y < this.yd ? this.yd : this.y;
      if (b == this.x && c == this.y) {
        if (this.Eh && (this.Eh = t, this.options.zy && this.options.zy.call(this)), this.vi && this.options.Yx && ("webkit" == Uf && (this.qM.style[gg] = "300ms"), this.qM.style.opacity = "0"), this.Pi && this.options.Yx) "webkit" == Uf && (this.tP.style[gg] = "300ms"), this.tP.style.opacity = "0"
      } else this.scrollTo(b, c, a || 0)
    }, gV: function (a) {
      var b = this, c, e;
      if ("wheelDeltaX" in a) c = a.wheelDeltaX / 12, e = a.wheelDeltaY / 12; else if ("wheelDelta" in a) c = e = a.wheelDelta / 12; else if ("detail" in a) c = e = 3 * -a.detail; else return;
      if ("zoom" == b.options.CP) {
        if (e = b.scale * Math.pow(2, 1 / 3 * (e ? e / Math.abs(e) : 0)), e < b.options.tl && (e = b.options.tl), e > b.options.Gq && (e = b.options.Gq), e != b.scale) !b.pz && b.options.fq && b.options.fq.call(b, a), b.pz++, b.zoom(a.pageX, a.pageY, e, 400), setTimeout(function () {
          b.pz--;
          !b.pz && b.options.dq && b.options.dq.call(b, a)
        }, 400)
      } else c = b.x + c, e = b.y + e, 0 < c ? c = 0 : c < b.qe && (c = b.qe), e > b.wf ? e = b.wf : e < b.yd && (e = b.yd), 0 > b.yd && b.scrollTo(c, e, 0)
    }, cV: function (a) {
      a.target == this.Sb && (this.ee(sg), this.RB())
    }, RB: function () {
      var a = this, b = a.x, c = a.y, e = Date.now(), f, g, i;
      a.sm || (a.Nj.length ? (f = a.Nj.shift(), f.x == b && f.y == c && (f.time = 0), a.sm = q, a.Eh = q, a.options.Oi) ? (a.QJ(f.time), a.Wr(f.x, f.y), a.sm = t, f.time ? a.ha(sg) : a.Fo(0)) : (i = function () {
        var k = Date.now(), m;
        if (k >= e + f.time) {
          a.Wr(f.x, f.y);
          a.sm = t;
          a.options.g_ && a.options.g_.call(a);
          a.RB()
        } else {
          k = (k - e) / f.time - 1;
          g = Sf.sqrt(1 - k * k);
          k = (f.x - b) * g + b;
          m = (f.y - c) * g + c;
          a.Wr(k, m);
          if (a.sm) a.qC = tg(i)
        }
      }, i()) : a.Fo(400))
    }, QJ: function (a) {
      a += "ms";
      this.Sb.style[dg] = a;
      this.vi && (this.XY.style[dg] = a);
      this.Pi && (this.B1.style[dg] = a)
    }, EI: function (a, b, c, e, f) {
      var b = Sf.abs(a) / b, g = b * b / 0.0012;
      0 < a && g > c ? (c += f / (6 / (6.0E-4 * (g / b))), b = b * c / g, g = c) : 0 > a && g > e && (e += f / (6 / (6.0E-4 * (g / b))), b = b * e / g, g = e);
      return {Ia: g * (0 > a ? -1 : 1), time: Sf.round(b / 6.0E-4)}
    }, qk: function (a) {
      for (var b = -a.offsetLeft, c = -a.offsetTop; a = a.offsetParent;) b -= a.offsetLeft, c -= a.offsetTop;
      a != this.Pn && (b *= this.scale, c *= this.scale);
      return {left: b, top: c}
    }, DJ: function (a, b) {
      var c, e, f;
      f = this.af.length - 1;
      c = 0;
      for (e = this.af.length; c < e; c++) if (a >= this.af[c]) {
        f = c;
        break
      }
      f == this.OC && (0 < f && 0 > this.XC) && f--;
      a = this.af[f];
      e = (e = Sf.abs(a - this.af[this.OC])) ? 500 * (Sf.abs(this.x - a) / e) : 0;
      this.OC = f;
      f = this.yf.length - 1;
      for (c = 0; c < f; c++) if (b >= this.yf[c]) {
        f = c;
        break
      }
      f == this.PC && (0 < f && 0 > this.YC) && f--;
      b = this.yf[f];
      c = (c = Sf.abs(b - this.yf[this.PC])) ? 500 * (Sf.abs(this.y - b) / c) : 0;
      this.PC = f;
      f = Sf.round(Sf.max(e, c)) || 200;
      return {x: a, y: b, time: f}
    }, ha: function (a, b, c) {
      (b || this.Sb).addEventListener(a, this, !!c)
    }, ee: function (a, b, c) {
      (b || this.Sb).removeEventListener(a, this, !!c)
    }, VC: ia(2), refresh: function () {
      var a, b, c, e = 0;
      b = 0;
      this.scale < this.options.tl && (this.scale = this.options.tl);
      this.Wu = this.Pn.clientWidth || 1;
      this.Qn = this.Pn.clientHeight || 1;
      this.wf = -this.options.f1 || 0;
      this.Sy = Sf.round(this.Sb.offsetWidth * this.scale);
      this.pq = Sf.round((this.Sb.offsetHeight + this.wf) * this.scale);
      this.qe = this.Wu - this.Sy;
      this.yd = this.Qn - this.pq + this.wf;
      this.YC = this.XC = 0;
      this.options.HN && this.options.HN.call(this);
      this.Op = this.options.Op && 0 > this.qe;
      this.Hn = this.options.Hn && (!this.options.PV && !this.Op || this.pq > this.Qn);
      this.vi = this.Op && this.options.vi;
      this.Pi = this.Hn && this.options.Pi && this.pq > this.Qn;
      a = this.qk(this.Pn);
      this.sG = -a.left;
      this.tG = -a.top;
      if ("string" == typeof this.options.Cu) {
        this.af = [];
        this.yf = [];
        c = this.Sb.querySelectorAll(this.options.Cu);
        a = 0;
        for (b = c.length; a < b; a++) e = this.qk(c[a]), e.left += this.sG, e.top += this.tG, this.af[a] = e.left < this.qe ? this.qe : e.left * this.scale, this.yf[a] = e.top < this.yd ? this.yd : e.top * this.scale
      } else if (this.options.Cu) {
        for (this.af = []; e >= this.qe;) this.af[b] = e, e -= this.Wu, b++;
        this.qe % this.Wu && (this.af[this.af.length] = this.qe - this.af[this.af.length - 1] + this.af[this.af.length - 1]);
        b = e = 0;
        for (this.yf = []; e >= this.yd;) this.yf[b] = e, e -= this.Qn, b++;
        this.yd % this.Qn && (this.yf[this.yf.length] = this.yd - this.yf[this.yf.length - 1] + this.yf[this.yf.length - 1])
      }
      this.lw("h");
      this.lw("v");
      this.ul || (this.Sb.style[dg] = "0", this.Fo(400))
    }, scrollTo: function (a, b, c, e) {
      var f = a;
      this.stop();
      f.length || (f = [{x: a, y: b, time: c, J_: e}]);
      a = 0;
      for (b = f.length; a < b; a++) f[a].J_ && (f[a].x = this.x - f[a].x, f[a].y = this.y - f[a].y), this.Nj.push({
        x: f[a].x,
        y: f[a].y,
        time: f[a].time || 0
      });
      this.RB()
    }, disable: function () {
      this.stop();
      this.Fo(0);
      this.enabled = t;
      this.ee(pg, window);
      this.ee(qg, window);
      this.ee(rg, window)
    }, enable: function () {
      this.enabled = q
    }, stop: function () {
      this.options.Oi ? this.ee(sg) : ug(this.qC);
      this.Nj = [];
      this.sm = this.Eh = t
    }, zoom: function (a, b, c, e) {
      var f = c / this.scale;
      this.options.nl && (this.ul = q, e = e === l ? 200 : e, a = a - this.sG - this.x, b = b - this.tG - this.y, this.x = a - a * f + this.x, this.y = b - b * f + this.y, this.scale = c, this.refresh(), this.x = 0 < this.x ? 0 : this.x < this.qe ? this.qe : this.x, this.y = this.y > this.wf ? this.wf : this.y < this.yd ? this.yd : this.y, this.Sb.style[dg] = e + "ms", this.Sb.style[ag] = "translate(" + this.x + "px," + this.y + "px) scale(" + c + ")" + vg, this.ul = t)
    }
  };

  function $f(a) {
    if ("" === Uf) return a;
    a = a.charAt(0).toUpperCase() + a.substr(1);
    return Uf + a
  }

  Tf = s;

  function xg(a) {
    this.m = {anchor: Vc, offset: new M(0, 0), maxWidth: "100%", imageHeight: 80};
    var a = a || {}, b;
    for (b in a) this.m[b] = a[b];
    this.hm = new cd(s, {Xe: "api"});
    this.rk = [];
    this.W = s;
    this.mg = {height: this.m.imageHeight, width: this.m.imageHeight * yg};
    this.Yc = this.GB = this.vm = this.ed = s
  }

  var zg = [0, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 4, 5, 5, 5, 6, 6, 7, 8, 8, 8, 9, 10],
    Ag = "\u5176\u4ed6 \u6b63\u95e8 \u623f\u578b \u8bbe\u65bd \u6b63\u95e8 \u9910\u996e\u8bbe\u65bd \u5176\u4ed6\u8bbe\u65bd \u6b63\u95e8 \u8bbe\u65bd \u89c2\u5f71\u5385 \u5176\u4ed6\u8bbe\u65bd".split(" ");
  A.Zk(function (a) {
    var b = s;
    a.addEventListener("position_changed", function () {
      a.m.visible && a.m.albumsControl === q && (b ? b.Oy(a.ac()) : (b = new xg(a.m.albumsControlOptions), b.za(a)))
    });
    a.addEventListener("albums_visible_changed", function () {
      a.m.albumsControl === q ? (b ? b.Oy(a.ac()) : (b = new xg(a.m.albumsControlOptions), b.za(a)), b.show()) : b.aa()
    });
    a.addEventListener("albums_options_changed", function () {
      b && b.Mj(a.m.albumsControlOptions)
    });
    a.addEventListener("visible_changed", function () {
      b && (a.Sx() ? a.m.albumsControl === q && (b.R.style.visibility = "visible") : b.R.style.visibility = "hidden")
    })
  });
  var yg = 1.8;
  K() && (yg = 1);
  x.extend(xg.prototype, {
    Mj: function (a) {
      for (var b in a) this.m[b] = a[b];
      a = this.m.imageHeight + "px";
      this.wc(this.m.anchor);
      this.R.style.width = isNaN(Number(this.m.maxWidth)) === q ? this.m.maxWidth : this.m.maxWidth + "px";
      this.R.style.height = a;
      this.vk.style.height = a;
      this.ci.style.height = a;
      this.mg = {height: this.m.imageHeight, width: this.m.imageHeight * yg};
      this.uk.style.height = this.mg.height - 6 + "px";
      this.uk.style.width = this.mg.width - 6 + "px";
      this.Oy(this.W.ac(), q)
    }, za: function (a) {
      this.W = a;
      this.Js();
      this.HQ();
      this.mZ();
      this.Oy(a.ac())
    }, Js: function () {
      var a = this.m.imageHeight + "px";
      this.R = F("div");
      var b = this.R.style;
      b.cssText = "background:rgb(37,37,37);background:rgba(37,37,37,0.9);";
      b.position = "absolute";
      b.zIndex = "2000";
      b.width = isNaN(Number(this.m.maxWidth)) === q ? this.m.maxWidth : this.m.maxWidth + "px";
      b.padding = "8px 0";
      b.visibility = "hidden";
      b.height = a;
      this.vk = F("div");
      b = this.vk.style;
      b.position = "absolute";
      b.overflow = "hidden";
      b.width = "100%";
      b.height = a;
      this.ci = F("div");
      b = this.ci.style;
      b.height = a;
      this.vk.appendChild(this.ci);
      this.R.appendChild(this.vk);
      this.W.R.appendChild(this.R);
      this.uk = F("div", {"class": "pano_photo_item_seleted"});
      this.uk.style.height = this.mg.height - 6 + "px";
      this.uk.style.width = this.mg.width - 6 + "px";
      this.wc(this.m.anchor)
    }, ZH: function (a) {
      for (var b = this.rk, c = b.length - 1; 0 <= c; c--) if (b[c].panoId == a) return c;
      return -1
    }, Oy: function (a, b) {
      if (b || !this.rk[this.ed] || !(this.rk[this.ed].panoId == a && 3 !== this.rk[this.ed].recoType)) {
        var c = this, e = this.ZH(a);
        !b && -1 !== e && this.rk[e] && 3 !== this.rk[e].recoType ? this.tq(e) : this.CY(function (a) {
          for (var b = {}, e, k, m = t, n = [], o = 0, p = a.length; o < p; o++) e = a[o].catlog, k = a[o].floor, l !== e && ("" === e && l !== k ? (m = q, b[k] || (b[k] = []), b[k].push(a[o])) : (b[zg[e]] || (b[zg[e]] = []), b[zg[e]].push(a[o])));
          for (var v in b) m ? n.push({data: v + "F", index: v}) : n.push({data: Ag[v], index: v});
          c.uH = b;
          c.cj = n;
          c.jj(a);
          0 == a.length ? c.aa() : c.show()
        })
      }
    }, DW: function () {
      if (!this.Zi) {
        var a = this.qY(this.cj), b = F("div");
        b.style.cssText = ["width:" + 134 * this.cj.length + "px;", "overflow:hidden;-ms-user-select:none;-moz-user-select:none;-webkit-user-select:none;"].join("");
        b.innerHTML = a;
        a = F("div");
        a.appendChild(b);
        a.style.cssText = "position:absolute;top:-25px;background:rgb(37,37,37);background:rgba(37,37,37,0.9);border-bottom:1px solid #4e596a;width:100%;line-height:25px;height:25px;overflow:scroll;outline:0";
        new wg(a, {dp: t, vy: q, vi: t, Pi: t, Hn: t, UE: q, bx: q, Ux: q});
        this.R.appendChild(a);
        for (var c = this, e = b.getElementsByTagName("span"), f = 0, g = e.length; f < g; f++) b = e[f], x.V(b, "click", function () {
          if (this.getAttribute("dataindex")) {
            c.jj(c.uH[this.getAttribute("dataindex")]);
            for (var a = 0, b = e.length; a < b; a++) e[a].style.color = "#FFFFFF";
            this.style.color = "#3383FF"
          }
        });
        this.Zi = a
      }
    }, zW: function () {
      if (this.Zi) a = this.IL(this.cj), this.XQ.innerHTML = a; else {
        var a = this.IL(this.cj), b = F("ul"), c = this;
        b.style.cssText = "list-style: none;padding:0px;margin:0px;display:block;width:60px;position:absolute;top:7px";
        b.innerHTML = a;
        x.V(b, "click", function (a) {
          if (a = (a.srcElement || a.target).getAttribute("dataindex")) {
            c.jj(c.uH[a]);
            for (var e = b.getElementsByTagName("li"), f = 0, g = e.length; f < g; f++) e[f].childNodes[0].getAttribute("dataindex") === a ? x.U.ib(e[f], "pano_catlogLiActive") : x.U.rc(e[f], "pano_catlogLiActive")
          }
        });
        var a = F("div"), e = F("a"), f = F("span"), g = F("a"), i = F("span"),
          k = ["background:url(" + J.ta + "panorama/catlog_icon.png) no-repeat;", "display:block;width:10px;height:7px;margin:0 auto;"].join("");
        f.style.cssText = k + "background-position:-18px 0;";
        e.style.cssText = "background:#1C1C1C;display:block;position:absolute;width:58px;";
        i.style.cssText = k + "background-position:0 0;";
        g.style.cssText = "background:#1C1C1C;display:block;position:absolute;width:58px;";
        g.style.top = this.m.imageHeight - 7 + "px";
        a.style.cssText = "position:absolute;top:0px;left:0px;width:60px;";
        e.appendChild(f);
        g.appendChild(i);
        x.V(e, "mouseover", function () {
          var a = parseInt(b.style.top, 10);
          7 !== a && (f.style.backgroundPosition = "-27px 0");
          new Bb({
            Nc: 60, fc: Cb.bt, duration: 300, Ba: function (c) {
              b.style.top = a + (7 - a) * c + "px"
            }
          })
        });
        x.V(e, "mouseout", function () {
          f.style.backgroundPosition = "-18px 0"
        });
        x.V(g, "mouseover", function () {
          var a = parseInt(b.style.top, 10), e = c.m.imageHeight - 14;
          if (!(parseInt(b.offsetHeight, 10) < e)) {
            var f = e - parseInt(b.offsetHeight, 10) + 7;
            f !== a && (i.style.backgroundPosition = "-9px 0");
            new Bb({
              Nc: 60, fc: Cb.bt, duration: 300, Ba: function (c) {
                b.style.top = a + (f - a) * c + "px"
              }
            })
          }
        });
        x.V(g, "mouseout", function () {
          i.style.backgroundPosition = "0 0"
        });
        a.appendChild(e);
        a.appendChild(g);
        e = F("div");
        e.style.cssText = ["position:absolute;z-index:2001;left:20px;", "height:" + this.m.imageHeight + "px;", "width:62px;overflow:hidden;background:rgb(37,37,37);background:rgba(37,37,37,0.9);"].join("");
        e.appendChild(b);
        e.appendChild(a);
        this.Zi = e;
        this.XQ = b;
        this.R.appendChild(e)
      }
    }, AW: function () {
      if (this.cj && !(0 >= this.cj.length)) {
        var a = F("div");
        a.innerHTML = this.lA;
        a.style.cssText = "position:absolute;background:#252525";
        this.R.appendChild(a);
        this.ft = a;
        this.Yc.og.style.left = this.mg.width + 8 + "px";
        this.Zi && (this.Zi.style.left = parseInt(this.Zi.style.left, 10) + this.mg.width + 8 + "px");
        var b = this;
        x.V(a, "click", function () {
          b.W.Gc(b.xX)
        })
      }
    }, jj: function (a) {
      this.rk = a;
      this.m.showCatalog && (0 < this.cj.length ? (Ya() ? this.zW() : this.DW(), this.Yc.offsetLeft = 60) : (this.ft && (this.R.removeChild(this.ft), this.ft = s, this.Yc.og.style.left = "0px"), this.Zi && (this.R.removeChild(this.Zi), this.Zi = s), this.Yc.offsetLeft = 0));
      var b = this.kY(a);
      Ya() && (this.cj && 0 < this.cj.length && this.m.showExit && this.lA) && (this.Yc.offsetLeft += this.mg.width + 8, this.ft ? this.ft.innerHTML = this.lA : this.AW());
      this.ci.innerHTML = b;
      this.ci.style.width = (this.mg.width + 8) * a.length + 8 + "px";
      a = this.R.offsetWidth;
      b = this.ci.offsetWidth;
      this.Yc.mt && (b += this.Yc.mt());
      b < a - 2 * this.Yc.Si - this.Yc.offsetLeft ? this.R.style.width = b + this.Yc.offsetLeft + "px" : (this.R.style.width = isNaN(Number(this.m.maxWidth)) === q ? this.m.maxWidth : this.m.maxWidth + "px", b < this.R.offsetWidth - 2 * this.Yc.Si - this.Yc.offsetLeft && (this.R.style.width = b + this.Yc.offsetLeft + "px"));
      this.Yc.refresh();
      this.GB = this.ci.children;
      this.ci.appendChild(this.uk);
      this.uk.style.left = "-100000px";
      a = this.ZH(this.W.ac(), this.h3);
      -1 !== a && this.tq(a)
    }, qY: function (a) {
      for (var b = "", c, e = 0, f = a.length; e < f; e++) c = '<div style="color:white;opacity:0.5;margin:0 35px;float:left;text-align: center"><span  dataIndex="' + a[e].index + '">' + a[e].data + "</span></div>", b += c;
      return b
    }, IL: function (a) {
      for (var b = "", c, e = 0, f = a.length; e < f; e++) c = '<li class="pano_catlogLi"><span style="display:block;width:100%;" dataIndex="' + a[e].index + '">' + a[e].data + "</span></li>", b += c;
      return b
    }, kY: function (a) {
      for (var b, c, e, f, g = [], i = this.mg.height, k = this.mg.width, m = 0; m < a.length; m++) b = a[m], recoType = b.recoType, e = b.panoId, f = b.name, c = b.heading, b = b.pitch, c = Qf.VL(e, c, b, 198, 108), b = '<a href="javascript:void(0);" class="pano_photo_item" data-index="' + m + '"><img style="width:' + (k - 2) + "px;height:" + (i - 2) + 'px;" data-index="' + m + '" name="' + f + '" src="' + c + '" alt="' + f + '"/><span class="pano_photo_decs" data-index="' + m + '" style="width:' + k + "px;font-size:" + Math.floor(i / 6) + "px; line-height:" + Math.floor(i / 6) + 'px;"><em class="pano_poi_' + recoType + '"></em>' + f + "</span></a>", 3 === recoType ? Ya() ? (this.lA = b, this.xX = e, a.splice(m, 1), m--) : (b = '<a href="javascript:void(0);" class="pano_photo_item" data-index="' + m + '"><img style="width:' + (k - 2) + "px;height:" + (i - 2) + 'px;" data-index="' + m + '" name="' + f + '" src="' + c + '" alt="' + f + '"/><div style="background:rgba(37,37,37,0.5);position:absolute;top:0px;left:0px;width:100%;height:100%;text-align: center;line-height:' + this.m.imageHeight + 'px;" data-index="' + m + '"><img src="' + J.ta + 'panorama/photoexit.png" style="border:none;vertical-align:middle;" data-index="' + m + '" alt=""/></div></a>', g.push(b)) : g.push(b);
      return g.join("")
    }, CY: function (a) {
      var b = this, c = this.W.ac();
      c && this.hm.Qx(c, function (e) {
        b.W.ac() === c && a(e)
      })
    }, wc: function (a) {
      if (!bb(a) || isNaN(a) || a < Tc || 3 < a) a = this.defaultAnchor;
      var b = this.R, c = this.m.offset.width, e = this.m.offset.height;
      b.style.left = b.style.top = b.style.right = b.style.bottom = "auto";
      switch (a) {
        case Tc:
          b.style.top = e + "px";
          b.style.left = c + "px";
          break;
        case Uc:
          b.style.top = e + "px";
          b.style.right = c + "px";
          break;
        case Vc:
          b.style.bottom = e + "px";
          b.style.left = c + "px";
          break;
        case 3:
          b.style.bottom = e + "px", b.style.right = c + "px"
      }
    }, HQ: function () {
      this.FQ()
    }, FQ: function () {
      var a = this;
      x.V(this.R, "touchstart", function (a) {
        a.stopPropagation()
      });
      x.V(this.vk, "click", function (b) {
        if ((b = (b.srcElement || b.target).getAttribute("data-index")) && b != a.ed) a.tq(b), a.W.Gc(a.rk[b].panoId)
      });
      x.V(this.ci, "mouseover", function (b) {
        b = (b.srcElement || b.target).getAttribute("data-index");
        b !== s && a.KK(b, q)
      });
      this.W.addEventListener("size_changed", function () {
        isNaN(Number(a.m.maxWidth)) && a.Mj({maxWidth: a.m.maxWidth})
      })
    }, tq: function (a) {
      this.uk.style.left = this.GB[a].offsetLeft + 8 + "px";
      this.uk.setAttribute("data-index", this.GB[a].getAttribute("data-index"));
      this.ed = a;
      this.KK(a)
    }, KK: function (a, b) {
      var c = this.mg.width + 8, e = 0;
      this.Yc.mt && (e = this.Yc.mt() / 2);
      var f = this.vk.offsetWidth - 2 * e, g = this.ci.offsetLeft || this.Yc.x, g = g - e, i = -a * c;
      i > g && this.Yc.scrollTo(i + e);
      c = i - c;
      g -= f;
      c < g && (!b || b && 8 < i - g) && this.Yc.scrollTo(c + f + e)
    }, mZ: function () {
      this.Yc = K() ? new wg(this.vk, {dp: t, vy: q, vi: t, Pi: t, Hn: t, UE: q, bx: q, Ux: q}) : new Bg(this.vk)
    }, aa: function () {
      this.R.style.visibility = "hidden"
    }, show: function () {
      this.R.style.visibility = "visible"
    }
  });

  function Bg(a) {
    this.R = a;
    this.mh = a.children[0];
    this.hs = s;
    this.Si = 20;
    this.offsetLeft = 0;
    this.za()
  }

  Bg.prototype = {
    za: function () {
      this.mh.style.position = "relative";
      this.refresh();
      this.Js();
      this.sC()
    }, refresh: function () {
      this.Ao = this.R.offsetWidth - this.mt();
      this.fB = -(this.mh.offsetWidth - this.Ao - this.Si);
      this.Wv = this.Si + this.offsetLeft;
      this.mh.style.left = this.Wv + "px";
      this.mh.children[0] && (this.hs = this.mh.children[0].offsetWidth);
      this.og && (this.og.children[0].style.marginTop = this.bs.children[0].style.marginTop = this.og.offsetHeight / 2 - this.og.children[0].offsetHeight / 2 + "px")
    }, mt: function () {
      return 2 * this.Si
    }, Js: function () {
      this.mw = F("div");
      this.mw.innerHTML = '<a class="pano_photo_arrow_l" style="background:rgb(37,37,37);background:rgba(37,37,37,0.9);" href="javascript:void(0)" title="\u4e0a\u4e00\u9875"><span class="pano_arrow_l"></span></a><a class="pano_photo_arrow_r" style="background:rgb(37,37,37);background:rgba(37,37,37,0.9);" href="javascript:void(0)" title="\u4e0b\u4e00\u9875"><span class="pano_arrow_r"></span></a>';
      this.og = this.mw.children[0];
      this.bs = this.mw.children[1];
      this.R.appendChild(this.mw);
      this.og.children[0].style.marginTop = this.bs.children[0].style.marginTop = this.og.offsetHeight / 2 - this.og.children[0].offsetHeight / 2 + "px"
    }, sC: function () {
      var a = this;
      x.V(this.og, "click", function () {
        a.scrollTo(a.mh.offsetLeft + a.Ao)
      });
      x.V(this.bs, "click", function () {
        a.scrollTo(a.mh.offsetLeft - a.Ao)
      })
    }, dV: function () {
      x.U.rc(this.og, "pano_arrow_disable");
      x.U.rc(this.bs, "pano_arrow_disable");
      var a = this.mh.offsetLeft;
      a >= this.Wv && x.U.ib(this.og, "pano_arrow_disable");
      a - this.Ao <= this.fB && x.U.ib(this.bs, "pano_arrow_disable")
    }, scrollTo: function (a) {
      a = a < this.mh.offsetLeft ? Math.ceil((a - this.Si - this.Ao) / this.hs) * this.hs + this.Ao + this.Si - 8 : Math.ceil((a - this.Si) / this.hs) * this.hs + this.Si;
      a < this.fB ? a = this.fB : a > this.Wv && (a = this.Wv);
      var b = this.mh.offsetLeft, c = this;
      new Bb({
        Nc: 60, fc: Cb.bt, duration: 300, Ba: function (e) {
          c.mh.style.left = b + (a - b) * e + "px"
        }, finish: function () {
          c.dV()
        }
      })
    }
  };

  function Cg() {
    var a = 256 * Math.pow(2, 15), b = 3 * a, c = R.Sa(new P(180, 0)).lng, b = b - c, e = -3 * a,
      a = R.Sa(new P(-180, 0)).lng, e = a - e;
    this.eV = c / Math.pow(2, 15);
    this.qg = c;
    this.hh = a;
    this.Ql = b + e;
    this.ih = c - a;
    this.RU = b;
    this.vT = e
  }

  Cg.prototype = {
    xm: function (a, b, c) {
      for (var e = 0, c = 1536 * Math.pow(2, b - 3) / (c || 256), f = c / 2 - 1, g = -c / 2; a > f;) a -= c, e -= this.Ql;
      for (; a < g;) a += c, e += this.Ql;
      var i = e, e = Math.round(e / Math.pow(2, 18 - b));
      return {offsetX: e, u4: i, Ag: a, t2: c, e6: f, f6: g}
    }, uC: function (a) {
      for (var b = a.lng; b > this.qg;) b -= this.ih;
      for (; b < this.hh;) b += this.ih;
      a.lng = b;
      return a
    }, WV: function (a, b) {
      for (var c = b || a.Hb(), e = a.nf.lng, f = a.kf.lng; c.lng > this.qg;) c.lng -= this.ih, e -= this.ih, f -= this.ih;
      for (; c.lng < this.hh;) c.lng += this.ih, e += this.ih, f += this.ih;
      a.nf.lng = e;
      a.kf.lng = f;
      return a
    }, vC: function (a, b, c, e) {
      for (var c = c || 256, f = e || Math.pow(2, 18 - b) * c, e = Math.floor(this.qg / f), g = Math.floor(this.hh / f), i = Math.floor(this.Ql / f), f = [], k = 0; k < a.length; k++) {
        var m = a[k], n = m[0], m = m[1];
        if (n >= e) {
          if (n += i, this.iy(n, b, c) !== q) {
            var o = "id_" + n + "_" + m + "_" + b;
            a[o] || (a[o] = q, f.push([n, m, b, c]))
          }
        } else n <= g && (n -= i, this.iy(n, b, c) !== q && (o = "id_" + n + "_" + m + "_" + b, a[o] || (a[o] = q, f.push([n, m, b, c]))))
      }
      k = 0;
      for (e = f.length; k < e; k++) a.push(f[k]);
      for (k = a.length - 1; 0 <= k; k--) n = a[k][0], this.iy(n, b, c) && a.splice(k, 1);
      return a
    }, iy: function (a, b, c) {
      for (var e = Math.pow(2, b - 3), b = Math.round(this.eV * e), e = 1536 * e / c; a > e / 2 - 1;) a -= e;
      for (; a < -(e / 2);) a += e;
      return 0 < a && a * c > b || 0 > a && Math.abs((a + 1) * c) > b ? q : t
    }, EM: function (a, b) {
      return a < this.hh || b > this.qg
    }, $L: function (a) {
      return Math.round((this.RU + this.vT) / Math.pow(2, 18 - a))
    }, MY: function (a, b, c) {
      var b = b || 256, e = Math.ceil(this.qg / c), f = Math.floor(this.hh / c);
      return a === e - 1 ? (a = (this.qg - c * (e - 1)) / c * b, a = Math.round(a), [0, 0, a, b]) : a === f ? (a = (this.hh - c * f) / c * b, a = Math.round(Math.abs(a)), [a, 0, b, b]) : s
    }
  };
  var ye = new Cg;
  A.Map = Pa;
  A.Hotspot = ob;
  A.MapType = De;
  A.Point = P;
  A.Pixel = Q;
  A.Size = M;
  A.Bounds = lb;
  A.TileLayer = Kd;
  A.Projection = hd;
  A.MercatorProjection = R;
  A.PerspectiveProjection = nb;
  A.Copyright = function (a, b, c) {
    this.id = a;
    this.jb = b;
    this.content = c
  };
  A.Overlay = kd;
  A.Label = sd;
  A.GroundOverlay = td;
  A.PointCollection = xd;
  A.Marker = V;
  A.CanvasLayer = Ad;
  A.Icon = od;
  A.IconSequence = qd;
  A.Symbol = pd;
  A.Polyline = Ed;
  A.Polygon = Dd;
  A.InfoWindow = rd;
  A.Circle = Fd;
  A.Control = Sc;
  A.NavigationControl = rb;
  A.GeolocationControl = Wc;
  A.OverviewMapControl = tb;
  A.CopyrightControl = Xc;
  A.ScaleControl = sb;
  A.MapTypeControl = ub;
  A.CityListControl = Yc;
  A.PanoramaControl = $c;
  A.TrafficLayer = Rd;
  A.CustomLayer = vb;
  A.ContextMenu = dd;
  A.MenuItem = gd;
  A.LocalSearch = jb;
  A.TransitRoute = hf;
  A.DrivingRoute = lf;
  A.TruckRoute = nf;
  A.WalkingRoute = mf;
  A.RidingRoute = of;
  A.Autocomplete = yf;
  A.RouteSearch = sf;
  A.Geocoder = tf;
  A.LocalCity = vf;
  A.Geolocation = Geolocation;
  A.Convertor = jd;
  A.BusLineSearch = xf;
  A.Boundary = wf;
  A.Panorama = Sa;
  A.PanoramaLabel = Ff;
  A.PanoramaService = cd;
  A.PanoramaCoverageLayer = ad;
  A.PanoramaFlashInterface = Of;

  function S(a, b) {
    for (var c in b) a[c] = b[c]
  }

  S(window, {
    BMap: A, _jsload2: function (a, b) {
      ka.ez.zZ && ka.ez.set(a, b);
      Wa.$V(a, b)
    }, BMAP_API_VERSION: "2.0"
  });
  var Dg = Pa.prototype;
  S(Dg, {
    getBounds: Dg.ke,
    getCenter: Dg.Hb,
    getMapType: Dg.ya,
    getSize: Dg.wb,
    setSize: Dg.He,
    getViewport: Dg.Ct,
    getZoom: Dg.la,
    centerAndZoom: Dg.xd,
    panTo: Dg.Hi,
    panBy: Dg.Og,
    setCenter: Dg.Af,
    setCurrentCity: Dg.FF,
    setMapType: Dg.Sg,
    setViewport: Dg.Tg,
    setZoom: Dg.Xc,
    highResolutionEnabled: Dg.$x,
    zoomTo: Dg.Vg,
    zoomIn: Dg.uG,
    zoomOut: Dg.vG,
    addHotspot: Dg.gC,
    removeHotspot: Dg.L_,
    clearHotspots: Dg.gx,
    checkResize: Dg.cW,
    addControl: Dg.Xo,
    removeControl: Dg.dO,
    getContainer: Dg.Ta,
    addContextMenu: Dg.om,
    removeContextMenu: Dg.jq,
    addOverlay: Dg.Ra,
    removeOverlay: Dg.Lb,
    clearOverlays: Dg.HK,
    openInfoWindow: Dg.Vc,
    closeInfoWindow: Dg.Mc,
    pointToOverlayPixel: Dg.cf,
    overlayPixelToPoint: Dg.ON,
    getInfoWindow: Dg.wh,
    getOverlays: Dg.Nx,
    getPanes: function () {
      return {
        floatPane: this.ce.HD,
        markerMouseTarget: this.ce.XE,
        floatShadow: this.ce.yL,
        labelPane: this.ce.PE,
        markerPane: this.ce.lN,
        markerShadow: this.ce.mN,
        mapPane: this.ce.Xt,
        vertexPane: this.ce.wP
      }
    },
    addTileLayer: Dg.Te,
    removeTileLayer: Dg.fg,
    pixelToPoint: Dg.cc,
    pointToPixel: Dg.vc,
    setFeatureStyle: Dg.b7,
    selectBaseElement: Dg.U6,
    setMapStyle: Dg.su,
    enable3DBuilding: Dg.sp,
    disable3DBuilding: Dg.SW,
    getPanorama: Dg.xt,
    initIndoorLayer: Dg.nZ,
    setNormalMapDisplay: Dg.Uy,
    setMapStyleV2: Dg.q0,
    setBMapCopyrightOffset: Dg.EF,
    getVectorContainer: Dg.SY
  });
  S(window, {BMAP_COORD_BD09: 5, BMAP_COORD_GCJ02: 3});
  var Eg = De.prototype;
  S(Eg, {
    getTileLayer: Eg.LY,
    getMinZoom: Eg.sf,
    getMaxZoom: Eg.Ye,
    getProjection: Eg.Aj,
    getTextColor: Eg.Tm,
    getTips: Eg.At
  });
  S(window, {BMAP_NORMAL_MAP: Qa, BMAP_PERSPECTIVE_MAP: Ta, BMAP_SATELLITE_MAP: db, BMAP_HYBRID_MAP: Va});
  var Fg = R.prototype;
  S(Fg, {lngLatToPoint: Fg.Lg, pointToLngLat: Fg.Kj});
  var Gg = nb.prototype;
  S(Gg, {lngLatToPoint: Gg.Lg, pointToLngLat: Gg.Kj});
  var Hg = lb.prototype;
  S(Hg, {
    equals: Hg.Vb,
    containsPoint: Hg.Hs,
    containsBounds: Hg.oW,
    intersects: Hg.Jt,
    extend: Hg.extend,
    getCenter: Hg.Hb,
    isEmpty: Hg.Gj,
    getSouthWest: Hg.Be,
    getNorthEast: Hg.tf,
    toSpan: Hg.gG
  });
  var Ig = kd.prototype;
  S(Ig, {isVisible: Ig.Oc, show: Ig.show, hide: Ig.aa});
  kd.getZIndex = kd.Sk;
  var Jg = mb.prototype;
  S(Jg, {
    openInfoWindow: Jg.Vc,
    closeInfoWindow: Jg.Mc,
    enableMassClear: Jg.wj,
    disableMassClear: Jg.UW,
    show: Jg.show,
    hide: Jg.aa,
    getMap: Jg.Kx,
    addContextMenu: Jg.om,
    removeContextMenu: Jg.jq
  });
  var Kg = V.prototype;
  S(Kg, {
    setIcon: Kg.Xb,
    getIcon: Kg.Ep,
    setPosition: Kg.va,
    getPosition: Kg.ma,
    setOffset: Kg.Rd,
    getOffset: Kg.yj,
    getLabel: Kg.ot,
    setLabel: Kg.Lj,
    setTitle: Kg.Hc,
    setTop: Kg.Li,
    enableDragging: Kg.jc,
    disableDragging: Kg.Ss,
    setZIndex: Kg.xq,
    getMap: Kg.Kx,
    setAnimation: Kg.xn,
    setShadow: Kg.Yy,
    hide: Kg.aa,
    setRotation: Kg.Wy,
    getRotation: Kg.ZL
  });
  S(window, {BMAP_ANIMATION_DROP: 1, BMAP_ANIMATION_BOUNCE: 2});
  var Lg = sd.prototype;
  S(Lg, {
    setStyle: Lg.Td,
    setStyles: Lg.Ki,
    setContent: Lg.Qc,
    setPosition: Lg.va,
    getPosition: Lg.ma,
    setOffset: Lg.Rd,
    getOffset: Lg.yj,
    setTitle: Lg.Hc,
    setZIndex: Lg.xq,
    getMap: Lg.Kx,
    getContent: Lg.Lk
  });
  var Mg = od.prototype;
  S(Mg, {
    setImageUrl: Mg.uO,
    setSize: Mg.He,
    setAnchor: Mg.wc,
    setImageOffset: Mg.ru,
    setImageSize: Mg.k0,
    setInfoWindowAnchor: Mg.n0,
    setPrintImageUrl: Mg.z0
  });
  var Ng = rd.prototype;
  S(Ng, {
    redraw: Ng.re,
    setTitle: Ng.Hc,
    setContent: Ng.Qc,
    getContent: Ng.Lk,
    getPosition: Ng.ma,
    enableMaximize: Ng.uh,
    disableMaximize: Ng.tx,
    isOpen: Ng.eb,
    setMaxContent: Ng.tu,
    maximize: Ng.ty,
    enableAutoPan: Ng.ct
  });
  var Og = md.prototype;
  S(Og, {
    getPath: Og.Ze,
    setPath: Og.Sd,
    setPositionAt: Og.Bn,
    getStrokeColor: Og.IY,
    setStrokeWeight: Og.wq,
    getStrokeWeight: Og.cM,
    setStrokeOpacity: Og.uq,
    getStrokeOpacity: Og.JY,
    setFillOpacity: Og.qu,
    getFillOpacity: Og.dY,
    setStrokeStyle: Og.vq,
    getStrokeStyle: Og.bM,
    getFillColor: Og.cY,
    getBounds: Og.ke,
    enableEditing: Og.ze,
    disableEditing: Og.TW,
    getEditing: Og.$X,
    getGeodesicPath: Og.gY
  });
  var Pg = Fd.prototype;
  S(Pg, {setCenter: Pg.Af, getCenter: Pg.Hb, getRadius: Pg.XL, setRadius: Pg.Bf});
  var Qg = Dd.prototype;
  S(Qg, {getPath: Qg.Ze, setPath: Qg.Sd, setPositionAt: Qg.Bn});
  var Rg = ob.prototype;
  S(Rg, {getPosition: Rg.ma, setPosition: Rg.va, getText: Rg.kE, setText: Rg.wu});
  P.prototype.equals = P.prototype.Vb;
  Q.prototype.equals = Q.prototype.Vb;
  M.prototype.equals = M.prototype.Vb;
  S(window, {
    BMAP_ANCHOR_TOP_LEFT: Tc,
    BMAP_ANCHOR_TOP_RIGHT: Uc,
    BMAP_ANCHOR_BOTTOM_LEFT: Vc,
    BMAP_ANCHOR_BOTTOM_RIGHT: 3
  });
  var Sg = Sc.prototype;
  S(Sg, {
    setAnchor: Sg.wc,
    getAnchor: Sg.PD,
    setOffset: Sg.Rd,
    getOffset: Sg.yj,
    show: Sg.show,
    hide: Sg.aa,
    isVisible: Sg.Oc,
    toString: Sg.toString
  });
  var Tg = rb.prototype;
  S(Tg, {getType: Tg.Mp, setType: Tg.Cn});
  S(window, {
    BMAP_NAVIGATION_CONTROL_LARGE: 0,
    BMAP_NAVIGATION_CONTROL_SMALL: 1,
    BMAP_NAVIGATION_CONTROL_PAN: 2,
    BMAP_NAVIGATION_CONTROL_ZOOM: 3
  });
  var Ug = tb.prototype;
  S(Ug, {changeView: Ug.ve, setSize: Ug.He, getSize: Ug.wb});
  var Vg = sb.prototype;
  S(Vg, {getUnit: Vg.RY, setUnit: Vg.PF});
  S(window, {BMAP_UNIT_METRIC: "metric", BMAP_UNIT_IMPERIAL: "us"});
  var Wg = Xc.prototype;
  S(Wg, {addCopyright: Wg.Tw, removeCopyright: Wg.rF, getCopyright: Wg.Km, getCopyrightCollection: Wg.WD});
  S(window, {BMAP_MAPTYPE_CONTROL_HORIZONTAL: Zc, BMAP_MAPTYPE_CONTROL_DROPDOWN: 1, BMAP_MAPTYPE_CONTROL_MAP: 2});
  var Xg = Kd.prototype;
  S(Xg, {getMapType: Xg.ya, getCopyright: Xg.Km, isTransparentPng: Xg.Rt});
  var Yg = dd.prototype;
  S(Yg, {addItem: Yg.zs, addSeparator: Yg.jC, removeSeparator: Yg.uF});
  var Zg = gd.prototype;
  S(Zg, {setText: Zg.wu});
  var $g = X.prototype;
  S($g, {
    getStatus: $g.Rm,
    setSearchCompleteCallback: $g.vu,
    getPageCapacity: $g.uf,
    setPageCapacity: $g.zn,
    setLocation: $g.yn,
    disableFirstResultSelection: $g.$C,
    enableFirstResultSelection: $g.tD,
    gotoPage: $g.Um,
    searchNearby: $g.qq,
    searchInBounds: $g.wn,
    search: $g.search
  });
  S(window, {
    BMAP_STATUS_SUCCESS: 0,
    BMAP_STATUS_CITY_LIST: 1,
    BMAP_STATUS_UNKNOWN_LOCATION: Qe,
    BMAP_STATUS_UNKNOWN_ROUTE: 3,
    BMAP_STATUS_INVALID_KEY: 4,
    BMAP_STATUS_INVALID_REQUEST: 5,
    BMAP_STATUS_PERMISSION_DENIED: Re,
    BMAP_STATUS_SERVICE_UNAVAILABLE: 7,
    BMAP_STATUS_TIMEOUT: Se
  });
  S(window, {
    BMAP_POI_TYPE_NORMAL: 0,
    BMAP_POI_TYPE_BUSSTOP: 1,
    BMAP_POI_TYPE_BUSLINE: 2,
    BMAP_POI_TYPE_SUBSTOP: 3,
    BMAP_POI_TYPE_SUBLINE: 4
  });
  S(window, {
    BMAP_TRANSIT_POLICY_RECOMMEND: 0,
    BMAP_TRANSIT_POLICY_LEAST_TIME: 4,
    BMAP_TRANSIT_POLICY_LEAST_TRANSFER: 1,
    BMAP_TRANSIT_POLICY_LEAST_WALKING: 2,
    BMAP_TRANSIT_POLICY_AVOID_SUBWAYS: 3,
    BMAP_TRANSIT_POLICY_FIRST_SUBWAYS: 5,
    BMAP_LINE_TYPE_BUS: 0,
    BMAP_LINE_TYPE_SUBWAY: 1,
    BMAP_LINE_TYPE_FERRY: 2,
    BMAP_LINE_TYPE_TRAIN: 3,
    BMAP_LINE_TYPE_AIRPLANE: 4,
    BMAP_LINE_TYPE_COACH: 5
  });
  S(window, {
    BMAP_TRANSIT_TYPE_POLICY_TRAIN: 0,
    BMAP_TRANSIT_TYPE_POLICY_AIRPLANE: 1,
    BMAP_TRANSIT_TYPE_POLICY_COACH: 2
  });
  S(window, {
    BMAP_INTERCITY_POLICY_LEAST_TIME: 0,
    BMAP_INTERCITY_POLICY_EARLY_START: 1,
    BMAP_INTERCITY_POLICY_CHEAP_PRICE: 2
  });
  S(window, {BMAP_TRANSIT_TYPE_IN_CITY: 0, BMAP_TRANSIT_TYPE_CROSS_CITY: 1});
  S(window, {BMAP_TRANSIT_PLAN_TYPE_ROUTE: 0, BMAP_TRANSIT_PLAN_TYPE_LINE: 1});
  var ah = gf.prototype;
  S(ah, {clearResults: ah.we});
  jf = hf.prototype;
  S(jf, {
    setPolicy: jf.An,
    toString: jf.toString,
    setPageCapacity: jf.zn,
    setIntercityPolicy: jf.IF,
    setTransitTypePolicy: jf.NF
  });
  S(nf.prototype, {
    setPolicy: nf.An,
    toString: nf.toString,
    setPageCapacity: nf.zn,
    setIntercityPolicy: nf.IF,
    setTransitTypePolicy: nf.NF
  });
  S(window, {
    BMAP_DRIVING_POLICY_DEFAULT: 0,
    BMAP_DRIVING_POLICY_AVOID_HIGHWAYS: 3,
    BMAP_DRIVING_POLICY_AVOID_CONGESTION: 5,
    BMAP_DRIVING_POLICY_FIRST_HIGHWAYS: 4
  });
  S(window, {
    BMAP_MODE_DRIVING: "driving",
    BMAP_MODE_TRANSIT: "transit",
    BMAP_MODE_WALKING: "walking",
    BMAP_MODE_NAVIGATION: "navigation"
  });
  var bh = sf.prototype;
  S(bh, {routeCall: bh.oO});
  S(window, {BMAP_HIGHLIGHT_STEP: 1, BMAP_HIGHLIGHT_ROUTE: 2});
  S(window, {BMAP_ROUTE_TYPE_DRIVING: Ue, BMAP_ROUTE_TYPE_WALKING: Te, BMAP_ROUTE_TYPE_RIDING: Ve});
  S(window, {BMAP_ROUTE_STATUS_NORMAL: We, BMAP_ROUTE_STATUS_EMPTY: 1, BMAP_ROUTE_STATUS_ADDRESS: 2});
  var ch = lf.prototype;
  S(ch, {setPolicy: ch.An});
  var dh = yf.prototype;
  S(dh, {show: dh.show, hide: dh.aa, setTypes: dh.OF, setLocation: dh.yn, search: dh.search, setInputValue: dh.Ty});
  S(vb.prototype, {});
  var eh = wf.prototype;
  S(eh, {get: eh.get});
  S(ad.prototype, {});
  S(window, {BMAP_POINT_DENSITY_HIGH: 200, BMAP_POINT_DENSITY_MEDIUM: Ud, BMAP_POINT_DENSITY_LOW: 50});
  S(window, {
    BMAP_POINT_SHAPE_STAR: 1,
    BMAP_POINT_SHAPE_WATERDROP: 2,
    BMAP_POINT_SHAPE_CIRCLE: ud,
    BMAP_POINT_SHAPE_SQUARE: 4,
    BMAP_POINT_SHAPE_RHOMBUS: 5
  });
  S(window, {
    BMAP_POINT_SIZE_TINY: 1,
    BMAP_POINT_SIZE_SMALLER: 2,
    BMAP_POINT_SIZE_SMALL: 3,
    BMAP_POINT_SIZE_NORMAL: vd,
    BMAP_POINT_SIZE_BIG: 5,
    BMAP_POINT_SIZE_BIGGER: 6,
    BMAP_POINT_SIZE_HUGE: 7
  });
  S(window, {
    BMap_Symbol_SHAPE_CAMERA: 11,
    BMap_Symbol_SHAPE_WARNING: 12,
    BMap_Symbol_SHAPE_SMILE: 13,
    BMap_Symbol_SHAPE_CLOCK: 14,
    BMap_Symbol_SHAPE_POINT: 9,
    BMap_Symbol_SHAPE_PLANE: 10,
    BMap_Symbol_SHAPE_CIRCLE: 1,
    BMap_Symbol_SHAPE_RECTANGLE: 2,
    BMap_Symbol_SHAPE_RHOMBUS: 3,
    BMap_Symbol_SHAPE_STAR: 4,
    BMap_Symbol_SHAPE_BACKWARD_CLOSED_ARROW: 5,
    BMap_Symbol_SHAPE_FORWARD_CLOSED_ARROW: 6,
    BMap_Symbol_SHAPE_BACKWARD_OPEN_ARROW: 7,
    BMap_Symbol_SHAPE_FORWARD_OPEN_ARROW: 8
  });
  S(window, {BMAP_CONTEXT_MENU_ICON_ZOOMIN: ed, BMAP_CONTEXT_MENU_ICON_ZOOMOUT: fd});
  S(window, {
    BMAP_SYS_DRAWER: Oa,
    BMAP_SVG_DRAWER: 1,
    BMAP_VML_DRAWER: 2,
    BMAP_CANVAS_DRAWER: 3,
    BMAP_SVG_DRAWER_FIRST: 4
  });
  A.wV();
  A.mz();
})()
