import request from '@/utils/request'

export function getDictionary(data) {
  return request({
    url: '/common/getDictionaryInfo',
    method: 'post',
    data
  })
}

export function getAllDataType(data) {
  return request({
    url: '/dict/getAllDataType',
    method: 'post',
    data
  })
}

export function getDictInfo(data) {
  return request({
    url: '/dict/getDictInfo',
    method: 'post',
    data
  })
}

export function addDict(data) {
  return request({
    url: '/dict/addDict',
    method: 'post',
    data
  })
}

export function updateDict(data) {
  return request({
    url: '/dict/updateDict',
    method: 'post',
    data
  })
}

export function deleteDict(data) {
  return request({
    url: '/dict/deleteDict',
    method: 'post',
    data
  })
}