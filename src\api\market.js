import service from "@/utils/request";

export default {
    //获取策略清单列表
    getStrategyList: (params) => service.post(`marketTask/getStrategyList`, params),
    //计算各地域客群统计信息
    calculateCustomerGroup: (params) => service.post(`/marketTask/calculateCustomerGroup`, params),
    //创建任务
    createTask: (params) => service.post(`/marketTask/createTask`, params),
    //查询监控任务列表
    getTaskInfo: (params) => service.post(`/marketTask/getTaskInfo`, params),
    //获取指标洞察数据
    getIndicatorInsightData: (params) => service.post(`/marketTask/getIndicatorInsightData`, params),
    //获取指标洞察数据
    getIndicatorInsightByPid: (params) => service.post(`/marketTask/getIndicatorInsightByPid`, params),
    //获取指标洞察最大数据日期
    getLatestDataDate: (params) => service.get(`/marketTask/getLatestDataDate`, params),
    //查看指标洞察列表
    getIndicatorInsightList: (params) => service.post(`/marketTask/getIndicatorInsightList`, params),
    //查看指标洞察详情
    getIndicatorInsightDetail: (params) => service.post(`/marketTask/getIndicatorInsightDetail`, params),
}