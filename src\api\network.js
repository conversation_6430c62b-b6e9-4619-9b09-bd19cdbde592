import request from '@/utils/request'

// 网络覆盖-有线-获取选择月份默认值
export function getDefaultStatMon(data) {
  return request({
    url: '/digitalBattle/index/wired/defaultStatMon',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-宽带占比
export function getKdRate(data) {
  return request({
    url: '/digitalBattle/index/wired/kdRate',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-趋势
export function getTrend(data) {
  return request({
    url: '/digitalBattle/index/wired/trend',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-份额情况占比
export function getShareTier(data) {
  return request({
    url: '/digitalBattle/index/wired/shareTier',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-top5高市占
export function getHighTop5(data) {
  return request({
    url: '/digitalBattle/index/wired/highTop5',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-top5低市占
export function getLowTop5(data) {
  return request({
    url: '/digitalBattle/index/wired/lowTop5',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-低市占分页查询
export function getLowTop5Page(data) {
  return request({
    url: '/digitalBattle/index/wired/lowTop5Page',
    method: 'post',
    data
  })
}

// 网络覆盖-有线-网格分布
export function getLowGrid(data) {
  return request({
    url: '/digitalBattle/index/wired/lowGrid',
    method: 'post',
    data
  })
}


// 网络覆盖-无线-获取选择月份默认值
export function getDefaultStatMonW(data) {
  return request({
    url: '/digitalBattle/index/wireless/defaultStatMon',
    method: 'post',
    data
  })
}

// 网络覆盖-无线-覆盖强弱占比
export function getCoverRate(data) {
  return request({
    url: '/digitalBattle/index/wireless/coverRate',
    method: 'post',
    data
  })
}

// 网络覆盖-无线-占比层次
export function getShareTierW(data) {
  return request({
    url: '/digitalBattle/index/wireless/shareTier',
    method: 'post',
    data
  })
}

// 网络覆盖-无线-top5高覆盖
export function getHighTop5W(data) {
  return request({
    url: '/digitalBattle/index/wireless/highTop5',
    method: 'post',
    data
  })
}

// 网络覆盖-无线-top5低覆盖
export function getLowTop5W(data) {
  return request({
    url: '/digitalBattle/index/wireless/lowTop5',
    method: 'post',
    data
  })
}

// 网络覆盖-无线-网格分布
export function getLowGridW(data) {
  return request({
    url: '/digitalBattle/index/wireless/lowGrid',
    method: 'post',
    data
  })
}