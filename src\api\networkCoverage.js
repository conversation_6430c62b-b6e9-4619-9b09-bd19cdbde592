import request from '@/utils/request'
export function getMapAreaInfo(data) {
  return request({
    url: 'digitalBattle/mapCoverage/getMapAreaInfo',
    method: 'post',
    data
  })
}
export function getKdSumData(data) {
  return request({
    url: 'digitalBattle/mapCoverage/getKdSumData',
    method: 'post',
    data
  })
}
export function getFqxData(data) {
  return request({
    url: 'digitalBattle/mapCoverage/getFqxData',
    method: 'post',
    data
  })
}
export function getKdDetailData(data) {
  return request({
    url: 'digitalBattle/mapCoverage/getKdDetailData',
    method: 'post',
    data
  })
}
export function getWxwlSumData(data) {
  return request({
    url: 'digitalBattle/mapCoverage/getWxwlSumData',
    method: 'post',
    data
  })
}
export function getWxwlDetailData(data) {
  return request({
    url: 'digitalBattle/mapCoverage/getWxwlDetailData',
    method: 'post',
    data
  })
}
