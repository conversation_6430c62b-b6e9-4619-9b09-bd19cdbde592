import request from '@/utils/request'
export function getMapInfo(data) {
  return request({
    url: 'busiBattleMap/getMapAreaInfo',
    method: 'post',
    data
  })
}
export function getStreetNumInArea(data) {
  return request({
    url: 'busiBattleMap/getStreetNumInArea',
    method: 'post',
    data
  })
}
export function getStreetInfoByCounty(data) {
  return request({
    url: 'busiBattleMap/getStreetInfoByCounty',
    method: 'post',
    data
  })
}

export function getSearchAllAreaList(data) {
  return request({
    url: 'busiBattleMap/allAreaList',
    method: 'post',
    data
  })
}

// 作战清单查询
export function getFightList(data) {
  return request({
    url: 'combatMissionTask/getCombatList',
    method: 'post',
    data
  })
}

// 街区目标字典查询
export function getStreetTargetOpts(data) {
  return request({
    url: 'common/queryConfig',
    method: 'post',
    data
  })
}

// 指派列表查询
export function getDispatchList(data) {
  return request({
    url: 'combatMissionTask/getAssignList',
    method: 'post',
    data
  })
}

// 派发角色查询
export function getDispatchRoles(data) {
  return request({
    url: 'combatMissionTask/getRoleList',
    method: 'post',
    data
  })
}

// 派发用户查询
export function getDispatchPersons(data) {
  return request({
    url: 'combatMissionTask/getUsersByRole',
    method: 'post',
    data
  })
}

// 创建派发任务
export function makeDispatchTask(data) {
  return request({
    url: 'combatMissionTask/publishTask',
    method: 'post',
    data
  })
}
// 下载明细模板
export function downloadTemplate(data) {
  return request({
    url: 'combatMissionTask/exportStreetTargetData',
    method: 'post',
    data,
    responseType: 'blob',
    returnAllResponse: true
    // headers: {
    //   'Content-Type': 'application/json'
    // }
  })
}
// 上传模板文件
export function uploadTemplate(data) {
  return request({
    url: 'combatMissionTask/importStreetTargetData',
    method: 'post',
    data,
    responseType: 'blob',
    returnAllResponse: true
    // headers: {
    //   'Content-Type': 'application/json'
    // }
  })
}

