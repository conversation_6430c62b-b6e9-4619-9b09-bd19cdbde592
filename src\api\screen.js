import request from '@/utils/request'

export function getOverViewAll(data) {
  return request({
    url: '/overview/getMemberCountInfo',
    method: 'post',
    data
  })
}

export function getTrainingNameInfo(data) {
  return request({
    url: '/overview/getTrainingNameInfo',
    method: 'post',
    data
  })
}

export function getAthleteTrainingInfo(data) {
  return request({
    url: '/overview/getAthleteTrainingInfo',
    method: 'post',
    data
  })
}

export function getScrollAthleteInfo(data) {
  return request({
    url: '/overview/getScrollAthleteInfo',
    method: 'post',
    data
  })
}

export function getScrollCoachInfo(data) {
  return request({
    url: '/overview/getScrollCoachInfo',
    method: 'post',
    data
  })
}

export function getProgramTypeInfo(data) {
  return request({
    url: '/overview/getProgramTypeInfo',
    method: 'post',
    data
  })
}

export function getShooterScoreSubsection(data) {
  return request({
    url: '/overview/getShooterScoreSubsection',
    method: 'post',
    data
  })
}

export function getPlayerCount(data) {
  return request({
    url: '/overview/getNotTrainingAthleteTotal',
    method: 'post',
    data
  })
}

export function getCoachCount(data) {
  return request({
    url: '/overview/getCoachTotal',
    method: 'post',
    data
  })
}

export function getShooterCountInfo(data) {
  return request({
    url: '/analyse/getShooterCountInfo',
    method: 'post',
    data
  })
}

export function getTrainingInfo(data) {
  return request({
    url: '/analyse/getTrainingInfo',
    method: 'post',
    data
  })
}

export function getJDData(data) {
  return request({
    url: '/analyse/getJDData',
    method: 'post',
    data
  })
}

export function getShootShake(data) {
  return request({
    url: '/analyse/getShootShake',
    method: 'post',
    data
  })
}

export function getMonitorList(data) {
  return request({
    url: '/overview/getMonitorList',
    method: 'post',
    data
  })
}

export function getLastShootShake(data) {
  return request({
    url: '/analyse/getLastShootShake',
    method: 'post',
    data
  })
}

export function getLastTenShoot(data) {
  return request({
    url: '/analyse/getLastTenShoot',
    method: 'post',
    data
  })
}

export function getLastShootShakeById(data) {
  return request({
    url: '/analyse/getLastShootShakeById',
    method: 'post',
    data
  })
}

export function getShakeStaticList(data) {
  return request({
    url: '/analyse/getShakeStaticList',
    method: 'post',
    data
  })
}