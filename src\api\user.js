import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/app/login/login',
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/userinfo/getUserRole',
    method: 'post',
    data: { token }
  })
}

export function logout() {
  return request({
    url: '/app/login/logout',
    method: 'post'
  })
}

export function getVerifyCode() {
  return request({
    url: '/app/login/getVerifyCode',
    method: 'post'
  })
}

export function getList(data) {
  return request({
    url: '/user/getUser',
    method: 'post',
    data
  })
}

export function getDepartment(data) {
  return request({
    url: '/user/getDepartment',
    method: 'post',
    data
  })
}

export function getUserRole(data) {
  return request({
    url: '/user/getUserRole',
    method: 'post',
    data
  })
}

export function addUser(data) {
  return request({
    url: '/user/addUser',
    method: 'post',
    data
  })
}

export function updateUser(data) {
  return request({
    url: '/user/updateUser',
    method: 'post',
    data
  })
}

export function delUser(data) {
  return request({
    url: '/user/delUser',
    method: 'post',
    data
  })
}

export function resertPassword(data) {
  return request({
    url: '/user/resertPassword',
    method: 'post',
    data
  })
}

export function getUser(data) {
  return request({
    url: '/user/getUserById',
    method: 'post',
    data
  })
}

export function updatePwd(data) {
  return request({
    url: '/user/updatePwd',
    method: 'post',
    data
  })
}

export function getRoleInfo(data) {
  return request({
    url: '/role/getRoleInfo',
    method: 'post',
    data
  })
}

export function addRoleInfo(data) {
  return request({
    url: '/role/addRoleInfo',
    method: 'post',
    data
  })
}

export function updateRoleInfo(data) {
  return request({
    url: '/role/updateRoleInfo',
    method: 'post',
    data
  })
}

export function getRoleList(data) {
  return request({
    url: '/role/getRoleList',
    method: 'post',
    data
  })
}

export function getAreaInfo(data = {}) {
  return request({
    url: '/common/getAreaInfo',
    method: 'get',
    data
  })
}
