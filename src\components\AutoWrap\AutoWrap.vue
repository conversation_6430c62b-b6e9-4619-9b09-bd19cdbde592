<template>
  <div ref="wrap" class="notice-bar__wrap" :style="{height: height + 'px'}">
    <div ref="content" class="notice-bar__content" :style="contentStyle">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        wrapWidth: 0,
        contentWidth: 0,
        deptTime: 0,
        deptTimer: null,
        convertSpeed: 1,
        contentStyle: {
          transitionDuration: '0s',
          transform: 'translateY(0px)'
        },
      }
    },
    props: {
      height: {
        type: Number,
        default: 100
      },
      // speed: {
      //   type: Number,
      //   default: 50
      // },
      // defaultWidth: {
      //   type: Number,
      //   default: 1080
      // },
      startWrap: {
        type: Boolean,
        default: false
      },
      fromBottom: {
        type: Boolean,
        default: true
      },
      interval: {
        // 执行间隔，毫秒
        type: Number,
        default: 30000
      }
    },
    watch: {
      startWrap: {
        handler(newVal, oldVal) {
          if (newVal === true) {
            this.clearTimer()
            this.$nextTick(() => {
              this.init()
            })
          } else {
            this.clearTimer()
            this.contentStyle.transitionDuration = '0s'
            this.contentStyle.transform = 'translateY(' + this.wrapWidth/2 + 'px)'
          }
        },
        immediate: true
      }
    },
    mounted() {
      this.wrapWidth = this.$refs.wrap.offsetHeight
      this.contentWidth = this.$refs.content.offsetHeight
    },
    beforeDestroy() {
      this.clearTimer()
    },
    destroyed() {
      this.clearTimer()
    },
    methods: {
      init () {
        // const _width = window.innerHeight
        // 根据分辨率转化成不同的速度
        // this.convertSpeed = _width / this.defaultWidth * this.speed
        this.wrapWidth = this.$refs.wrap.offsetHeight
        this.contentWidth = this.$refs.content.offsetHeight
        this.startAnimate()
        // this.deptTimer = setInterval(() => {
        //   this.startAnimate()
        // }, this.deptTime * 1000)
        this.deptTimer = setInterval(() => {
          this.startAnimate()
        }, this.interval)
      },
      startAnimate () {
        this.contentStyle.transitionDuration = '0s'
        if (this.fromBottom) {
          this.contentStyle.transform = 'translateY(' + this.wrapWidth + 'px)'
        } else {
          this.contentStyle.transform = 'translateY(' + this.wrapWidth/2 + 'px)'
        }
        // this.deptTime = (this.wrapWidth + this.contentWidth) / this.convertSpeed

        setTimeout(() => {
          // this.contentStyle.transitionDuration = this.deptTime + 's'
          this.contentStyle.transitionDuration = this.interval / 1000 + 's'
          this.contentStyle.transform = 'translateY(-' + this.contentWidth + 'px)'
        }, 100)
      },
      clearTimer() {
        console.log("clear timer......")
        this.deptTimer && clearInterval(this.deptTimer)
        this.deptTimer = null
      }
    }
  }
</script>

<style lang="scss" scoped>
  .notice-bar__wrap {
    position: relative;
    overflow: hidden;
    
    .notice-bar__content {
      position: absolute;
      width: 100%;
      // white-space: nowrap;
      transition-timing-function: linear;
    }
  }
</style>