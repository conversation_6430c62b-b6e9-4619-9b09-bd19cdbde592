<template>
  <div :id="playerId" style="width: 100%;"></div>
</template>

<script>
  import EZUIKit from "ezuikit-js"
  import { UUID } from "@/utils/common.js"
  
  export default {
    props: {
      // src: {
      //   type: String,
      //   default: ""
      // },
      // token: {
      //   type: String,
      //   default: ""
      // },
      data: {
        type: Object,
        default: () => { return { src: '', token: ''} }
      },
      width: {
        type: String|Number,
        default: 200
      },
      height: {
        type: String|Number,
        default: 200
      }
    },
    data() {
      return {
        playerId: 'video-' + UUID(),
        player: null,
        enableZ:false, //默认关闭电子放大
        play: true //默认停止播放
      }
    },
    watch: {
      data: {
        handler(newValue, oldValue) {
          this.$nextTick(() => {
            if (newValue.src !== null && newValue.src !== undefined && newValue.src !== '') {
              // console.log(newValue)
              if (this.player === null) {
                this.player = new EZUIKit.EZUIKitPlayer({
                  autoplay: this.play,
                  id: this.playerId,
                  accessToken: newValue.token,
                  url: newValue.src,
                  template: "simple", // simple - 极简版;standard-标准版;security - 安防版(预览回放);voice-语音版；
                  audio: 0, // 是否默认开启声音 0 - 关闭 1 - 开启
                  width: this.width,
                  height: this.height
                });
              } else {
                this.player.stop()
                this.player.accessToken = newValue.token
                this.player.url = newValue.src
                this.player.play()
                // this.play(newValue)
              }
              console.log(this.player)
            }
          })
        },
        deep: true,
        immediate: true
      }
    },
    beforeDestroy(){
      if (this.play) {
        this.player.stop()
        this.player = null
      }
    },
    methods: {
    }
  }
</script>

<style lang="scss">
</style>