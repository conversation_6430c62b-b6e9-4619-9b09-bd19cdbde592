<template>
  <div class="card-view" :style="cardStyle">
    <div class="title">
      <div class="title-text">
        <span>{{ title }}</span>
        <span class="desc">{{ titleDesc }}</span>
      </div>
      <div class="background"></div>
    </div>
    <div class="body">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    height: {
      type: Number | String,
      default: 60
    },
    width: {
      type: Number | String,
      default: 334
    },
    title: {
      type: String,
      default: ""
    },
    titleDesc: {
      type: Number | String,
      default: ""
    },
    bg: {
      type: String,
      default: "rgba(18, 34, 47, 0.6)"
    }
  },
  computed: {
    cardStyle() {
      let height = ""
      let width = ""
      let style = ""
      // 处理高度
      if (this.height > 0 && this.height !== '') {
        height = this.$isNumber(this.height) ? ("height: " + this.height + "px") : ("height: " + this.height)
      }
      // 处理宽度
      if (this.width > 0 && this.width !== '') {
        width = this.$isNumber(this.width) ? ("width: " + this.width + "px") : ("width: " + this.width)
      }
      let backgroundColor =  "backgroundColor: "+this.bg;
      if (width !== "" && height !== "") {
        style = width + "; " + height + ";" + backgroundColor
      } else if (width !== "" && height === "") {
        style = width + ";" + backgroundColor
      } else if (width === "" && height !== "") {
        style = height + ";" + backgroundColor
      }
      return style
    }
  },
}
</script>

<style lang="scss" scoped>
.card-view {
  min-width: 334px;
  // min-height: 60px;
  // background: #12222F;
  // opacity: 0.6;
  background-color: rgba(18, 34, 47, 0.6);
  display: flex;
  flex-direction: column;

  .title {
    width: 100%;
    position: relative;
    height: 47px;
    line-height: 47px;

    &-text {
      font-size: 22px;
      font-family: PangMenZhengDao-3;
      font-weight: 400;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 10px;
    }

    .background {
      position: absolute;
      bottom: 0;
      left: 10px;
      right: 3px;
      height: 26px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-image: url(../../assets/images/titlebg.png);
    }

    .desc {
      font-size: 28px;
      font-family: PangMenZhengDao-3;
      font-weight: 400;
      color: #54E5AE;
      margin-right: 42px;
    }
  }

  .body {
    position: relative;
    flex: 1;
  }
}
</style>