<template>
  <div id="pieChart" ref="pieChart" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null
    }
  },
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    data: {
      type: Array,
      default: () => { return [] }
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    radius: {
      type: String,
      default: "80%"
    },
    color: {
      type: Array,
      default: () => { return [] }
    },
    centerImage: {
      type: String,
      default: ''
    },
    centerImageSize: {
      type: Number,
      default: 0.3
    },
    
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.clearChart()
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeChart);
  },
  beforeD<PERSON>roy() {
    this.clearChart()
  },
  methods: {
    clearChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    initChart() {
      this.chart = echarts.init(this.$refs.pieChart)
      let option = {
        graphic: {
          //设置中心图片及文字
          elements: [
            {
              type: "image", //通过不同top值可以设置上下显示
              left: "center",
              // left: 75,
              // left: 'calc(100% - 80px)',
              top: "center",
              style: {
                image: require("@/assets/images/network/centerImg.png"), // 添加你想要展示的图片的URL
                width: 60,
                height: 60,
                top: "center" // 图片距离容器顶部的距离
                // left: 75 // 图片距离容器左侧的距离
              }
              // position: [75, 30],
            }
          ]
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {d}%',
        },
        legend: {
          show: false,
          orient: 'vertical',
          right: 100,
          top: 'center',
          itemWidth: 20,
          itemHeight: 20,
          icon: 'rect',
          backgroundColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14,
          },
          data: ['中国移动', '中国电信', '中国联通', '京宽网络', '未知/其他'],
        },
        series: [
          {
            name: '运营商分布',
            type: 'pie',
            radius: ['71%', '95%'],
            center: ['50%', '50%'], // 饼图偏左
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false, // 去除指示线
            },
            // data: [
            //   { "name": "中国移动", value: 50 },
            //   { "name": "中国电信", value: 20 },
            //   { "name": "中国联通", value: 15 },
            //   { "name": "京宽网络", value: 10 },
            //   { "name": "未知/其他", value: 5 },
            // ],
            data:this.data,
            itemStyle: {
              normal: {
                color: function (params) {
                  var colorList = [
                    {
                      c1: 'rgba(237,190,22,0.8)',
                      c2: 'rgba(237,190,22,0.8)'
                    },
                    {
                      c1: '#40b6ed',
                      c2: '#40b6ed'
                    },
                    {
                      c1: 'rgba(133,217,228,0.8)',
                      c2: 'rgba(133,217,228,0.8)'
                    },
                    {
                      c1: 'rgba(66,139,245,0.8)', // '#9E9E9E',
                      c2: 'rgba(66,139,245,0.8)'// '#9E9E9E00'
                    },
                    {
                      c1: 'rgba(158,158,158,0.8)',
                      c2: 'rgba(158,158,158,0.8)'
                    },
                    // {
                    //   c1: '#6A1B9A',
                    //   c2: '#BA68C8'
                    // },
                  ];
                  return new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    {
                      //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上

                      offset: 0,
                      color: colorList[params.dataIndex].c1
                    },
                    {
                      offset: 1,
                      color: colorList[params.dataIndex].c2
                    }
                  ]);


                }
              }
            }

          },
        ],
      }
      this.chart.setOption(option)
    },
    resizeChart() {
      if (this.$refs.pieChart) {
        this.$refs.pieChart.resize();
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
</style>