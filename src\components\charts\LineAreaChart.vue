<template>
  <div ref="lineAreaChart" :class="className" :style="{height:height, width:width}" />
</template>

<script>
  import * as echarts from 'echarts'
  
  export default {
    data() {
      return {
        chart: null
      }
    },
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '200px'
      },
      height: {
        type: String,
        default: '200px'
      },
      data: {
        type: Array,
        default: () => { return [] }
      },
      tooltipBorderColor: {
        type: String,
        default: '#2CE4FF'
      },
      tooltipBackgroundColor: {
        type: String,
        default: 'rgba(25,63,81,0.69)'
      },
      tooltipTextColor: {
        type: String,
        default: '#ffffff'
      },
      tooltipFontSize: {
        type: Number,
        default: 12
      },
      xAxisData: {
        type: Array,
        default: () => { return [] }
      },
      xAxisName: {
        type: String,
        default: ''
      },
      yAxisName: {
        type: String,
        default: ''
      },
      lineColor: {
        type: String,
        default: '#2CE4FF'
      },
      areaColor: {
        type: String|Object,
        default: () => {
          return {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                // 0% 处的颜色
                offset: 0,
                color: '#4AE8FF'
              }, {
                // 100% 处的颜色
                offset: 1,
                color: '#0E3553'
              }
            ],
            // 缺省为 false
            global: false
          }
        }
      },
      areaOpacity: {
        type: Number,
        default: 0.4
      }
    },
    watch: {
      data: {
        handler(newVal) {
          this.$nextTick(() => {
            this.clearChart()
            setTimeout(() => {
              this.initChart()
            }, 100)
          })
        },
        deep: true
      }
    },
    mounted() {
      // this.initChart()
    },
    beforeDestroy() {
      this.clearChart()
    },
    methods: {
      clearChart() {
        if (!this.chart) {
          return
        }
        this.chart.dispose()
        this.chart = null
      },
      initChart() {
        this.chart = echarts.init(this.$refs.lineAreaChart)
        let option = {
          tooltip: {
            trigger: 'axis',
            formatter: '{c}&nbsp;&nbsp;&nbsp;&nbsp;{b}',
            borderColor: this.tooltipBorderColor,
            backgroundColor: this.tooltipBackgroundColor,
            textStyle: {
              color: this.tooltipTextColor,
              fontSize: this.tooltipFontSize
            },
            axisPointer: {
              type: 'none'
            }
          },
          grid: {
            top: 36,
            bottom: 22
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.xAxisData,
            name: this.xAxisName,
            nameTextStyle: {
              fontSize: 12,
              color: '#ffffff'
            },
            axisLabel: {
              color: '#ffffff'
            } 
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#ffffff'
            },
            name: this.yAxisName,
            nameTextStyle: {
              fontSize: 12,
              color: '#ffffff'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: 'rgba(255, 255, 255, 0.23)'
              }
            }
          },
          series: [
            {
              data: this.data,
              type: 'line',
              smooth: false,
              showSymbol: false,
              symbol: 'circle',
              lineStyle: {
                color: this.lineColor,
                width: 1
              },
              itemStyle: {
                color: this.lineColor
              },
              areaStyle: {
                color: this.areaColor,
                opacity: this.areaOpacity
              },
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' }
                ],
                symbol: 'rect',
                symbolSize: [155, 31],
                symbolOffset: [0, -19],
                label: {
                  color: '#fff',
                  fontFamily: 'Source Han Sans CN',
                  formatter: (params) => {
                    // console.log(params)
                    return params.value + "  " + this.xAxisData[params.data.coord[0]]
                  }
                },
                itemStyle: {
                  borderColor: this.tooltipBorderColor,
                  color: this.tooltipBackgroundColor,
                  borderWidth: 1
                }
              },
            }
          ]
        }
        this.chart.setOption(option)
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>