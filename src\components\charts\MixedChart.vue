<template>
  <div ref="mixedChart" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'MixedChart',
  data() {
    return {
      chart: null
    }
  },
  props: {
    className: {
      type: String,
      default: 'mixed-chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // X轴数据
    xAxisData: {
      type: Array,
      // default: () => ['类别1', '类别2', '类别3', '类别4', '类别5', '类别6']
      default: () => []
    },
    // 柱状图数据
    barData: {
      type: Array,
      default: () => [
        // {
        //   name: '数据1',
        //   data: [3, 5.5, 6, 5, 6, 7],
        //   color: ['#85D9E4', '#6AC0D0', '#4FA7BC']
        // },
        // {
        //   name: '数据2',
        //   data: [2, 4, 4.5, 3.5, 4, 5],
        //   color: ['#418AF4', '#3678D8', '#2B66BC']
        // }
      ]
    },
    // 折线图数据
    lineData: {
      type: Array,
      default: () => [
        // {
        //   name: '折线1',
        //   data: [40, 20, 45, 60, 65, 70],
        //   color: '#EDBE16'
        // },
        // {
        //   name: '折线2',
        //   data: [45, 15, 40, 55, 58, 75],
        //   color: '#E3925F'
        // }
      ]
    },
    // Y轴配置
    yAxisConfig: {
      type: Object,
      default: () => ({
        left: {
          name: '单位：万',
          min: 0,
          max: 'dataMax'
        },
        right: {
          name: '',
          min: 0,
          max: 100,
          formatter: '{value}%'
        }
      })
    },
    // 图例配置
    showLegend: {
      type: Boolean,
      default: true
    },
    // 柱状图宽度
    barWidth: {
      type: String,
      default: '20%'
    },
    dataZoom: {
      // 滚动条
      type: Array,
      default: () => []
    },
  },
  watch: {
    xAxisData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    barData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    lineData: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    this.clearChart()
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    clearChart() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    initChart() {
      if (!this.$refs.mixedChart) {
        console.warn('Chart container not found')
        return
      }

      this.chart = echarts.init(this.$refs.mixedChart)
      this.setChartOption()
    },
    updateChart() {
      if (this.chart) {
        this.setChartOption()
      }
    },
    setChartOption() {
      if (!this.chart) return

      // 构建图例数据
      const legendData = []
      if (this.barData && Array.isArray(this.barData)) {
        legendData.push(...this.barData.map(item => item.name))
      }
      if (this.lineData && Array.isArray(this.lineData)) {
        legendData.push(...this.lineData.map(item => item.name))
      }

      // 构建系列数据
      const series = []

      // 添加柱状图系列
      if (this.barData && Array.isArray(this.barData)) {
        this.barData.forEach(item => {
          if (item.data && Array.isArray(item.data)) {
            series.push({
              name: item.name,
              type: 'bar',
              barWidth: this.barWidth,
              data: item.data,
              itemStyle: {
                color: this.getBarColor(item.color)
              }
            })
          }
        })
      }

      // 添加折线图系列
      if (this.lineData && Array.isArray(this.lineData)) {
        this.lineData.forEach(item => {
          if (item.data && Array.isArray(item.data)) {
            series.push({
              name: item.name,
              type: 'line',
              yAxisIndex: 1,
              data: item.data,
              smooth: true,
              symbol: 'circle',
              symbolSize: 8,
              lineStyle: {
                color: item.color || '#91cc75',
                width: 2
              },
              itemStyle: {
                color: item.color || '#91cc75'
              }
            })
          }
        })
      }

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'transparent',
          borderWidth: 0,
          textStyle: {
            color: '#fff' // 设置字体颜色为白色
          },
          extraCssText: `
            padding: 10px;
            background: linear-gradient(180deg, #254177 0%, #1F4A9C 100%),
radial-gradient(76.5% 72.7% at 3.63% 87.5%, rgba(128, 170, 255, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%);
            border-radius: 8px;
            color: #fff;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            
          `,
          formatter: function (params) {
            const addPercentTo = ['占比']
            // 加单位%
            let result = `${params[0].axisValue}<br/>`;
            result += params.map(item => {
              const needPercent = addPercentTo.some(keyword => item.seriesName.includes(keyword));
              return `${item.marker} ${item.seriesName}: ${needPercent ? item.value + '%' : item.value}`;
            }).join('<br/>');
            return result;
          }
        },
        legend: this.showLegend ? {
          data: legendData,
          top: 10,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 15
        } : false,
        grid: {
          left: '10%',
          right: '10%',
          bottom: '10%',
          top: this.showLegend ? '20%' : '10%',
          containLabel: true
        },
        dataZoom: this.dataZoom,
        xAxis: {
          type: 'category',
          data: this.xAxisData || [],
          axisLine: {
            lineStyle: {
              color: '#d0d7de'
            }
          },
          axisLabel: {
            color: '#fff'
          }
        },
        yAxis: [
          {
            type: 'value',
            name: this.yAxisConfig.left?.name || '',
            nameTextStyle: {
              color: '#fff'
            },
            axisLine: {
              lineStyle: {
                color: '#d0d7de'
              }
            },
            axisLabel: {
              color: '#fff'
            },
            splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#FFFFFF33'
            }
          },
            min: this.yAxisConfig.left?.min || 0,
            max: this.yAxisConfig.left?.max || 'dataMax'
          },
          {
            type: 'value',
            name: this.yAxisConfig.right?.name || '',
            position: 'right',
            axisLine: {
              lineStyle: {
                color: '#d0d7de'
              }
            },
            axisLabel: {
              color: '#fff',
              formatter: this.yAxisConfig.right?.formatter || '{value}'
            },
            splitLine: {
              show: false
            },
            min: this.yAxisConfig.right?.min || 0,
            ...(this.yAxisConfig.right.noMax ? {} : {
              max: this.yAxisConfig.right?.max || 100
            })
          }
        ],
        series: series
      }

      this.chart.setOption(option, true)
    },
    // 获取柱状图颜色（包含透明渐变效果）
    getBarColor(colorConfig) {
      if (!colorConfig) {
        return '#5470c6' // 默认颜色
      }

      if (typeof colorConfig === 'string') {
        // 单一颜色也创建从不透明到透明的渐变
        return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: colorConfig },
          { offset: 1, color: this.addAlphaToColor(colorConfig, 0.5) }
        ])
      }

      if (Array.isArray(colorConfig)) {
        if (colorConfig.length === 1) {
          // 单一颜色创建从不透明到透明的渐变
          const baseColor = colorConfig[0]
          return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: baseColor },
            { offset: 1, color: this.addAlphaToColor(baseColor, 0.5) }
          ])
        } else if (colorConfig.length >= 3) {
          // 创建从上到下变浅变透明的渐变色
          return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colorConfig[0] },
            { offset: 0.5, color: this.addAlphaToColor(colorConfig[1], 0.7) },
            { offset: 1, color: this.addAlphaToColor(colorConfig[2], 0.5) }
          ])
        } else if (colorConfig.length === 2) {
          return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colorConfig[0] },
            { offset: 1, color: this.addAlphaToColor(colorConfig[1], 0.5) }
          ])
        }
      }

      return '#5470c6'
    },
    // 添加透明度到颜色的辅助方法
    addAlphaToColor(color, alpha) {
      // 如果颜色已经是rgba格式，直接替换透明度
      if (color.startsWith('rgba')) {
        return color.replace(/rgba\(([^)]+)\)/, (match, values) => {
          const parts = values.split(',')
          return `rgba(${parts[0]},${parts[1]},${parts[2]}, ${alpha})`
        })
      }

      // 如果是rgb格式，转换为rgba
      if (color.startsWith('rgb')) {
        return color.replace('rgb', 'rgba').replace(')', `, ${alpha})`)
      }

      // 如果是十六进制颜色，转换为rgba
      if (color.startsWith('#')) {
        const hex = color.substring(1)
        const r = parseInt(hex.substring(0, 2), 16)
        const g = parseInt(hex.substring(2, 4), 16)
        const b = parseInt(hex.substring(4, 6), 16)
        return `rgba(${r}, ${g}, ${b}, ${alpha})`
      }

      // 其他情况返回原色
      return color
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mixed-chart {
  width: 100%;
  height: 100%;
}
</style>