<template>
  <div ref="pieChart" :class="className" :style="{height:height, width:width}" />
</template>

<script>
  import * as echarts from 'echarts'
  
  export default {
    data() {
      return {
        chart: null
      }
    },
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '200px'
      },
      height: {
        type: String,
        default: '200px'
      },
      data: {
        type: Array,
        default: () => { return [] }
      },
      showLabel: {
        type: Boolean,
        default: true
      },
      radius: {
        type: String,
        default: "80%"
      },
      color: {
        type: Array,
        default: () => { return [] }
      }
    },
    watch: {
      data: {
        handler(newVal, oldVal) {
          this.clearChart()
          this.initChart()
        },
        deep: true
      }
    },
    mounted() {
      // this.initChart()
    },
    beforeDestroy() {
      this.clearChart()
    },
    methods: {
      clearChart() {
        if (!this.chart) {
          return
        }
        this.chart.dispose()
        this.chart = null
      },
      initChart() {
        this.chart = echarts.init(this.$refs.pieChart)
        let option = {
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: this.radius,
              data: this.data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              label: {
                color: '#ffffff',
                fontSize: 14,
                show: this.showLabel,
                fontFamily: 'PangMenZhengDao-3'
              },
            }
          ]
        }
        if (this.color && this.color.length > 0) {
          option.color = this.color
        }
        this.chart.setOption(option)
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>