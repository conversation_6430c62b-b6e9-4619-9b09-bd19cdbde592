<template>
  <div ref="pieChart" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null
    }
  },
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    data: {
      type: Array,
      default: () => { return [] }
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    radius: {
      type: String,
      default: "80%"
    },
    color: {
      type: Array,
      default: () => { return [] }
    },
    centerImage: {
      type: String,
      default: ''
    },
    centerImageSize: {
      type: Number,
      default: 0.3
    },
    dataZoom:{
      // 滚动条
      type: Array,
      default: () => []
    },
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.clearChart()
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.clearChart()
  },
  methods: {
    clearChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    generateLineChartOption(apiData) {
      const legendMap = {
        ydWifiCnt: '中国移动',
        dxWifiCnt: '中国电信',
        ltWifiCnt: '中国联通',
        jkWifiCnt: '京宽网络',
        // gdWifiCnt: '中国广电',
        qtWifiCnt: '未知/其他'
      };
      const colorMap = {
        ydWifiCnt: 'rgba(237,190,22,0.8)',
        dxWifiCnt: 'rgba(133,217,228,0.8)',
        ltWifiCnt: 'rgba(66,139,245,0.8)',
        jkWifiCnt: '#40b6ed',
        // gdWifiCnt: '中国广电',
        qtWifiCnt: 'rgba(158,158,158,0.8)'
      }

      // 1. 提取所有月份（x轴）
      const xAxisData = apiData.map(item => item.statMon.replace('-', ''));

      // 2. 构造 series 数据
      const seriesData = {};
      Object.keys(legendMap).forEach(key => {
        seriesData[key] = apiData.map(item => {
          const val = item[key] || 0;
          // 转为“万”为单位
          // return (val / 10000).toFixed(2);
          // 暂时不转
          return val;
        });
      });

      // 3. 构造 series 数组
      const series = Object.keys(legendMap).map(key => ({
        name: legendMap[key],
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { color: colorMap[key] },
        itemStyle: { color: colorMap[key] },
        // 临时删除最后一条数据, 为了演示外部造了一条
        data: seriesData[key].slice(0, -1)
      }));

      console.log(series,'series=====')

      // 4. 组装完整 ECharts 配置
      return {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'transparent',
          borderWidth: 0,
          textStyle: {
            color: '#fff' // 设置字体颜色为白色
          },
          extraCssText: `
            padding: 10px;
            background: linear-gradient(180deg, #254177 0%, #1F4A9C 100%),
radial-gradient(76.5% 72.7% at 3.63% 87.5%, rgba(128, 170, 255, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%);
            border-radius: 8px;
            color: #fff;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            width:vw(255);
            height:vh(228);
          `,
          // formatter: function (params) {
          //   let html = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`;
          //   params.forEach(item => {
          //     html += `
          //       <div style="display: flex; align-items: center; margin: 4px 0;">
          //         <span style="
          //           display: inline-block;
          //           width: 8px;
          //           height: 8px;
          //           background-color: ${item.color};
          //           border-radius: 2px;
          //           margin-right: 6px;
          //         "></span>
          //         <span>${item.seriesName}: ${item.value}</span>
          //       </div>
          //     `;
          //   });
          //   return html;
          // }
        },
        legend: {
          data: Object.values(legendMap),
          top: 10,
          right: 20,
          textStyle: { color: '#fff' }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        dataZoom: this.dataZoom,
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: { color: '#fff' }
        },
        yAxis: {
          type: 'value',
          // max: 50,
          // interval: 10,
          axisLabel: {
            formatter: '{value}',
            color: '#fff',
            showMaxLabel: true,
            // margin:30,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#FFFFFF33'
            }
          },

        },
        series
      };
    },
    initChart() {
      this.chart = echarts.init(this.$refs.pieChart)
      let option = this.generateLineChartOption(this.data)
      // let option = {
      //   title: {
      //     // text: '基础平滑折线图',
      //     left: 'center',
      //     textStyle: {
      //       color: '#333'
      //     }
      //   },
      //   tooltip: {
      //     trigger: 'axis'
      //   },
      //   legend: {
      //     data: ['中国移动', '中国电信', '中国联通', '京兆网络', '未知/其他'],
      //     top: 10,
      //     right: 20,
      //     textStyle: {
      //       color: '#fff' // 你也可以改成 '#fff'，如果背景是深色
      //     }
      //   },
      //   grid: {
      //     left: '3%',
      //     right: '4%',
      //     bottom: '3%',
      //     containLabel: true
      //   },
      //   xAxis: {
      //     type: 'category',
      //     boundaryGap: false,
      //     data: ['202411', '202412', '202501', '202502', '202503', '202504', '202505', '202506', '202507', '202508'],
      //     axisLabel: {
      //       color: '#fff'
      //     },
      //   },
      //   yAxis: {
      //     type: 'value',
      //     axisLabel: {
      //       formatter: '{value} 万',
      //       color: '#fff'
      //     },

      //   },
      //   series: [
      //     {
      //       name: '中国移动',
      //       type: 'line',
      //       smooth: true,
      //       data: [10, 12, 15, 18, 20, 25, 28, 30, 35, 40],
      //       symbol: 'none',       // 显示圆点

      //     },
      //     {
      //       name: '中国电信',
      //       type: 'line',
      //       smooth: true,
      //       data: [5, 7, 10, 13, 15, 18, 20, 22, 24, 26]
      //     },
      //     {
      //       name: '中国联通',
      //       type: 'line',
      //       smooth: true,
      //       data: [3, 5, 6, 8, 10, 11, 13, 14, 15, 16]
      //     },
      //     {
      //       name: '京兆网络',
      //       type: 'line',
      //       smooth: true,
      //       data: [1, 2, 2, 3, 4, 4, 5, 6, 6, 7]
      //     },
      //     {
      //       name: '未知/其他',
      //       type: 'line',
      //       smooth: true,
      //       data: [2, 3, 3, 4, 5, 5, 6, 7, 8, 9]
      //     }
      //   ]
      // }

      // 如果提供了中心图片，则添加图片
      if (this.centerImage) {
        option.graphic = [{
          type: 'image',
          left: 'center',
          top: 'center',
          z: 100,
          bounding: 'raw',
          style: {
            image: this.centerImage,
            width: this.width.replace('px', '') * this.centerImageSize,
            height: this.height.replace('px', '') * this.centerImageSize
          }
        }]
      }
      if (this.color && this.color.length > 0) {
        option.color = this.color
      }
      this.chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped></style>