<template>
  <div ref="pieChart" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    data() {
      return {
        chart: null
      }
    },
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '200px'
      },
      height: {
        type: String,
        default: '200px'
      },
      data: {
        type: Array,
        default: () => {
          return []
        }
      },
      showLabel: {
        type: Boolean,
        default: true
      },
      radius: {
        type: String,
        default: "80%"
      },
      color: {
        type: Array,
        default: () => {
          return []
        }
      },
      centerImage: {
        type: String,
        default: ''
      },
      centerImageSize: {
        type: Number,
        default: 0.3
      },
      xData: {
        type: Array,
        default: () => {
          return []
        }
      },
      yAxisName: {
        type: String,
        default: ''
      },
      yInterval: {
        type: Number,
        default: 20
      },
      yMax: {
        type: Number,
        default: 100
      }
    },
    watch: {
      data: {
        handler(newVal, oldVal) {
          this.clearChart()
          this.initChart()
        },
        deep: true
      }
    },
    mounted() {
      this.initChart()
    },
    beforeDestroy() {
      this.clearChart()
    },
    methods: {
      clearChart() {
        if (!this.chart) {
          return
        }
        this.chart.dispose()
        this.chart = null
      },
      generateStackedBarOption(data) {
        const operatorMap = {
          ydWifiRate: {
            name: '移动',
            color: ['rgba(237,190,22,0.8)', 'rgba(237,190,22,0)']
          },
          dxWifiRate: {
            name: '电信',
            color: ['rgba(133,217,228,0.8)', 'rgba(133,217,228,0)']
          },
          ltWifiRate: {
            name: '联通',
            color: ['rgba(66,139,245,0.8)', 'rgba(66,139,245,0)']
          },
          // gdWifiRate: { name: '广电', color: ['rgba(83,191,239,0.8)', 'rgba(83,191,239,0)'] },
          qtWifiRate: {
            name: '其他',
            color: ['rgba(158,158,158,0.8)', 'rgba(158,158,158,0)']
          }
        };

        // 1. x轴
        const xData = data.map(item => item.villageName);

        // 2. series 构造
        const series = Object.keys(operatorMap).map(key => {
          return {
            name: operatorMap[key].name,
            type: 'bar',
            stack: 'total',
            barWidth: '20%',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0,
                    color: operatorMap[key].color[0]
                  },
                  {
                    offset: 1,
                    color: operatorMap[key].color[1]
                  }
                ]
              }
            },
            data: data.map(item => (item[key] * 100).toFixed(2)) // 转为百分比
          };
        });

        // 3. legend
        const legendData = Object.values(operatorMap).map(item => item.name);

        return {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'transparent',
            borderWidth: 0,
            textStyle: {
              color: '#fff'
            },
            extraCssText: `
    padding: 10px;
    background: linear-gradient(180deg, #254177 0%, #1F4A9C 100%),
    radial-gradient(76.5% 72.7% at 3.63% 87.5%, rgba(128, 170, 255, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%);
    border-radius: 8px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  `,
            formatter: function(params) {
              // 获取数据项索引
              const dataIndex = params[0].dataIndex;
              const dataItem = data[dataIndex];

              // // 构建地理位置信息
              // let locationInfo = params[0].axisValue; // 默认显示行政村名称
              // if (dataItem && dataItem.countyName && dataItem.areaName) {
              //   locationInfo = `${dataItem.countyName}-${dataItem.areaName}-${dataItem.villageName}`;
              // }
              let locationInfo = params[0].axisValue; // 默认显示x轴值
              if (dataItem && dataItem.countyName && dataItem.areaName && dataItem.villageName) {
                locationInfo = `${dataItem.countyName} ${dataItem.areaName}<br/>${dataItem.villageName}`;
              }

              let result = `${locationInfo}<br/>`;
              result += params.map(item => `${item.marker} ${item.seriesName}: ${item.value}%`).join('<br/>');
              return result;
            }
          },
          legend: {
            data: legendData,
            top: 10,
            right: 20,
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            icon: 'rect',
            itemWidth: 10, // 图例图标的宽度，默认 25
            itemHeight: 10, // 图例图标的高度，默认 14
          },
          grid: {
            left: 10,
            right: 10,
            bottom: 30,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: xData,
            // axisLabel: {
            //   color: '#fff',
            //   interval: 0
            // },
            axisLabel: {
              interval: 0, // 强制显示所有标签
              formatter: function(value) {
                // 每行最多显示6个字
                const maxLength = 6;
                let val = '';
                for (let i = 0, len = value.length; i < len; i += maxLength) {
                  val += value.slice(i, i + maxLength) + '\n';
                }
                return val.trim(); // 去掉最后的换行符
              }
            },
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            }
          },
          yAxis: {
            type: 'value',
            // name: '%',
            nameTextStyle: {
              align: 'right'
            },
            max: 100,
            interval: 50,
            axisLabel: {
              formatter: '{value} %',
              color: '#fff',
              showMaxLabel: true,
              showMinLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#FFFFFF33'
              }
            }
          },
          series
        };
      },
      initChart() {
        this.chart = echarts.init(this.$refs.pieChart)
        let option = this.generateStackedBarOption(this.data)
        // 如果提供了中心图片，则添加图片
        if (this.centerImage) {
          option.graphic = [{
            type: 'image',
            left: 'center',
            top: 'center',
            z: 100,
            bounding: 'raw',
            style: {
              image: this.centerImage,
              width: this.width.replace('px', '') * this.centerImageSize,
              height: this.height.replace('px', '') * this.centerImageSize
            }
          }]
        }
        if (this.color && this.color.length > 0) {
          option.color = this.color
        }
        this.chart.setOption(option)
      }
    }
  }
</script>

<style lang="scss" scoped></style>
