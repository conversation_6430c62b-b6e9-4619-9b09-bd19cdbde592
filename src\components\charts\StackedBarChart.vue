<template>
  <div ref="stackedChart" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'StackedBarChart',
    data() {
      return {
        chart: null
      }
    },
    props: {
      className: {
        type: String,
        default: 'stacked-chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '300px'
      },
      // X轴数据
      xAxisData: {
        type: Array,
        // default: () => ['分类1', '分类2', '分类3', '分类4', '分类5']
        default: () => []
      },
      // 系列数据
      seriesData: {
        type: Array,
        default: () => [
          // {
          //   name: '移动',
          //   data: [120, 100, 90, 110, 95],
          //   color: ['#ECBD15', '#E0A812', '#D4930F']
          // },
          // {
          //   name: '电信',
          //   data: [30, 35, 25, 30, 28],
          //   color: ['#7AC7D7', '#6AB5C5', '#5AA3B3']
          // },
          // {
          //   name: '联通',
          //   data: [25, 20, 18, 22, 20],
          //   color: ['#428AF4', '#3778E0', '#2C66CC']
          // },
          // // {
          // //   name: '广电',
          // //   data: [15, 12, 10, 13, 11],
          // //   color: ['#53BFEF', '#47ABDB', '#3B97C7']
          // // },
          // {
          //   name: '其他',
          //   data: [10, 8, 7, 9, 8],
          //   color: ['#9E9E9E', '#8A8A8A', '#767676']
          // }
        ]
      },
      // Y轴配置
      yAxisConfig: {
        type: Object,
        default: () => ({
          name: '数量',
          min: 0,
          max: 250
        })
      },
      // 图例配置
      showLegend: {
        type: Boolean,
        default: true
      },
      // 柱状图宽度
      barWidth: {
        type: String,
        default: '40%'
      },
      // 堆叠标识
      stackName: {
        type: String,
        default: 'total'
      },
      dataZoom: {
        // 滚动条
        type: Array,
        default: () => []
      },
      flag: {
        type: Boolean,
        default: true
      },
      // 原始数据，用于显示详细的地理信息
      rawData: {
        type: Array,
        default: () => []
      }
    },
    watch: {
      xAxisData: {
        handler() {
          this.updateChart()
        },
        deep: true
      },
      seriesData: {
        handler() {
          this.updateChart()
        },
        deep: true
      },
      yAxisConfig: {
        handler() {
          this.updateChart()
        },
        deep: true
      },

    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
      })
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      this.clearChart()
      window.removeEventListener('resize', this.handleResize)
    },
    methods: {
      clearChart() {
        if (this.chart) {
          this.chart.dispose()
          this.chart = null
        }
      },
      initChart() {
        if (!this.$refs.stackedChart) {
          console.warn('Chart container not found')
          return
        }

        this.chart = echarts.init(this.$refs.stackedChart)
        this.setChartOption()
      },
      updateChart() {
        if (this.chart) {
          this.setChartOption()
        }
      },
      setChartOption() {
        if (!this.chart) return

        // 构建图例数据
        const legendData = this.seriesData?.map(item => item.name) || []

        // 构建系列数据
        const series = []

        if (this.seriesData && Array.isArray(this.seriesData)) {
          this.seriesData.forEach((item, index) => {
            if (item.data && Array.isArray(item.data)) {
              series.push({
                name: item.name,
                type: 'bar',
                stack: this.stackName,
                barWidth: index === 0 ? this.barWidth : undefined, // 只在第一个系列设置barWidth
                data: item.data,
                itemStyle: {
                  color: this.getSeriesColor(item.color)
                },
                label: item.label
              })
            }
          })
        }

        const flag = this.flag;
        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'transparent',
            borderWidth: 0,
            textStyle: {
              color: '#fff' // 设置字体颜色为白色
            },
            extraCssText: `
            padding: 10px;
            background: linear-gradient(180deg, #254177 0%, #1F4A9C 100%),
radial-gradient(76.5% 72.7% at 3.63% 87.5%, rgba(128, 170, 255, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%);
            border-radius: 8px;
            color: #fff;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          `,
            formatter: (params) => {
              const addPercentTo = ['覆盖']

              // 获取数据项索引
              const dataIndex = params[0].dataIndex;
              const dataItem = this.rawData && this.rawData[dataIndex];

              // 构建地理位置信息
              // 构建区县、网格、行政村分行显示
              let locationInfo = params[0].axisValue; // 默认显示x轴值
              if (dataItem && dataItem.countyName && dataItem.areaName && dataItem.villageName) {
                locationInfo = `${dataItem.countyName} ${dataItem.areaName}<br/>${dataItem.villageName}`;
              }

              // 加单位%
              let result = `${locationInfo}<br/>`;
              result += params.map(item => {
                const needPercent = addPercentTo.some(keyword => item.seriesName.includes(keyword)) || flag;
                return `${item.marker} ${item.seriesName}: ${needPercent ?  item.value:item.value + '%'}`;
              }).join('<br/>');
              return result;
            }
          },
          legend: this.showLegend ? {
            data: legendData,
            top: 10,
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 15
          } : false,
          grid: {
            left: 10,
            right: 10,
            bottom: 30,
            top: this.showLegend ? '20%' : '10%',
            containLabel: true
          },
          dataZoom: this.dataZoom,
          xAxis: {
            type: 'category',
            data: this.xAxisData || [],
            axisLine: {
              lineStyle: {
                color: '#d0d7de'
              }
            },
            axisLabel: {
              color: '#fff',
              formatter: function(value) {
                // 每行最多显示6个字
                const maxLength = 6;
                let val = '';
                for (let i = 0, len = value.length; i < len; i += maxLength) {
                  val += value.slice(i, i + maxLength) + '\n';
                }
                return val.trim(); // 去掉最后的换行符
              }
            }
          },
          yAxis: {
            type: 'value',
            name: this.yAxisConfig?.name || '',
            nameTextStyle: {
              color: '#fff'
            },
            axisLine: {
              lineStyle: {
                color: '#d0d7de'
              }
            },
            axisLabel: {
              color: '#fff'
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#FFFFFF33'
              }
            },
            min: this.yAxisConfig?.min || 0,
            max: this.yAxisConfig?.max || 'dataMax'
          },
          series: series
        }

        this.chart.setOption(option, true)
      },
      getSeriesColor(colorConfig) {
        if (!colorConfig) {
          return '#5470c6' // 默认颜色
        }

        if (typeof colorConfig === 'string') {
          return colorConfig
        }

        if (Array.isArray(colorConfig)) {
          if (colorConfig.length === 1) {
            // 单一颜色也创建从不透明到透明的渐变
            const baseColor = colorConfig[0]
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: baseColor
              },
              {
                offset: 1,
                color: this.addAlphaToColor(baseColor, 0.5)
              }
            ])
          } else if (colorConfig.length >= 3) {
            // 创建从上到下变浅变透明的渐变色
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: colorConfig[0]
              },
              {
                offset: 0.5,
                color: this.addAlphaToColor(colorConfig[1], 0.7)
              },
              {
                offset: 1,
                color: this.addAlphaToColor(colorConfig[2], 0.5)
              }
            ])
          } else if (colorConfig.length === 2) {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: colorConfig[0]
              },
              {
                offset: 1,
                color: this.addAlphaToColor(colorConfig[1], 0.5)
              }
            ])
          }
        }

        return '#5470c6'
      },
      // 添加透明度到颜色的辅助方法
      addAlphaToColor(color, alpha) {
        // 如果颜色已经是rgba格式，直接替换透明度
        if (color.startsWith('rgba')) {
          return color.replace(/rgba\(([^)]+)\)/, (match, values) => {
            const parts = values.split(',')
            return `rgba(${parts[0]},${parts[1]},${parts[2]}, ${alpha})`
          })
        }

        // 如果是rgb格式，转换为rgba
        if (color.startsWith('rgb')) {
          return color.replace('rgb', 'rgba').replace(')', `, ${alpha})`)
        }

        // 如果是十六进制颜色，转换为rgba
        if (color.startsWith('#')) {
          const hex = color.substring(1)
          const r = parseInt(hex.substring(0, 2), 16)
          const g = parseInt(hex.substring(2, 4), 16)
          const b = parseInt(hex.substring(4, 6), 16)
          return `rgba(${r}, ${g}, ${b}, ${alpha})`
        }

        // 其他情况返回原色
        return color
      },
      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .stacked-chart {
    width: 100%;
    height: 100%;
  }
</style>
