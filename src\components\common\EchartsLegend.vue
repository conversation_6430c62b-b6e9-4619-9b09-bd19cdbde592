<template>
  <div class="echarts-legend" :style="{backgroundColor: arealevel>='5'?'#060f22b2':''}" :class="hasPanel ? 'panel-left' : ''">
    <div v-if="title" class="legend-title">{{ title }}</div>
    <div v-for="item,index in legendList" :key="index" class="legend-icon">
      <img v-if="item.icon" :src="item.icon">
      <div v-else :style="{backgroundColor:item.color,borderColor:item.borderColor}" class="legend-icon-box" />
      <div>{{ item.text }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'EchartsLegend',
  props: {
    title: {
      type: String,
      default: ''
    },
    legendList: {
      type: Array,
      default: () => []
    },
    hasPanel: {
      type: Boolean,
      default: false
    },
    arealevel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.echarts-legend {
  padding:5px 15px 15px;
  border-radius: 4px;
  position: absolute;
  left: 40px;
  bottom: 53px;
  &.panel-left{
    left: vw(480);
  }
  .legend-title {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(14);
    color: #ffffff;
    margin-bottom: vh(20);
  }
  .legend-icon {
    color: #ffffff;
    display: flex;
    align-items: center;
    margin-top: vh(10);
    > div {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      &:first-child {
        margin-right: vw(12);
        width: vw(13);
        height: vw(13);
        border: vw(1) solid;
      }
    }
    img{
      width: vw(25);
      margin-right: vw(12);
    }
  }
}
</style>
