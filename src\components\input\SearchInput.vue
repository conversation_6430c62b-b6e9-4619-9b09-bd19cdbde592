<template>
  <div class="search-input">
    <input
      :value="value"
      :placeholder="placeholder"
      v-bind="$attrs"
      autocomplete="new-password"
      v-on="inputListeners" />
    <svg-icon icon-class='search' class="search-icon" @click="search" />
  </div>
</template>

<script>
  export default {
    data() {
      return {
        text: this.value
      }
    },
    props: {
      placeholder: {
        type: String,
        default: "请输入"
      },
      value: {
        type: String,
        default: ""
      }
    },
    watch: {
      value(newVal, oldVal) {
        this.text = newVal
      },
      text(newVal, oldVal) {
        this.$emit("input", newVal)
      }
    },
    computed: {
      inputListeners () {
        let _t = this
        return Object.assign({}, this.$listeners, {
          input: function (event) {
            // _t.showClear = !!event.target.value;
            _t.$emit('input', event.target.value)
          }
        })
      }
    },
    methods: {
      search() {
        this.$emit("submit", this.text)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .search-input {
    width: 496px;
    height: 56px;
    background: rgba(33, 158, 151, 0.1);
    border-radius: 9px;
    display: flex;
    align-items: center;
    padding-right: 13px;
    
    input {
      font-size: 21px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      // color: rgba(106, 255, 255, 0.28);
      color: #ffffff;
      flex: 1;
      background: transparent;
      border: 0px;
      outline: none;
      display: block;
      padding: 0 8px 0 18px;
      height: 100%;
    }
    
    .search-icon {
      width: 28px;
      height: 30px;
      color: #54E5AE;
      cursor: pointer;
    }
    
    input::-webkit-input-placeholder{   /* 使用webkit内核的浏览器 */
    	color: rgba(106, 255, 255, 0.28);
    }
    input:-moz-placeholder{    /* Firefox版本4-18 */
    	color: rgba(106, 255, 255, 0.28);
    }              
    input::-moz-placeholder{    /* Firefox版本19+ */
    	color: rgba(106, 255, 255, 0.28);
    }              
    input:-ms-input-placeholder{   /* IE浏览器 */
    	color: rgba(106, 255, 255, 0.28);
    }
  }
</style>