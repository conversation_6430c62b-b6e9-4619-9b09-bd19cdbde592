<template>
  <div class="photo-box">
    <div class="photo-panel" :style="{width: size, height: size}">
      <img v-if="photo === ''" :src="require('@/assets/images/avatar.png')"/>
      <img v-else :src="photo"/>
    </div>
    <div v-if="showText" class="photo-text">{{ text }}</div>
  </div>
</template>

<script>
  export default {
    data() {
      return {}
    },
    props: {
      text: {
        type: String,
        default: ""
      },
      photo: {
        type: String,
        default: ""
      },
      showText: {
        type: Boolean,
        default: true
      },
      size: {
        type: String,
        default: 93
      }
    }
  }
</script>

<style lang="scss" scoped>
  .photo-box {
    .photo-panel {
      width: 93px;
      height: 93px;
      padding: 8px;
      background-image: url(../../assets/images/photo.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      
      img {
        width: 100%;
        height: 100%;
      }
    }
    
    .photo-text {
      width: 100%;
      height: 27px;
      line-height: 27px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #FFFFFF;
      text-align: center;
      background-image: url(../../assets/images/name.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
</style>