<template>
  <div class="progress-bar" :style="{height: height + 'px'}">
    <div class="progress-bar-panel" :class="{'black-panel': blackPanel === true}">
      <div class="progress-bar-item" :style="{width: (percentage > 100 ? 100 : percentage) + '%'}"></div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {}
    },
    props: {
      percentage: {
        type: Number,
        default: 0
      },
      height: {
        type: Number,
        default: 15
      },
      blackPanel: {
        type: Boolean,
        default: false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .progress-bar {
    width: 100%;
    
    .black-panel {
      background: rgba(5, 20, 14, 0.38);
    }
    
    &-panel {
      width: 100%;
      // background: #54E5AE;
      // opacity: 0.3;
      background: rgba(84, 229, 174, 0.3);
      height: 100%;
    }
    
    &-item {
      background: #DCF867;
      height: 100%;
    }
  }
</style>