const drag = {
  inserted(el, binding) {
    el.style.position = 'absolute'
    el.style.cursor = 'move'

    let isDragging = false
    let startX, startY
    const threshold = 5 // 移动超过5px才认为是拖动

    el.onmousedown = function(e) {
      // 只处理左键点击
      if (e.button !== 0) return

      isDragging = false
      startX = e.clientX
      startY = e.clientY

      const { ox, oy } = {
        ox: e.clientX - el.offsetLeft,
        oy: e.clientY - el.offsetTop
      }

      document.onmousemove = function(em) {
        const movedX = Math.abs(em.clientX - startX)
        const movedY = Math.abs(em.clientY - startY)

        if (movedX > threshold || movedY > threshold) {
          isDragging = true
        }

        if (isDragging) {
          const { left, top } = {
            left: em.clientX - ox,
            top: em.clientY - oy
          }

          el.style.cursor = 'grabbing'
          el.style.left = left + 'px'
          el.style.top = top + 'px'
        }
      }

      document.onmouseup = function(eu) {
        document.onmousemove = null
        document.onmouseup = null
        el.style.cursor = 'move'

        // 如果是拖动操作，阻止后续的点击事件
        if (isDragging) {
          // 阻止所有子元素的点击事件
          const clickHandler = function(e) {
            e.stopPropagation()
            e.preventDefault()
          }

          // 临时添加事件处理器
          document.addEventListener('click', clickHandler, {
            capture: true,
            once: true
          })

          // 延迟一小段时间确保点击事件被阻止
          setTimeout(() => {
            document.removeEventListener('click', clickHandler, { capture: true })
          }, 100)
        }

        isDragging = false
      }
    }
  }
}

export default drag
