<template>
  <div class="frame-main">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key"/>
    </transition>
  </div>
</template>

<script>
  export default {
    name: 'FrameMain',
    data() {
      return {}
    },
    computed: {
      key() {
        return this.$route.path
      }
    }
  }
</script>

<style lang="scss" scoped>
  .frame-main {
    flex: 1;
    position: relative;
  }
</style>