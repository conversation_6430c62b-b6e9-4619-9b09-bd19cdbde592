<template>
  <div class="frame-nav">
    <div class="title">数字化营销作战</div>
    <div class="nav-bar-item-group">
      <div style="margin-left: 50px;" v-for="item in menu" :key="item.path"
        class="nav-bar-item"
        :class="{'active': item.name === menuName}"
        @click="navClick(item)">
        {{ item.menuName }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        menuName: this.menuActive
      }
    },
    props: {
      menuActive: {
        type: String,
        default: ''
      },
      menu: {
        type: Array,
        default: () => []
      }
    },
    mounted() {
      console.log(this.menu);
    },
    watch: {
      menuActive(newVal, oldVal) {
        this.menuName = newVal;
      }
    },
    methods: {
      navClick(menuItem) {
        this.menuName = menuItem.name;
        this.$emit('navclick', menuItem);
      }
    }
  }
</script>

<style lang="scss" scoped>
  .frame-nav {
    display: flex;
    padding: 0 20px;

    .title {
      height: 56px;
      line-height: 56px;
      font-size: 18px;
      font-weight: bold;
    }
    
    .nav-bar-item-group {
      display: flex;
    }
    
    .nav-bar-item {
      cursor: pointer;
      // width: 80px;
      height: 56px;
      line-height: 56px;
      font-size: 18px;
      font-weight: 500;
      // color: rgba(255,255,255,0.6);
    }
    
    .active {
      font-weight: bold;
      // color: #54E5AE;
      background-image: url(../../assets/images/menuselect.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
</style>