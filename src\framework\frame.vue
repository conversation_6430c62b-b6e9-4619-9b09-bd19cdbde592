<template>
  <div class="frame-index">
    <!-- <frame-nav :menu-active="menuActive" :menu="menuArray" @navclick="navClick" /> -->
    <frame-main />
  </div>
</template>

<script>
import FrameMain from './components/frameMain'
// import FrameNav from './components/frameNav'

export default {
  components: {
    FrameMain
    // FrameNav
  },
  data() {
    return {
      menuActive: 'dashboard',
      menuArray: [
        { name: 'dashboard', menuName: '菜单1', path: '/dashboard' },
        { name: 'analyse', menuName: '菜单2', path: '/analyse' },
        { name: 'sign', menuName: '菜单3', path: '/sign' },
        { name: 'device', menuName: '菜单4', path: '/device' }
      ]
    }
  },
  mounted() {
  },
  methods: {
    navClick(menuItem) {
      this.$router.push({ path: menuItem.path })
    }
  }
}
</script>

<style lang="scss" scoped>
  .frame-index {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    // background-image: url(../assets/images/backgroundover.png);
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
  }
</style>
