<template>
  <section class="app-main">
    <div class="app-main-container">
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key" />
      </transition>
    </div>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    key() {
      return this.$route.path
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  /* min-height: calc(100vh - 50px); */
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 16px;
  background: #F8F9FA;
  display: flex;
}
.app-main-container {
  flex: 1;
  position: relative;
  overflow-y: auto;
  background: #ffffff;
}
.fixed-header+.app-main {
  padding-top: 66px;
  /* margin-top: 66px; */
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
