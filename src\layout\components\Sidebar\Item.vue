<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      if (icon.includes('el-icon')) {
        vnodes.push(<i class={[icon, 'sub-el-icon']} />)
      } else {
        // vnodes.push(<svg-icon icon-class={icon}/>)
        const url = require('@/assets/images/' + icon + '.png')
        vnodes.push(<img src={url} class={'menu-image'} />)
      }
    }

    if (title) {
      vnodes.push(<span slot='title' style='font-size: 16px'>{(title)}</span>)
    }
    return vnodes
  }
}
</script>

<style scoped>
.sub-el-icon {
  /* color: currentColor; */
  /* width: 1em;
  height: 1em; */
  width: 30px;
  font-size: 20px;
  color: #F4F4F4;
}

.menu-image {
  width: 30px;
  height: 30px;
  margin-right: 16px;
  margin-bottom: 2px;
}
</style>
