import Vue from 'vue'
import Router from 'vue-router'
import menu from './menu.js'
import { getAreaInfo } from '@/api/user'
Vue.use(Router)

/* 解决路由跳转报错：Redirected when going from “/login“ to “/dashboard“ via a navigation guard */
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject)
  }
  return originalPush.call(this, location).catch((err) => err)
}

/* Layout */
// import Layout from '@/layout'
import Frame from '@/framework/frame'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  // {
  //   path: '/login',
  //   component: () => import('@/views/login/index'),
  //   hidden: true
  // },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/',
    component: Frame,
    redirect: '/network',
    children: menu
  },
  {
    path: '/operation',
    component: () => import('@/views/operation/index.vue'),
    children: [
      {
        path: '/operation/indexInsight',
        component: () => import('@/views/indexInsight'),
        name: 'indexInsight'
      },
      {
        path: '/operation/operationMap',
        component: () => import('@/views/operationMap'),
        name: 'operationMap'
      },
      {
        path: '/operation/taskAssessment',
        component: () => import('@/views/taskAssessment'),
        name: 'taskAssessment'
      },
      {
        path: '/operation/taskManagement',
        component: () => import('@/views/taskManagement'),
        name: 'taskManagement'
      }
    ],
    redirect: '/operation/operationMap'
  },
  {
    path: '/market',
    component: () => import('@/views/market/index.vue'),
    children: [
      {
        path: '/market/indicatorInsights',
        component: () => import('@/views/indicatorInsights'),
        name: 'indicatorInsights'
      },
      {
        path: '/market/executionMonitoring',
        component: () => import('@/views/executionMonitoring'),
        name: 'executionMonitoring'
      },
      {
        path: '/market/taskScheduling',
        component: () => import('@/views/taskScheduling'),
        name: 'taskScheduling'
      },
      {
        path: '/market/executionMonitoring/detail',
        component: () => import('@/views/executionMonitoring/detail'),
        name: 'executionMonitoringDetail'
      }
    ],
    redirect: '/market/indicatorInsights'
  },
  {
    path: '/networkCoverage',
    component: () => import('@/views/networkCoverage/index.vue'),
    children: [
      {
        path: '/networkCoverage/wired',
        component: () => import('@/views/networkCoverage/wired.vue'),
        name: 'networkCoverageWired'
      },
      {
        path: '/networkCoverage/wireless',
        component: () => import('@/views/networkCoverage/wireless.vue'),
        name: 'networkCoverageWireless'
      }
    ],
    redirect: '/networkCoverage/wired'
  },
  {
    path: '/network',
    component: () => import('@/views/networkAll/index.vue'),
    meta: { 
          keepAlive: true,
        },
    children: [
      // {
      //   path: '/network/wired',
      //   component: () => import('@/views/networkAll/NetworkWired.vue'),
      //   name: 'wired',
      //   meta: { 
      //         keepAlive: true,
      //       }
      // },
      {
        path: '/network/wiredNet',
        component: () => import('@/views/networkAll/WiredNet.vue'),
        name: 'wiredNet'
      },
      // {
      //   path: '/network/wireless',
      //   component: () => import('@/views/networkAll/WiredlessNet.vue'),
      //   name: 'wirelessNet',
      //   meta: { 
      //         keepAlive: true,
      //       }
      // }
    ]
  },
  {
    path:'/test',
    component: () => import('@/views/networkCoverage/test.vue'),
  },
  {
    path: '/test2',
    component: () => import('@/views/echartTest/index.vue'),
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = []

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

router.beforeEach((to, from, next) => {
  if (!sessionStorage.getItem('userInfo')) {
    getAreaInfo().then(res => {
      if (res.code === 200) {
        sessionStorage.setItem('userInfo', JSON.stringify(res.data))
      }
    })
    next()
  } else {
    next()
  }
})

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
