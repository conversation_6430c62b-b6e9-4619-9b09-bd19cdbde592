import Vue from 'vue'
import Vuex from 'vuex'
// import getters from './getters'
// import app from './modules/app'
// import permission from './modules/permission'
// import settings from './modules/settings'
// import screen from './modules/screen'
import networkCoverage from './modules/networkCoverage'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    // app,
    // permission,
    // settings,
    // screen,
    networkCoverage
  }
  // getters
})

export default store
