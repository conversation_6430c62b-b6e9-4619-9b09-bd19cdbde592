const SET_AREA = 'setArea'
const SET_FORM = 'setForm'
const SET_UPDATE_KEY = 'setUpdateKey'
const SET_SELECT_AREA_LIST = 'setSelectAreaList'
export default {
  namespaced: true,
  state: {
    commonCurrentArea: {}, // 当前区域
    commonSelectForm: {}, // 当前选择条件
    commonSelectAreaList: {}, // 选择框数据
    updateKey: 0 // 区域变化 更新标识 用于监听
  },
  getters: {},
  mutations: {
    [SET_AREA](state, data) {
      state.commonCurrentArea = data
    },
    [SET_FORM](state, data) {
      state.commonSelectForm = data
    },
    [SET_SELECT_AREA_LIST](state, data) {
      state.commonSelectAreaList = data
    },
    [SET_UPDATE_KEY](state) {
      state.updateKey++
    }
  },
  actions: {
    setArea({ commit }, params) {
      commit(SET_AREA, params)
    },
    setForm({ commit }, params) {
      commit(SET_FORM, params)
    },
    setSelectAreaList({ commit }, params) {
      commit(SET_SELECT_AREA_LIST, params)
    },
    setUpdateKey({ commit }, params) {
      commit(SET_UPDATE_KEY, params)
    }
  }
}
