import {
  getOverViewAll,
  getTrainingNameInfo,
  getAthleteTrainingInfo,
  getScrollAthleteInfo,
  getScrollCoachInfo,
  getProgramTypeInfo,
  getShooterScoreSubsection,
  getPlayerCount,
  getCoachCount,
  getShooterCountInfo,
  getTrainingInfo,
  getJDData,
  getShootShake,
  getMonitorList,
  getLastShootShake,
  getLastTenShoot,
  getLastShootShakeById,
  getShakeStaticList
} from '@/api/screen'

const state = {
  playerSearch: ''
}

const mutations = {
  SET_PlayerSearch: (state, search) => {
    state.playerSearch = search
  }
}

const actions = {
  set_PlayerSearch({ commit }, search) {
    commit('SET_PlayerSearch', search)
  },
  getOverViewAll(context, params) {
    return new Promise((resolve, reject) => {
      getOverViewAll(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getTrainingNameInfo(context, params) {
    return new Promise((resolve, reject) => {
      getTrainingNameInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getAthleteTrainingInfo(context, params) {
    return new Promise((resolve, reject) => {
      getAthleteTrainingInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getScrollAthleteInfo(context, params) {
    return new Promise((resolve, reject) => {
      getScrollAthleteInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getScrollCoachInfo(context, params) {
    return new Promise((resolve, reject) => {
      getScrollCoachInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getProgramTypeInfo(context, params) {
    return new Promise((resolve, reject) => {
      getProgramTypeInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getShooterScoreSubsection(context, params) {
    return new Promise((resolve, reject) => {
      getShooterScoreSubsection(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getPlayerCount(context, params) {
    return new Promise((resolve, reject) => {
      getPlayerCount(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getCoachCount(context, params) {
    return new Promise((resolve, reject) => {
      getCoachCount(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getShooterCountInfo(context, params) {
    return new Promise((resolve, reject) => {
      getShooterCountInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getTrainingInfo(context, params) {
    return new Promise((resolve, reject) => {
      getTrainingInfo(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getJDData(context, params) {
    return new Promise((resolve, reject) => {
      getJDData(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getShootShake(context, params) {
    return new Promise((resolve, reject) => {
      getShootShake(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getMonitorList(context, params) {
    return new Promise((resolve, reject) => {
      getMonitorList(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getLastShootShake(context, params) {
    return new Promise((resolve, reject) => {
      getLastShootShake(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getLastTenShoot(context, params) {
    return new Promise((resolve, reject) => {
      getLastTenShoot(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getLastShootShakeById(context, params) {
    return new Promise((resolve, reject) => {
      getLastShootShakeById(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getShakeStaticList(context, params) {
    return new Promise((resolve, reject) => {
      getShakeStaticList(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
