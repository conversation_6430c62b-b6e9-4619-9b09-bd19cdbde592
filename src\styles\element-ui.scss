// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;

  .dialog-header {
    display: flex;
    align-items: center;

    &-icon {
      content:url('../assets/images/line_03.png');
      width: 5px;
      height: 20px;
    }

    &-title {
      margin-left: 19px;
      font-size: 20px;
      font-weight: bold;
      color: #333333;
    }
    
    &-close {
      color: #909399;
      cursor: pointer;
      position: absolute;
      top: 20px;
      right: 20px;
    }
  }

  .dialog-footer {
    padding: 10px 20px 20px;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-tree {
  .el-tree-node__content {
    height: 32px;
  }
}

// el-tree选中后的节点字体颜色
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    color: #3D7FFF !important;
}

.el-form {
  label {
    font-weight: normal;
  }
}

.el-tabs {
  .el-tabs__nav-wrap {
    padding-left: 20px;
  }
}
