@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
// @import './sidebar.scss';

// html, body {
//     font-family: Source <PERSON>,Microsoft YaHei,sans-serif;
//     background-color: rgba(12, 23, 32, 0.8);
//   }
  
@media screen and (min-width: 2080px) {
  body {
    display: flex;
    justify-content: center;
  }
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei, Source Han Sans CN, PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Arial, sans-serif;
  // background-color: rgba(18, 28, 42, 1);
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}
