import Cookies from 'js-cookie'

const TokenKey = 'com_physical_yn_token'
const RefreshTokenKey = 'com_physical_yn_refresh_token'

export function getToken() {
  // return Cookies.get(TokenKey)
  return localStorage.getItem(TokenKey)
}

export function setToken(token) {
  // return Cookies.set(Token<PERSON><PERSON>, token)
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  // return Cookies.remove(TokenKey)
  return localStorage.removeItem(TokenKey)
}

export function getRefreshToken() {
  // return Cookies.get(RefreshTokenKey)
  return localStorage.getItem(RefreshTokenKey)
}

export function setRefreshToken(token) {
  // return Cookies.set(RefreshTokenKey, token)
  return localStorage.setItem(RefreshTokenKey, token)
}

export function removeRefreshToken() {
  // return Cookies.remove(RefreshTokenKey)
  return localStorage.removeItem(RefreshTokenKey)
}
