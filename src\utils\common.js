"use strict"

// 获取url里面的参数（window.location.search里面的参数）
export const getQueryString = (name) => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  let r = window.location.search.substr(1).match(reg)
  if (r !== null) {
    return unescape(r[2])
  } else {
    return ''
  }
}
// 获取token
export const getQueryString1 = (name) => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  let url = window.location.href
  let index = url.lastIndexOf('?')
  let token = url.substring(index + 1)
  let r = token.match(reg)
  if (r !== null) {
    return unescape(r[2])
  } else {
    return ''
  }
}
// 判断安卓机还是苹果机
export const isAndroid = () => {
  let userEngine = navigator.userAgent
  let isAndroid = userEngine.indexOf('Android') > -1 || userEngine.indexOf('Linux') > -1
  return isAndroid
}

// 判断安卓机还是苹果机
export const isIOS = () => {
  let deviceIsWindowsPhone = navigator.userAgent.indexOf("Windows Phone") >= 0;
  let isIOS = /iP(ad|hone|od)/.test(navigator.userAgent) && !deviceIsWindowsPhone;
  return isIOS
}

// 获取当前访问地址
export const getLocationHref = () => {
  let url = window.location.href
  return url.substr(0, url.indexOf('#') + 1)
}

// 屏幕滚动相关
export const switchScroll = {
  preventScroll: function() { // 阻止屏幕滚动
    this.scrollTop = document.body.scrollTop || document.documentElement.scrollTop
    document.body.style.cssText += 'position:fixed;width:100%;top:-' + this.scrollTop + 'px;';
  },
  recoverScroll: function() { // 恢复屏幕滚动
    let body = document.body;
    body.style.position = '';
    let top = body.style.top;
    document.body.scrollTop = document.documentElement.scrollTop = -parseInt(top);
    body.style.top = '';
  }
}

/**
 * 生成随机 uuid.
 *
 * 使用: UUID(length, radix)
 *   length - uuid长度
 *   radix  - the number of allowable values for each character.
 *
 * 示例:
 *   // 无参数  - returns RFC4122, version 4 ID
 *   >>> UUID()
 *   "92329d39-6f5c-4520-abfc-aab64544e172"
 *
 *   // 1个参数 - 返回指定长度的uuid
 *   >>> UUID(15)     // 15 character ID (default base=36)
 *   "vcydxgltxrvzstv"
 *
 *   // 2个参数 - returns ID of the specified length, and radix. (Radix must be <= 36)
 *   >>> UUID(8, 2)  // 8 character ID (base=2)
 *   "01001010"
 *   >>> UUID(8, 10) // 8 character ID (base=10)
 *   "47473046"
 *   >>> UUID(8, 16) // 8 character ID (base=16)
 *   "098f4d35"
 */
export const UUID = (len, radix) => {
  let chars = '0123456789abcdefghijklmnopqrstuvwxyz'.split('');
  let uuid = [],
    i;
  radix = radix || chars.length;
  radix = radix > chars.length ? chars.length : radix;

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) {
      uuid[i] = chars[0 | Math.random() * radix];
    }
  } else {
    // rfc4122, version 4 form
    let r;

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16;
        uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
      }
    }
  }
  return uuid.join('');
}

/**
 * 对象的深度克隆
 * @param obj
 * @returns {any}
 */
export function obj_clone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 格式化字符串型或时间戳日期
 * @param date 日期，如果传入值是null或者空字符串，则默认为当前日期
 * @param format 格式化字符，例如"yyyy-MM-dd HH:mm"
 * @returns {String}
 */
export function getDateTimeString(date, format) {
  if (date && date != '') {
    switch (format || (format = "yyyy-MM-dd"),
      typeof date) {
      case "string":
        date = date.length < 8 ? e + "/01" : e; // 保证safari兼容性，如果是2021/01或2021-01的形式就需要补充日期
        date = new Date(date.replace(/\-/g, "/"));
        break;
      case "number":
        date = new Date(date)
    }
  } else {
    date = new Date()
  }
  if (!(!date instanceof Date)) {
    let a = {
      yyyy: date.getFullYear(),
      M: date.getMonth() + 1,
      d: date.getDate(),
      H: date.getHours(),
      m: date.getMinutes(),
      s: date.getSeconds(),
      MM: ("" + (date.getMonth() + 101)).substr(1),
      dd: ("" + (date.getDate() + 100)).substr(1),
      HH: ("" + (date.getHours() + 100)).substr(1),
      mm: ("" + (date.getMinutes() + 100)).substr(1),
      ss: ("" + (date.getSeconds() + 100)).substr(1)
    };
    return format.replace(/(yyyy|MM?|dd?|HH?|ss?|mm?)/g, function() {
      return a[arguments[0]]
    })
  }
}

/**
 * 解析url中的查询参数，返回数组
 */
export const getQueryParamArray = () => {
  //获取url中“?”后面的参数：http://127.0.0.1:8080/index.html?city=1&county=2
  let params = [];
  let searchURL = window.location.search;
  if (searchURL.length > 1) {
    //去掉“?”
    searchURL = searchURL.substring(1, searchURL.length);
    let paramslist = searchURL.split("&");
    for (let i = 0; i < paramslist.length; i++) {
      // let param = paramslist[i].split("=");
      // params.push({key: param[0], value: unescape(param[1])})
      let idx = paramslist[i].indexOf("=")
      let key = paramslist[i].substring(0, idx);
      let val = paramslist[i].substring(idx + 1);
      params.push({
        key: key,
        value: unescape(val)
      })
    }
  }
  return params
}

/**
 * 解析url中的查询参数，返回json对象
 */
export const getQueryParamObject = () => {
  //获取url中“?”后面的参数：http://127.0.0.1:8080/index.html?city=1&county=2
  let params = {};
  let searchURL = window.location.search;
  if (searchURL.length > 1) {
    //去掉“?”
    searchURL = searchURL.substring(1, searchURL.length);
    let paramslist = searchURL.split("&");
    for (let i = 0; i < paramslist.length; i++) {
      // let param = paramslist[i].split("=");
      // params[param[0]] = unescape(val)
      let idx = paramslist[i].indexOf("=")
      let key = paramslist[i].substring(0, idx);
      let val = paramslist[i].substring(idx + 1);
      params[key] = unescape(val)
    }
  }
  return params
}

/**
 * 判断给定对象是不是函数，返回 true - 函数，false - 不是函数
 * @param {Object} obj 给定对象
 */
export function isFunction(obj) {
  return typeof obj === "function" && typeof obj.nodeType !== "number"
}

/**
 * 判断给定值是否是数字
 * @param {Object} value
 */
export const isNumber = (value) => {
  return !isNaN(parseFloat(value)) && isFinite(value)
}

export const convertValueForMinSec = (value) => {
  let minute = parseInt(value/60)
  let second = value%60
  return minute + ":" + (second < 10 ? ('0' + second) : second)
}
