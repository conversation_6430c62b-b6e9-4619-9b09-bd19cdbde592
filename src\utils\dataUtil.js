/* Echarts图表字体、间距自适应 */
export const fitChartSize = (size, defalteWidth = 1920) => {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
  if (!clientWidth) return size
  const scale = (clientWidth / defalteWidth)
  return Number((size * scale).toFixed(3))
}

/**
 * 格式化数字并返回合适的单位
 * @param {number} value - 需要格式化的数值
 * @param {boolean} isPercentage - 是否是百分比数据
 * @param {number} decimal - 保留小数位数
 * @return {object} 包含格式化后的值和单位
 */
export const formatNumberWithUnit = (value, isPercentage = false, decimal = 2, needMultiplication = true) => {
  // 如果值不存在或不是数字，返回默认值
  if (value == null || isNaN(Number(value))) {
    return {
      value: '',
      unit: ''
    }
  }

  let num = Number(value)
  let unit = ''

  // 处理百分比
  if (isPercentage) {
    num = needMultiplication ? (num * 100) : num
    unit = '%'
    return {
      value: num.toFixed(decimal),
      unit: unit
    }
  }

  // 处理普通数字，根据大小动态调整单位
  if (num >= 1000000000) { // 10亿
    num = num / 100000000
    unit = '亿'
  } else if (num >= 100000000) { // 1亿
    num = num / 100000000
    unit = '亿'
  } else if (num >= 10000000) { // 1000万
    num = num / 10000000
    unit = '千万'
  } else if (num >= 10000) { // 1万
    num = num / 10000
    unit = '万'
  }

  // 如果数值非常小且不为零，保留更多小数位
  if (num > 0 && num < 0.01) {
    return {
      value: num.toFixed(4),
      unit: unit
    }
  }

  return {
    value: num.toFixed(decimal),
    unit: unit
  }
}
