import axios from 'axios'
import {
  Message
} from 'element-ui'
import { EventBus } from '@/views/dashboard/eventBus' // 给gridOperations目录引入事件总线
// import store from '@/store'
// import {
//   getToken,
//   getRefreshToken,
//   setToken
// } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60000 // request timeout
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // do something before request is sent
    // config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    // if (store.getters.token) {
    //   // let each request carry token
    //   // ['X-Token'] is a custom headers key
    //   // please modify it according to the actual situation
    //   // config.headers['Authorization'] = getToken()
    // }
    EventBus.$emit('start-loading')
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    EventBus.$emit('stop-loading')
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    EventBus.$emit('stop-loading')
    if (response.config.responseType === 'blob') {
      return response
    }
    if (res.type === 'multipart/form-data') {
      return response
    }
    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 200) {
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  }, error => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    EventBus.$emit('stop-loading')
    return Promise.reject(error)
  }
)

export default service
