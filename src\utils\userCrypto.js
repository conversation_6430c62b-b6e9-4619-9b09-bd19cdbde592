import CryptoJS from 'crypto-js'
import {getDateTimeString} from '@/utils/common.js'

const preKey = 'qP2$bG9;'

const reverseStr = str => {
  return str.split('').reverse().join('')
}

const encryptByCBC = (word, keyStr, ivStr) => {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const iv = CryptoJS.enc.Utf8.parse(ivStr)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  //解密
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC, //AES加密模式
    padding: CryptoJS.pad.Pkcs7 //填充方式
  })
  //返回base64格式密文
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
}

const decryptByCBC = (word, keyStr, ivStr) => {
  const key = CryptoJS.enc.Utf8.parse(keyStr);
  const iv = CryptoJS.enc.Utf8.parse(ivStr);
  //base64格式密文转换
  const base64 = CryptoJS.enc.Base64.parse(word);
  const src = CryptoJS.enc.Base64.stringify(base64);
  //解密
  const decrypt = CryptoJS.AES.decrypt(src, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC, //AES加密模式
    padding: CryptoJS.pad.Pkcs7 //填充方式
  });
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}

export function encryptPwd(needEncryptStr, userName) {
  let encrypted = CryptoJS.MD5(needEncryptStr).toString(CryptoJS.enc.Hex)
  // console.log(encrypted)
  // console.log(encrypted + userName)
  encrypted = CryptoJS.MD5(encrypted + userName).toString(CryptoJS.enc.Hex)
  return encrypted
}

export function encryptStr(needEncryptStr) {
  // const keyLength = 16
  const dateStr = getDateTimeString(null, 'yyyyMMdd')
  // console.log(dateStr)
  const key = preKey + dateStr, iv = reverseStr(key)
  return encryptByCBC(needEncryptStr, key, iv)
}

export function decryptStr(encryptedStr) {
  // const keyLength = 16
  const dateStr = getDateTimeString(null, 'yyyyMMdd')
  // console.log(dateStr)
  const key = preKey + dateStr, iv = reverseStr(key)
  return decryptByCBC(encryptedStr, key, iv)
}