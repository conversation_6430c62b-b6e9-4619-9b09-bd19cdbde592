<template>
    <div class="body-content">
      <div class="left-box">
        <div class="bigMarket nav"></div>
        <div class="border-box">
          <!-- 上左部分 -->
          <div class="info-box">
            <div class="title">
              <div class="title-icon"></div>
              <div class="title-text">数字作战目标客群</div>
            </div>
            <div class="data1-top-box">
              <div class="data1">
                <div class="icon1 icon1-1"></div>
                <div class="text">
                  <div class="name">融合升档</div>
                  <div class="number shadow">{{ this.data.value_001 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
              <div class="data1">
                <div class="icon1 icon1-2"></div>
                <div class="text">
                  <div class="name">套餐升档</div>
                  <div class="number shadow">{{ this.data.value_002 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
              <div class="data1">
                <div class="icon1 icon1-3"></div>
                <div class="text">
                  <div class="name">金牛</div>
                  <div class="number shadow">{{ this.data.value_003 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
              <div class="data1">
                <div class="icon1 icon1-4"></div>
                <div class="text">
                  <div class="name">金盾</div>
                  <div class="number shadow">{{ this.data.value_004 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
            </div>
            <div class="data1-bottom-box">
              <div class="data1">
                <div class="icon1 icon1-4"></div>
                <div class="text">
                  <div class="name">爱家小福袋</div>
                  <div class="number shadow">{{ this.data.value_005 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
              <div class="data1">
                <div class="icon1 icon1-5"></div>
                <div class="text">
                  <div class="name">流失风险</div>
                  <div class="number shadow">{{ this.data.value_006 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
              <div class="data1">
                <div class="icon1 icon1-6"></div>
                <div class="text">
                  <div class="name">质差修复</div>
                  <div class="number shadow">{{ this.data.value_007 }}<span>万</span></div>
                  <div class="bg1"></div>
                  <div class="bg2"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="chain"></div>
          <!-- 上右部分 -->
          <div class="info-box">
            <div class="title title-topRight" >
              <div class="title-icon"></div>
              <div class="title-text">商机上门服务单</div>
            </div>
            <!-- <div class="data2-top-box">
              <div class="data2">
                <div class="icon2 icon2-1"></div>
                <div class="text">
                  <div class="name">接入数商</div>
                  <div class="number"><span>{{ this.data.value_008 }}</span>家</div>
                </div>
              </div>
              <div class="data2">
                <div class="icon2 icon2-2"></div>
                <div class="text">
                  <div class="name">汇聚数据</div>
                  <div class="number"><span>{{ this.data.value_009 }}</span>类</div>
                </div>
              </div>
              <div class="data2">
                <div class="icon2 icon2-3"></div>
                <div class="text">
                  <div class="name">流通数据</div>
                  <div class="number"><span>{{ this.data.value_010 }}</span>条</div>
                </div>
              </div>
            </div> -->
            <!-- <div class="title data2-middle">
              <div class="title-icon"></div>
              <div class="title-text">市网协同服务单</div>
            </div> -->
            <div class="data2-4-bottom-box">
              <div class="text">
                <div class="name">派单量</div>
                <div class="number shadow">{{ this.data.value_011 }}<span>万</span></div>
              </div>
              <div class="text">
                <div class="name">预约量</div>
                <div class="number shadow">{{ this.data.value_012 }}<span>万</span></div>
              </div>
              <div class="text">
                <div class="name">上门量</div>
                <div class="number shadow">{{ this.data.value_013 }}<span>万</span></div>
              </div>
            </div>
          </div>
          <div class="chain"></div>
        </div>
        <div class="govBusines nav"></div>
        <div class="border-box">
          <!-- 下左部分 -->
          <div class="info-box">
            <div class="title">
              <div class="title-icon"></div>
              <div class="title-text">数字作战目标客群</div>
            </div>
            <div class="data3-box">
              <div class="left-data">
                <div class="text3-1 text">
                  <div class="name">楼宇目标数</div>
                  <div class="number"><span class="shadow">{{ this.data.value_025 }}</span>个</div>
                </div>
                <div class="text3-2 text">
                  <div class="name">泛住宿目标客户数</div>
                  <div class="number"><span class="shadow">{{ this.data.value_027 }}</span>辆</div>
                </div>
                <div class="text3-3 text">
                  <div class="name">沿街商铺客户数</div>
                  <div class="number"><span class="shadow">{{ this.data.value_029 }}</span>辆</div>
                </div>
              </div>
              <div class="right-data">
                <div class="text">
                  <div class="number"><span class="shadow">{{ this.data.value_026 }}</span>个</div>
                  <div class="name">低占领区域</div>
                </div>
                <div class="text">
                  <div class="number"><span class="shadow">{{ this.data.value_028 }}</span>个</div>
                  <div class="name">低占领区域</div>
                </div>
                <div class="text">
                  <div class="number"><span class="shadow">{{ this.data.value_030 }}</span>个</div>
                  <div class="name">低占领区域</div>
                </div>
              </div>
            </div>
          </div>
          <div class="chain"></div>
          <!-- 下右部分 -->
          <div class="info-box">
            <div class="title">
              <div class="title-icon"></div>
              <div class="title-text">区域式作战</div>
            </div>
            <div class="data4-top-box">
              <div class="left-data">
                <div class="icon4-1"></div>
                <div class="text">
                  <div class="name">任务总量</div>
                  <div class="number shadow">{{ this.data.value_031 }}<span>万</span></div>
                </div>
              </div>
              <div class="circlIcon-box">
                <div class="info">
                  <div class="icon4-2">
                    <div class="icon-inner"></div>
                  </div>
                  <div class="text">
                    <div class="number"><span>{{ this.data.value_032 }}</span>家</div>
                    <div class="name">作战区域</div>
                  </div>
                </div>
                <div class="info">
                  <div class="icon4-2">
                    <div class="icon-inner"></div>
                  </div>
                  <div class="text">
                    <div class="number"><span>{{ this.data.value_033 }}</span>家</div>
                    <div class="name">正在作战区域数</div>
                  </div>
                </div>
                <div class="info">
                  <div class="icon4-2">
                    <div class="icon-inner"></div>
                  </div>
                  <div class="text">
                    <div class="number"><span>{{ this.data.value_034 }}</span>家</div>
                    <div class="name">区域平均参与人数</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="title data4-middle">
              <div class="title-icon"></div>
              <div class="title-text">产品式作战</div>
            </div>
            <div class="data2-4-bottom-box">
              <div class="text">
                <div class="name">派单量</div>
                <div class="number">{{ this.data.value_035 }}<span>万</span></div>
              </div>
              <div class="text">
                <div class="name">执行量</div>
                <div class="number">{{ this.data.value_036 }}<span>万</span></div>
              </div>
              <div class="text">
                <div class="name">转化率</div>
                <div class="number">{{ this.data.value_037 }}<span>%</span></div>
              </div>
            </div>
          </div>
          <div class="chain"></div>
        </div>
      </div>
      <div class="right-box">
        <div class="rightTop-box">
          <div class="top-title">
            <div class="icon1"></div>
            <div class="icon2">
              <div class="text-left">
                <div class="name">CHN收入</div>
                <div class="number shadow">{{ this.data.value_014 }}<span>万元</span></div>
              </div>
              <div class="text-right">
                <div class="text">
                  <div class="name">增收</div>
                  <div class="number shadow">{{ this.data.value_015 }}<span>万元</span></div>
                </div>
                <div class="text">
                  <div class="name">增幅</div>
                  <div class="number shadow">{{ this.data.value_016 }}<span>%</span><img
                      src="../../../assets/images/dashboard/arrow-up.png" alt="">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="title data-top">
            <div class="title-icon"></div>
            <div class="title-text">爱家计划</div>
          </div>
          <div class="circlIcon-box right-circlIcon-data">
            <div class="info">
              <div class="icon4-2">
                <div class="icon-inner"></div>
              </div>
              <div class="text">
                <div class="number"><span>{{ this.data.value_017 }}</span>家</div>
                <div class="name">宽带进攻</div>
              </div>
            </div>
            <div class="info">
              <div class="icon4-2">
                <div class="icon-inner"></div>
              </div>
              <div class="text">
                <div class="number"><span>{{ this.data.value_018 }}</span>家</div>
                <div class="name">爱家升档</div>
              </div>
            </div>
            <div class="info">
              <div class="icon4-2">
                <div class="icon-inner"></div>
              </div>
              <div class="text">
                <div class="number"><span>{{ this.data.value_019 }}</span>家</div>
                <div class="name">爱家加固</div>
              </div>
            </div>
            <div class="info">
              <div class="icon4-2">
                <div class="icon-inner"></div>
              </div>
              <div class="text">
                <div class="number"><span>{{ this.data.value_020 }}</span>家</div>
                <div class="name">爱家福袋</div>
              </div>
            </div>
            <div class="info">
              <div class="icon4-2">
                <div class="icon-inner"></div>
              </div>
              <div class="text">
                <div class="number"><span>{{ this.data.value_021 }}</span>家</div>
                <div class="name">爱家融合</div>
              </div>
            </div>
          </div>
          <div class="title data-bottom">
            <div class="title-icon"></div>
            <div class="title-text">雷霆行动三大拉新</div>
          </div>
          <div class="thunder-box">
            <div class="data-box">
              <div class="icon icon1"></div>
              <div class="text">
                <div class="name">金牛CC拉新</div>
                <div class="number shadow">{{ this.data.value_022 }}<span>条</span></div>
              </div>
            </div>
            <div class="data-box">
              <div class="icon icon2"></div>
              <div class="text">
                <div class="name">家庭异网拉新</div>
                <div class="number shadow">{{ this.data.value_023 }}<span>条</span></div>
              </div>
            </div>
            <div class="data-box">
              <div class="icon icon3"></div>
              <div class="text">
                <div class="name">终端卡位拉新</div>
                <div class="number shadow">{{ this.data.value_024 }}<span>条</span></div>
              </div>
            </div>
          </div>
        </div>
        <div class="rightBottom-box">
          <div class="top-title">
            <div class="icon1"></div>
            <div class="icon2">
              <div class="text-left">
                <div class="name">商客客群收入</div>
                <div class="number shadow">{{ this.data.value_038 }}<span>万元</span></div>
              </div>
              <div class="text-right">
                <div class="text">
                  <div class="name">增收</div>
                  <div class="number shadow">{{ this.data.value_039 }}<span>万元</span></div>
                </div>
                <div class="text">
                  <div class="name">增收</div>
                  <div class="number shadow">{{ this.data.value_040 }}<span>%</span><img
                      src="../../../assets/images/dashboard/arrow-up.png" alt="">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="title data-top">
            <div class="title-icon"></div>
            <div class="title-text">规模</div>
          </div>
          <div class="middle-text-box">
            <div class="left-text">
              <div class="name"><img src="../../../assets/images/dashboard/people-icon.png" alt="">客户数净增</div>
              <div class="number shadow">{{ this.data.value_041 }}<span>万人</span></div>
            </div>
            <div class="right-text">
              <div class="name">增幅</div>
              <div class="number shadow">{{ this.data.value_042 }}<span>%</span><img
                  src="../../../assets/images/dashboard/arrow-up.png" alt="">
              </div>
            </div>
          </div>
          <div class="bottom-box">
            <div class="text">
              <div class="number shadow">{{ this.data.value_043 }}<span>个</span></div>
              <div class="name">低占区域减少</div>
            </div>
            <div class="text">
              <div class="number shadow">{{ this.data.value_044 }}<span>%</span></div>
              <div class="name">--</div>
            </div>
            <div class="text">
              <div class="number shadow">{{ this.data.value_045 }}<span>%</span></div>
              <div class="name">--</div>
            </div>
            <div class="text">
              <div class="number shadow">{{ this.data.value_046 }}<span>%</span></div>
              <div class="name">--</div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
<script>
export default {
  name: 'BodyContent',
  // props: {
  //   data: {
  //     type: Object,
  //     default: () => {
  //       return {}
  //     }
  //   }
  // },
  data() {
    return {
      data: {
        value_001: 'xx',
        value_002: 'xx',
        value_003: 'xx',
        value_004: 'xx',
        value_005: 'xx',
        value_006: 'xx',
        value_007: 'xx',
        value_008: 'xx',
        value_009: 'xx',
        value_010: 'xx',
        value_011: 'xx',
        value_012: 'xx',
        value_013: 'xx',
        value_025: 'xx',
        value_027: 'xx',
        value_029: 'xx',
        value_026: 'xx',
        value_028: 'xx',
        value_030: 'xx',
        value_031: 'xx',
        value_032: 'xx',
        value_033: 'xx',
        value_034: 'xx',
        value_035: 'xx',
        value_036: 'xx',
        value_037: 'xx',
        value_014: 'xx',
        value_015: 'xx',
        value_016: 'xx',
        value_017: 'xx',
        value_018: 'xx',
        value_019: 'xx',
        value_020: 'xx',
        value_021: 'xx',
        value_022: 'xx',
        value_023: 'xx',
        value_024: 'xx',
        value_038: 'xx',
        value_039: 'xx',
        value_040: 'xx',
        value_041: 'xx',
        value_042: 'xx',
        value_043: 'xx',
        value_044: 'xx',
        value_045: 'xx',
        value_046: 'xx',
      },
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
.body-content {
  width: 100%;
  flex: 1;
  display: flex;
  padding-left: vw(40);
  z-index: 99;
  .shadow {
    text-shadow: 0px 2px 7px rgba(34, 141, 188, 0.8);
  }
  .title {
    display: flex;
    justify-content: center;
    position: relative;
    // padding-left: vw(51);
    padding-top: vh(9);
    top: vh(-1);
    width: vw(318);
    height: vh(49);
    margin: 0 auto;
    background: url("../../../assets/images/dashboard/title.png") no-repeat;
    background-size: 100% 100%;
    .title-icon {
      width: vw(24);
      height: vh(24);
      background: url("../../../assets/images/dashboard/title-icon.png") no-repeat;
      background-size: 100% 100%;
      margin-right: vw(9.03);
    }
    .title-text {
      width: fit-content;
      height: vh(29);
      line-height: vh(29);
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vw(22);
      letter-spacing: vw(1.3);
    }
  }
  .title-topRight {
    margin-bottom: vh(71);
  }
  .circlIcon-box {
    width: vw(289);
    height: vh(84);
    // background-color: blueviolet;
    display: flex;
    justify-content: space-between;
    .info {
      width: vw(91);
      height: vh(82);
      position: relative;
      .icon4-2 {
        position: absolute;
        width: vw(49);
        height: vh(49);
        background: url("../../../assets/images/dashboard/data4-out-circle.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .icon-inner {
          width: vw(30);
          height: vh(30);
          background: url("../../../assets/images/dashboard/data4-inner-circle.png") no-repeat;
          background-size: 100% 100%;
        }
      }
      .text {
        width: 100%;
        height: vh(69);
        position: absolute;
        bottom: 0;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        line-height: vh(20.75);
        color: rgba(181, 211, 255, 1);
        padding-left: vw(21);
        .number {
          height: vh(44);
          span {
            display: inline-block;
            color: white;
            margin-right: vw(9);
            font-family: MiSans;
            font-weight: 630;
            font-size: vw(24);
            line-height: vh(44);
          }
        }
        .name {
          width: vw(60);
        }
      }
    }
  }
  .left-box {
    width: fit-content;
    display: flex;
    flex-direction: column;
    .nav {
      width: vw(958);
    }
    .bigMarket {
      height: vh(92);
      margin-bottom: vh(12);
      background: url("../../../assets/images/dashboard/nav-bigMarket.png") no-repeat;
      background-size: 100% 133%;
    }
    .govBusines {
      height: vh(91);
      margin-top: vh(8);
      background: url("../../../assets/images/dashboard/nav-govBusiness.png") no-repeat;
      background-size: 100% 133%;
    }
    .border-box {
      display: flex;
      align-items: center;
      padding: vh(1) 0 0 vw(10);
      width: fit-content;
      .info-box {
        color: white;
        width: vw(520);
        height: vh(363);
        border-radius: vw(24);
        // background: linear-gradient(to bottom, rgba(89, 183, 255, 0.5029), rgba(41, 33, 98, 0));
        background: url("../../../assets/images/dashboard/left-infobox.png") no-repeat;
        background-size: 100% 100%;
        .data1-top-box {
          width: vw(382);
          height: vh(160);
          display: flex;
          flex-wrap: wrap;
          margin: 0 auto;
          margin-top: vh(21);
          margin-bottom: vh(16);
        }
        .data1 {
          display: flex;
          width: vw(191);
          height: vh(80);
          .icon1 {
            position: absolute;
            width: vw(88);
            height: vh(80.5);
          }
          .icon1-1 {
            background: url("../../../assets/images/dashboard/computer.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon1-2 {
            background: url("../../../assets/images/dashboard/form.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon1-3 {
            background: url("../../../assets/images/dashboard/circle.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon1-4 {
            background: url("../../../assets/images/dashboard/file.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon1-5 {
            background: url("../../../assets/images/dashboard/building.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon1-6 {
            background: url("../../../assets/images/dashboard/rise.png") no-repeat;
            background-size: 100% 100%;
          }
          .text {
            width: vw(151);
            height: vh(60);
            display: flex;
            flex-direction: column;
            // background-color: black;
            margin-left: vw(41);
            margin-top: vh(3);
            padding-left: vw(33.96);
            position: relative;
            .name {
              width: fit-content;
              height: vh(20);
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(16);
              line-height: vh(19.39);
              margin-bottom: vh(8.22);
            }
            .number {
              width: fit-content;
              height: vh(29);
              font-family: MiSans;
              font-weight: 630;
              font-size: vw(22);
              line-height: vh(29);
              text-align: center;
              span {
                display: inline-block;
                margin-left: vw(4);
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(14);
                line-height: vh(19.39);
                text-shadow: none;
              }
            }
            .bg1 {
              position: absolute;
              left: vh(11.33);
              top: vh(8);
              width: vw(130);
              height: vh(18);
              background: url("../../../assets/images/dashboard/data1-bg1.png") no-repeat;
              background-size: 100% 100%;
            }
            .bg2 {
              position: absolute;
              left: 0;
              bottom: 0;
              width: vw(151);
              height: vh(30);
              background: url("../../../assets/images/dashboard/data1-bg2.png") no-repeat;
              background-size: 100% 100%;
            }
          }
        }
        .data1-bottom-box {
          display: flex;
          padding-left: vw(9);
          .data1 {
            margin-right: vw(-22); //样式覆盖
          }
        }
        .data2-top-box {
          width: vw(477);
          height: vh(58);
          margin-top: vh(24);
          // background-color: aquamarine;
          display: flex;
          justify-content: space-between;
          margin-left: vw(35);
          .data2 {
            width: fit-content;
            height: vh(58);
            display: flex;
            .icon2 {
              width: vw(58);
              height: vh(58);
              margin-right: vw(8);
            }
            .icon2-1 {
              background: url("../../../assets/images/dashboard/data2-1.png") no-repeat;
              background-size: 100% 100%;
            }
            .icon2-2 {
              background: url("../../../assets/images/dashboard/data2-2.png") no-repeat;
              background-size: 100% 100%;
            }
            .icon2-3 {
              background: url("../../../assets/images/dashboard/data2-3.png") no-repeat;
              background-size: 100% 100%;
            }
            .text {
              width: fit-content;
              height: vh(24);
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(16);
              line-height: vh(24);
              color: rgba(255, 255, 255, 0.75);
              .number {
                span {
                  display: inline-block;
                  margin-right: vw(4);
                  font-family: Alimama ShuHeiTi;
                  font-weight: 700;
                  font-size: vw(28);
                  line-height: vh(34);
                  color: white;
                  text-shadow: none;
                }
              }
            }
          }
        }
        .data2-middle {
          margin-top: vh(16);
          margin-bottom: vh(15);
        }
        //2，4通用
        .data2-4-bottom-box {
          width: vw(478);
          height: vh(137);
          margin: 0 auto;
          // background-color: aquamarine;
          display: flex;
          justify-content: space-between;
          .text {
            width: vw(137);
            height: vh(151);
            background: url("../../../assets/images/dashboard/data-bottom-icon.png") no-repeat;
            background-size: 100% 100%;
            .name {
              width: fit-content;
              height: vh(34);
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(20);
              line-height: vh(34);
              margin: 0 auto;
            }
            .number {
              width: fit-content;
              height: vh(38);
              font-family: MiSans;
              font-weight: 630;
              font-size: vw(28);
              line-height: vh(38);
              margin: 0 auto;
              span {
                display: inline-block;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: vw(16);
                line-height: vh(30);
                vertical-align: bottom;
                margin-left: vw(4);
                background: linear-gradient(0deg, rgba(239, 248, 254, 0.75), rgba(239, 248, 254, 0.75)),
                  linear-gradient(180deg, #EEF5FF 26.85%, #8AB6F8 72.22%);
                background-clip: text;
                color: transparent;
                text-shadow: none;
              }
            }
          }
        }
        .data3-box {
          width: 100%;
          display: flex;
          .left-data {
            width: fit-content;
            height: vh(285);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding-left: vw(27);
            padding-top: vh(9);
            .text {
              width: vw(267);
              height: vh(85);
              font-family: PingFang SC;
              line-height: vh(22.29);
              padding-left: vw(107.85);
              padding-top: vh(4.6);
              .name {
                width: fit-content;
                height: vh(23);
                font-weight: 600;
                font-size: vw(18.44);
                margin-bottom: vh(4);
              }
              .number {
                font-weight: 400;
                font-size: vw(16.1);
                span {
                  display: inline-block;
                  margin-right: vw(4.6);
                  font-family: MiSans;
                  font-weight: 630;
                  font-size: vw(25.3);
                  line-height: vh(34);
                }
              }
            }
            .text3-1 {
              background: url("../../../assets/images/dashboard/data3-left1.png") no-repeat;
              background-size: 100% 100%;
            }
            .text3-2 {
              background: url("../../../assets/images/dashboard/data3-left2.png") no-repeat;
              background-size: 100% 100%;
            }
            .text3-3 {
              background: url("../../../assets/images/dashboard/data3-left3.png") no-repeat;
              background-size: 100% 100%;
            }
          }
          .right-data {
            width: vw(210);
            height: vh(277);
            margin-top: vh(1.15);
            // background-color: aquamarine;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .text {
              width: vw(210);
              height: vh(77);
              padding-left: vw(55.21);
              padding-top: vh(6.56);
              background: url("../../../assets/images/dashboard/data3-right.png") no-repeat;
              background-size: 100% 100%;
              font-family: PingFang SC;
              line-height: vw(22.29);
              .number {
                // height: vh(34);
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(16.1);
                span {
                  display: inline-block;
                  // margin-right: vw(4.37);
                  width: fit-content;
                  line-height: vh(34);
                  font-family: MiSans;
                  font-weight: 630;
                  font-size: vw(25.3);
                  margin-right: vw(4.37);
                }
              }
              .name {
                width: vw(92);
                height: vh(23);
                font-weight: 600;
                font-size: vw(18.4);
              }
            }
          }
        }
        .data4-top-box {
          width: 100%;
          height: vh(84);
          display: flex;
          padding-left: vw(24);
          // background-color: aqua;
          .left-data {
            width: vw(164);
            height: vh(84);
            margin-right: vw(17);
            // background-color: black;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .icon4-1 {
              width: vw(76);
              height: vh(76);
              background: url("../../../assets/images/dashboard/data4.png") no-repeat;
              background-size: 100% 100%;
            }
            .text {
              width: vw(80);
              height: vh(84);
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .name {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: vw(20);
                line-height: vh(34);
              }
              .number {
                font-family: MiSans;
                font-weight: 630;
                font-size: vw(32);
                line-height: vh(42);
              }
            }
          }
        }
        .data4-middle {
          margin-top: vh(24);
          margin-bottom: vh(4);
        }
      }
      .chain {
        position: relative;
        right: vw(9);
        width: vw(108);
        height: vh(292.11);
        background: url("../../../assets/images/dashboard/right-chain.png") no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .right-box {
    position: relative;
    right: vw(9);
    padding-top: vh(37);
    color: white;
    .rightTop-box {
      width: vw(584);
      height: vh(461);
      border-radius: vw(24);
      background: url("../../../assets/images/dashboard/right-top-infobox.png") no-repeat;
      background-size: 100% 100%;
      margin-bottom: vh(36);
      .thunder-box {
        width: vw(546.7);
        height: vh(74.5);
        margin: 0 auto;
        margin-top: vh(12);
        // background-color: aqua;
        display: flex;
        justify-content: space-between;
        .data-box {
          width: vw(175);
          height: vh(75);
          display: flex;
          // background-color: black;
          .icon {
            width: vw(78);
            height: vh(71);
            // margin-right: vw(4);
          }
          .icon1 {
            background: url("../../../assets/images/dashboard/right-thunder-icon1.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon2 {
            background: url("../../../assets/images/dashboard/right-thunder-icon2.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon3 {
            background: url("../../../assets/images/dashboard/right-thunder-icon3.png") no-repeat;
            background-size: 100% 100%;
          }
          .text {
            width: fit-content;
            height: vh(68);
            .name {
              width: fit-content;
              height: vh(34);
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(16);
              line-height: vh(34);
            }
            .number {
              display: flex;
              align-items: flex-end;
              font-family: Alimama ShuHeiTi;
              font-weight: 700;
              font-size: vw(28);
              line-height: vh(34);
              background: linear-gradient(180deg, #FFFFFF 50%, #20B5FF 119.26%);
              background-clip: text;
              color: transparent;
              span {
                display: inline-block;
                margin-left: vw(4);
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(16);
                line-height: vh(24);
                text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
              }
            }
          }
        }
      }
    }
    .rightBottom-box {
      width: vw(584);
      height: vh(415);
      border-radius: vw(24);
      background: url("../../../assets/images/dashboard/right-bottom-infobox.png") no-repeat;
      background-size: 100% 100%;
      .middle-text-box {
        width: vw(410);
        height: vh(37);
        margin: 0 auto;
        display: flex;
        align-items: center;
        margin-bottom: vh(15);
        .left-text {
          width: vw(237);
          height: vh(37);
          display: flex;
          justify-content: space-between;
          margin-right: vw(40);
          .name {
            display: flex;
            align-items: center;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: vw(20);
            line-height: vh(37);
            letter-spacing: 6%;
            background: linear-gradient(183.66deg, #D9E8FF 25.59%, #7FAEF2 87.32%);
            background-clip: text;
            color: transparent;
            img {
              width: vw(20);
              height: vh(20);
              margin-right: vw(5);
            }
          }
          .number {
            display: flex;
            align-items: center;
            font-family: MiSans;
            font-weight: 630;
            font-size: vw(28);
            line-height: vh(37);
            span {
              display: inline-block;
              margin-right: vw(3.47);
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(14);
              line-height: vh(16.84);
            }
          }
        }
        .right-text {
          width: vw(111);
          height: vh(27);
          display: flex;
          align-items: center;
          justify-content: space-between;
          .name {
            // width: vw(32);
            color: rgba(244, 246, 254, 0.75);
            font-family: PingFang SC;
            font-weight: 600;
            font-size: vw(16);
            line-height: vh(22);
          }
          .number {
            font-family: MiSans;
            font-weight: 630;
            font-size: vw(20);
            line-height: vh(27);
            display: flex;
            align-items: center;
            span {
              display: inline-block;
              margin-left: vw(2);
              margin-right: vw(2);
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(14);
              line-height: vh(16.84);
            }
            img {
              width: vw(9);
              height: vh(7);
            }
          }
        }
      }
      .bottom-box {
        width: vw(583);
        height: vh(139);
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        .text {
          width: vw(151);
          height: vh(134);
          background: url("../../../assets/images/dashboard/bottom-bg.png") no-repeat;
          background-size: 100% 100%;
          padding-top: vh(67);
          .name {
            width: vw(96);
            height: vh(34);
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(16);
            line-height: vh(22);
            margin: 0 auto;
            text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            text-align: center;
            color: rgba(228, 238, 255, 1)
          }
          .number {
            width: fit-content;
            height: vh(37);
            font-family: MiSans;
            font-weight: 630;
            font-size: vw(28);
            line-height: vh(37);
            margin: 0 auto;
            text-align: center;
            span {
              display: inline-block;
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(16);
              line-height: vh(30);
              vertical-align: bottom;
              margin-left: vw(4);
              background: linear-gradient(0deg, rgba(239, 248, 254, 0.75), rgba(239, 248, 254, 0.75)),
                linear-gradient(180deg, #EEF5FF 26.85%, #8AB6F8 72.22%);
              background-clip: text;
              color: transparent;
              text-shadow: none;
            }
          }
        }
      }
    }
    // 右边上下标题可以共用
    .top-title {
      height: vh(138);
      position: relative;
      bottom: vh(18);
      padding-left: vw(59);
      .icon1 {
        width: vw(120);
        height: vh(138);
        background: url("../../../assets/images/dashboard/right-top-icon1.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon2 {
        position: absolute;
        bottom: 0;
        left: vw(89);
        width: vw(424);
        height: vh(101);
        background: url("../../../assets/images/dashboard/right-top-icon2.png") no-repeat;
        background-size: 100% 100%;
        padding-top: vh(13.13);
        padding-left: vw(82);
        display: flex;
        .text-left {
          width: vw(152.71);
          height: vh(74);
          .name {
            font-family: YouSheBiaoTiHei;
            font-weight: 400;
            font-size: vw(20);
            line-height: vh(24);
            letter-spacing: 6%;
            background: linear-gradient(183.66deg, #D9E8FF 25.59%, #7FAEF2 87.32%);
            background-clip: text;
            color: transparent;
          }
          .number {
            display: flex;
            align-items: center;
            font-family: MiSans;
            font-weight: 630;
            font-size: vw(34.74);
            line-height: vh(46);
            span {
              display: inline-block;
              margin-right: vw(3.47);
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(17.37);
              line-height: vh(16.84);
              text-shadow: none;
            }
          }
        }
        .text-right {
          width: vw(156);
          height: vh(64);
          margin-left: vw(-4);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .text {
            width: vw(156);
            height: vh(27);
            display: flex;
            align-items: center;
            justify-content: space-between;
            .name {
              // width: vw(32);
              color: rgba(244, 246, 254, 0.75);
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(16);
              line-height: vh(22);
            }
            .number {
              font-family: MiSans;
              font-weight: 630;
              font-size: vw(20);
              line-height: vh(27);
              display: flex;
              align-items: center;
              span {
                display: inline-block;
                margin-left: vw(2);
                margin-right: vw(2);
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(14);
                line-height: vh(16.84);
                text-shadow: none;
              }
              img {
                width: vw(9);
                height: vh(7);
              }
            }
          }
        }
      }
    }
    .data-top {
      margin-top: vh(-2);
      margin-bottom: vh(11);
    }
    .right-circlIcon-data {
      width: vw(495);
      margin: 0 auto;
      margin-bottom: vh(15);
      .text {
        width: 100%;
        // height: vh();
        color: rgba(181, 211, 255, 1);
        .number {
          width: vw(100);
          height: vh(44);
          span {
            color: rgba(244, 246, 254, 1);
            font-size: 1.63vw;
            line-height: vh(44);
            text-shadow: 0px 2px 7px rgba(34, 141, 188, 0.8);
            margin-right: vw(4);
          }
        }
        .name {
          width: vw(64);
        }
      }
    }
  }
}
</style>
