<template>
  <div class="headerPart">
    <div class="option-btn">
      <div :class="['btn',isActive === 1? 'active' : 'unactive']" @click="tabSwitch(1)">首页</div>
      <div :class="['btn',isActive === 2? 'active' : 'unactive']" @click="tabSwitch(2)">网络覆盖</div>
      <div :class="['btn',isActive === 3? 'active' : 'unactive']" >
        <el-select v-model="checkMarket" placeholder="市场洞察" popper-class="modul-select" @change="tabSwitch(3)">
          <el-option
            v-for="item in market"
            :key="item.path"
            :label="item.name"
            :value="item.path">
          </el-option>
        </el-select>
      </div>
      <div :class="['btn',isActive === 4? 'active' : 'unactive']">
        <el-select v-model="checkOperation" placeholder="政企洞察" popper-class="modul-select" @change="tabSwitch(4)">
          <el-option
            v-for="item in operation"
            :key="item.path"
            :label="item.name"
            :value="item.path">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="header-time">
      <div class="area-select">
        <el-select v-model="selectForm.cityCode" :clearable="false" placeholder="省" popper-class="custom-select" size="mini" @change="onCityChange">
          <el-option v-for="item in selectAreaList.cityList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
        </el-select>
        <el-select v-model="selectForm.countyCode" :clearable="false" placeholder="区县" popper-class="custom-select" size="mini" @change="onCountyChange">
          <el-option v-for="item in selectAreaList.countyList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
        </el-select>
        <el-select v-model="selectForm.areaCode" :clearable="false" placeholder="网格" popper-class="custom-select" size="mini" @change="onAreaChange">
          <el-option v-for="item in selectAreaList.areaList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
        </el-select>
      </div>
      <!-- <div class="time-part">
        <div class="day">{{ dateDay }}</div>
        <div class="time">{{ dateTime }}</div>
      </div> -->
    </div>
  </div>
</template>
<script>
import service from '../../../api/dashbord'
import common from '@/api/common'
export default {
  name: 'HeaderPart',
  filters: {
  },
  data() {
    return {
      isActive: 1,
      dateDay: null,
      dateTime: null,
      selectForm: {
        cityCode: '',
        countyCode: '',
        areaCode: '',
      },
      checkMarket: '',
      checkOperation: '',
      market: [
        {
          name: '指标洞察',
          path: '/market/indicatorInsights',
        },
        {
          name: '任务调度',
          path: '/market/taskScheduling',
        },
         {
          name: '执行监控',
           path: '/market/executionMonitoring',
        },
      ],
      operation: [
        {
          name: '指标洞察',
          path: '/operation/indexInsight',
        },
        {
          name: '任务调度',
          path: '/operation/operationMap',
        },
        {
          name: '任务管理',
          path: '/operation/taskManagement',
        },
        {
          name: '执行监控',
          path: '/operation/taskAssessment',
        },
      ],
      selectAreaList: {
        cityList: [],
        countyList: [],
        areaList: []
      },
      dashbordData: {}
    }
  },
  computed: {
  },
  created() {
    this.getUserInfo()
  },
  async mounted() {
     // 首次进入获取地市的选择
    await this.getAreaByPid(999, 'cityList')
    this.timeFn()
    this.getUserCode()
  },
  methods: {
    timeFn() {
      this.timing = setInterval(() => {
        this.dateTime = new Date().Format('hh:mm:ss')
        this.dateDay = new Date().Format('yyyy/MM/dd')
      }, 1000)
    },
    //TODO: 地市、区县、网格编码信息尚未获取
     getUserInfo() {
        const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
        this.areaLevel = userInfo.roleType || 1
        console.log(userInfo)
    },
    async getUserCode() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      switch (this.areaLevel) {
        case 1:
          this.areaId = userInfo.provId //999
          break
        case 2:
          this.areaId = userInfo.cityId
          this.selectForm.cityCode = userInfo.cityId || '0871'
          this.onCityChange()
          break
        case 3:
          this.areaId = userInfo.countyId
          this.selectForm.cityCode = userInfo.cityId || '0871'
          this.selectForm.countyCode = userInfo.countyId || 'A0AN'
          await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
          break
        case 4:
          this.areaId = userInfo.areaId
          this.selectForm.cityCode = userInfo.cityId || '0871'
          this.selectForm.countyCode = userInfo.countyId || 'A0AN'
          this.selectForm.areaCode = userInfo.areaId || 'AN42'
          await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
          await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
      }
    },
    // 获取下拉框选择列表
    async getAreaByPid(code, listName) {
      await common.getAreaByPid({
        areaPid: code
      }).then(res => {
        if (res.code === 200) {
          this.selectAreaList[listName] = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async onCityChange() {
      this.areaLevel = 2
      this.selectForm.countyCode = ''
      this.selectForm.areaCode = ''
      this.selectAreaList.countyList = []
      this.selectAreaList.areaList = []
      await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
      this.$emit('updateData', this.selectForm)
    },
    async onCountyChange() {
      this.areaLevel = 3
      this.selectForm.areaCode = ''
      this.selectAreaList.areaList = []
      await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
    },
    async onAreaChange() {
      this.areaLevel = 4
    },
    tabSwitch(type) {
      console.log(type)
      this.isActive = type
      //页面跳转
      if (type === 2) {  //网络覆盖
        console.log('网络覆盖')
        // this.$router.push('/networkCoverage/wired')
         this.$router.push('/network')
      } else if (type === 3) {  //市场
        this.$router.push(this.checkMarket)
      } else if (type === 4) {  //政企
        this.$router.push(this.checkOperation)
      }
      this.$emit('changeTab', this.dashbordData);
    },

    //地区改变获得新的展示数据
    getHomeIndex() {
      const statDate = this.dateDay.Format('yyyy-MM-dd')
      service.getHomeIndex({
        cityCode: this.selectForm.cityCode,
        countyCode: this.selectForm.countyCode,
        areaCode: this.selectForm.areaCode,
        statDate: statDate
      }).then(res => {
        if(res.code === 200) {
          this.dashbordData = res.data
        } else {
          this.$message.error('数据获取失败')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
.headerPart {
  width: 100%;
  height: vh(86);
  line-height: vh(86);
  display: flex;
  justify-content: space-between;
  background: url("../../../assets/images/dashboard/header-background.png")
    no-repeat;
  background-size: 100% 100%;
  padding-left: vw(547);
}
.option-btn {
  width: vw(560);
  height: vh(32);
  // background-color: aqua;
  margin-top: vh(11);
  display: flex;
  .btn {
    width: vw(144);
    height: vh(34);
    font-family: PingFang SC;
    font-weight: 600;
    font-size: vw(16);
    line-height: vh(34);
    text-align: center;
    cursor: pointer;
    ::v-deep .el-select {
      .el-input__suffix {
        opacity: 0;
      }
      .el-input__inner {
        background-color: transparent;
        border: none;
        height: vh(34);
        font-size: vw(16);
        color: rgba(242, 248, 255, 1);
        padding: 0;
        text-align: center;
      }
    }
  }
  .active {
    color: rgba(242, 248, 255, 1);
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background: url("../../../assets/images/dashboard/active.png") no-repeat;
    background-size: 100% 100%;
  }
  .unactive {
    color: #c0c4cc;
    background: url("../../../assets/images/dashboard/unactive.png") no-repeat;
    background-size: 100% 100%;
  }
}
.header-time {
  display: flex;
  height: vh(36);
  align-items: center;
  margin-top: vh(5);
  .time-part {
    display: flex;
    align-items: baseline;
    color: #c3c7cb;
    width: vw(250);
    height: vh(37);
    .day {
      font-family: Milibus;
      font-size: vw(18.11);
      font-weight: 600;
      line-height: vh(37);
      letter-spacing: 0.03em;
    }
    .time {
      font-family: Milibus;
      font-size: vw(18.11);
      font-weight: 600;
      line-height: vh(37);
      letter-spacing: 0.03em;
      margin-left: vw(10);
    }
  }
}
.area-select {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: vw(482);
  height: vh(36);
  line-height: vh(36);
  margin-right: vw(38);
  ::v-deep .el-input__inner {
    width: vw(150);
    height: vh(36);
    line-height: vh(36);
    background-color: #141e32;
    border-color: #677aa5;
    border-radius: vw(4);
    font-size: vw(14);
    color: #ffffff !important;
  }
  // ::v-deep .el-input__suffix-inner .el-icon-arrow-up:before {
  //   // content: "\e78f";
  // }
  ::v-deep .el-select {
    font-size: vw(14);
  }
}
</style>
<style lang="scss">
@use "sass:math";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
.custom-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #141e32 !important;
    }
  }
  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    height: vh(34);
    line-height: vh(34);
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}
.modul-select {
  background-color: #1d4876 !important;
  border: none !important;
  // color: #ccd3d9;
  .el-select-dropdown__item {
    font-size: vw(16);
    color: white !important;
    height: vh(30) !important;
    line-height: vh(30);
  }
  .hover {
    background-color: #0f8bff !important;
  }
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #1d4876 !important;
    }
  }
}
</style>
