<template>
  <ScreenAdapter :h="screenHeight" :w="screenWidth">
    <HeaderPart @changeTab="changeTab" @updateData="updateData"/>
    <BodyContent :data="dashbordData"/>
    <BottomPart />
  </ScreenAdapter>
</template>
<script>
import BottomPart from './components/BottomPart.vue'
import HeaderPart from './components/HeaderPart'
import ScreenAdapter from './components/ScreenAdapter.vue'
import BodyContent from './components/BodyContent.vue'
export default {
  name: 'Dashboard',
  components: {
    ScreenAdapter,
    HeaderPart,
    BottomPart,
    BodyContent
  },
  data() {
    return {
      screenWidth: 1920, // 默认值
      screenHeight: 1080, // 默认值
      dashbordData: {}
    }
  },
  mounted() {
    this.updateScreenSize()
    this.onresize = this.debounce(() => this.updateScreenSize(), 100)
    window.addEventListener('resize', this.onresize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onresize)
  },
  methods: {
    debounce(fn, delay = 500) {
      let timer
      return function (...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          timer = null
          fn.apply(this, args)
        }, delay)
      }
    },
    updateScreenSize() {
      this.screenWidth = window.innerWidth
      this.screenHeight = window.innerHeight
    },
    changeTab(tab) {
      console.log('changeTab')
    },
    updateData(data) {
      console.log(data)
      this.dashbordData = data
    },
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "./icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
</style>
