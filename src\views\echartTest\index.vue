<template>
    <div class="chart-container">
      <h2>图表组件使用示例</h2>
      
      <!-- 混合图表示例 -->
      <div class="chart-section">
        <h3>混合图表（柱状图 + 折线图）</h3>
        <MixedChart
          :width="'100%'"
          :height="'400px'"
          :xAxisData="mixedChartData.xAxis"
          :barData="mixedChartData.bars"
          :lineData="mixedChartData.lines"
          :yAxisConfig="mixedChartData.yAxisConfig"
          :showLegend="true"
          barWidth="20%"
        />
      </div>
  
      <!-- 堆叠柱状图示例 -->
      <div class="chart-section">
        <h3>堆叠柱状图</h3>
        <StackedBarChart
          :width="'100%'"
          :height="'400px'"
          :xAxisData="stackedChartData.xAxis"
          :seriesData="stackedChartData.series"
          :yAxisConfig="stackedChartData.yAxisConfig"
          :showLegend="true"
          barWidth="40%"
          stackName="total"
        />
      </div>
  
      <!-- 数据更新示例 -->
      <div class="controls">
        <button @click="updateData">更新数据</button>
        <button @click="resetData">重置数据</button>
      </div>
    </div>
  </template>
  
  <script>
  // 引入组件
  import MixedChart from '@/components/charts/MixedChart.vue'
  import StackedBarChart from '@/components/charts/StackedBarChart.vue'
  
  export default {
    name: 'ChartExample',
    components: {
      MixedChart,
      StackedBarChart
    },
    data() {
      return {
        // 混合图表数据
        mixedChartData: {
          xAxis: ['一季度', '二季度', '三季度', '四季度', '年度', '总计'],
          bars: [
            {
              name: '高市占点比',
              data: [3, 5.5, 6, 5, 6, 7],
              color: ['#85D9E4', '#6AC0D0', '#4FA7BC']
            },
            {
              name: '低市占点比', 
              data: [2, 4, 4.5, 3.5, 4, 5],
              color: ['#418AF4', '#3678D8', '#2B66BC']
            }
          ],
          lines: [
            {
              name: '高市占数量',
              data: [40, 20, 45, 60, 65, 70],
              color: '#EDBE16'
            },
            {
              name: '低市占数量',
              data: [45, 15, 40, 55, 58, 75], 
              color: '#E3925F'
            }
          ],
          yAxisConfig: {
            left: {
              name: '单位：万',
              min: 0,
              max: 'dataMax'
            },
            right: {
              name: '',
              min: 0,
              max: 100,
              formatter: '{value}%'
            }
          }
        },
        
        // 堆叠柱状图数据
        stackedChartData: {
          xAxis: ['地区A', '地区B', '地区C', '地区D', '地区E'],
          series: [
            {
              name: '移动',
              data: [120, 100, 90, 110, 95],
              color: ['#ECBD15', '#E0A812', '#D4930F']
            },
            {
              name: '电信',
              data: [30, 35, 25, 30, 28],
              color: ['#7AC7D7', '#6AB5C5', '#5AA3B3']
            },
            {
              name: '联通',
              data: [25, 20, 18, 22, 20],
              color: ['#428AF4', '#3778E0', '#2C66CC']
            },
            {
              name: '广电',
              data: [15, 12, 10, 13, 11],
              color: ['#53BFEF', '#47ABDB', '#3B97C7']
            },
            {
              name: '其他',
              data: [10, 8, 7, 9, 8],
              color: ['#9E9E9E', '#8A8A8A', '#767676']
            }
          ],
          yAxisConfig: {
            name: '数量',
            min: 0,
            max: 250
          }
        }
      }
    },
    methods: {
      // 更新数据示例
      updateData() {
        // 更新混合图表数据
        this.mixedChartData.bars[0].data = this.generateRandomData(6, 1, 10)
        this.mixedChartData.bars[1].data = this.generateRandomData(6, 1, 8)
        this.mixedChartData.lines[0].data = this.generateRandomData(6, 10, 80)
        this.mixedChartData.lines[1].data = this.generateRandomData(6, 10, 80)
        
        // 更新堆叠图表数据
        this.stackedChartData.series.forEach(series => {
          series.data = this.generateRandomData(5, 5, series.name === '移动' ? 120 : 40)
        })
      },
      
      // 重置数据
      resetData() {
        // 重置为初始数据
        this.mixedChartData.bars[0].data = [3, 5.5, 6, 5, 6, 7]
        this.mixedChartData.bars[1].data = [2, 4, 4.5, 3.5, 4, 5]
        this.mixedChartData.lines[0].data = [40, 20, 45, 60, 65, 70]
        this.mixedChartData.lines[1].data = [45, 15, 40, 55, 58, 75]
        
        this.stackedChartData.series[0].data = [120, 100, 90, 110, 95]
        this.stackedChartData.series[1].data = [30, 35, 25, 30, 28]
        this.stackedChartData.series[2].data = [25, 20, 18, 22, 20]
        this.stackedChartData.series[3].data = [15, 12, 10, 13, 11]
        this.stackedChartData.series[4].data = [10, 8, 7, 9, 8]
      },
      
      // 生成随机数据
      generateRandomData(length, min, max) {
        return Array.from({ length }, () => 
          Math.floor(Math.random() * (max - min + 1)) + min
        )
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .chart-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .chart-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: #333;
      font-size: 18px;
    }
  }
  
  .controls {
    text-align: center;
    margin-top: 20px;
    
    button {
      margin: 0 10px;
      padding: 10px 20px;
      background: #409eff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
      
      &:hover {
        background: #337ecc;
      }
      
      &:active {
        transform: translateY(1px);
      }
    }
  }
  
  h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
  }
  </style>