<template>
  <div class="echartsMap">
    <div id="echart-map-wrap" />
  </div>
</template>
<script>
import { fitChartSize } from '@/utils/dataUtil.js'
import * as echarts from 'echarts'
import common from '@/api/common'
import market from '@/api/market'
export default {
  name: 'EchartsMap',
  props: {
    strategyBatchList: {
      type: Array,
      default: () => []
    },
    selectForm: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      mapChart: null,
      map: null,
      mapJson: {}, // 地图边界值
      mapBorderJson: {}, // 地图白色边框以及地图底层边界值
      mapData: {}, // 地图悬浮数据
      geoCoordMap: {}, // 悬浮数据中心点
      currentArea: {},
      customerNumberData: [],
      areaId: '999',
      areaLevel: 2, 
      countMap: [],
      customerTotalCount: 0,
    }
  },
  watch: {
    customerTotalCount: {
      handler(val) {
        console.log(val,999)
       this.$emit('getTotalCount', this.customerTotalCount)
      },
      deep: true,
    }
  },
  async mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.mapChart) {
      this.mapChart.dispose()
    }
  },
  methods: {
    //获取地图数据信息
    async calculateCustomerGroup(t) {  //获取昆明下属区县的数据level=2
      await market.calculateCustomerGroup({
        cityCode: this.selectForm.cityCode || '',
        countyCode: this.selectForm.countyCode || '',
        areaCode: this.selectForm.areacode || '',
        villageCode: this.selectForm.villageCode || '',
        level: this.areaLevel-1 || 0, //与全局地图层级-1使用
        pageNum: 1,
        pageSize: 10,
        strategyBatchList: this.strategyBatchList,
        calculateType: 0
      }).then(res => {
        if (res.code === 200) {
          this.customerNumberData = res.data.regionStatistics
          this.countMap = this.customerNumberData.reduce((map, item) => {
            // 假设customerNumberData中的每个项有regionCode和count属性
            map[item.regionCode] = item.count;
            return map;
          }, {});
          this.customerTotalCount = res.data.totalCount
          if (t===1) {
            this.mapData = this.mapData.map(item => {
              return {
                ...item, 
                value: this.countMap[item.areaId] ?? 0 
              };
            });
            this.$nextTick(() => {
              this.initMapEcharts(this.mapData)
            })
          }
        } else (
          this.$message.error(res.msg)
        )
      })
    },
    //获取区域边界 获取云南省0，1
    async getShengArea() {
      await common.getDimAreaByPid({
        areaPid: 0,
        areaLevel: 1
      }).then(async (code, data) => {
        if (code === 200) {
          this.mapBorderJson = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {
                  name: data[0].area_name
                },
                geometry: {
                  type: 'MultiPolygon',
                  coordinates: this.polygonPath(data[0].area_location)
                }
              }
            ]
          }
        }
      })
    },
    //获取地图边界
    async getDimAreaByPid(params) {
      this.areaLevel = params.areaLevel
      await this.calculateCustomerGroup()
      await common.getDimAreaByPid({
        areaPid: params.areaId,
        areaLevel: params.areaLevel
      }).then(async ({ code, data }) => {
        if (code === 200) {
          this.mapJson = {
            type: 'FeatureCollection',
            features: this.polygonFullPath(data)
          }
          this.geoCoordMap = this.getGeoCoordMap(data)
          this.mapData = data.map(item => {
            return {
              name: item.area_name,
              areaId: item.area_id,
              areaLevel: params.areaLevel+1,
              centralPoint: item.central_point,
              value: this.countMap[item.area_id] ?? 50
            }
          })
          this.$nextTick(() => {
            this.initMapEcharts(this.mapData)
          })
        }
      })
    },
    /**
     * @description 格式化白色边框边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonPath(data) {
      const points = data.split(';')
      const polygon = points.map(item => {
        const [lon, lat] = item.split(',')
        return [parseFloat(lon.trim()), parseFloat(lat.trim())]
      })
      return [[polygon]]
    },
    /**
     * @description 格式化边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonFullPath(data) {
      return data.map(item => {
        return {
          type: 'Feature',
          properties: {
            name: item.area_name
          },
          geometry: {
            type: 'MultiPolygon',
            coordinates: this.polygonPath(item.area_location)
          }
        }
      })
    },
    /**
     * @description 获取悬浮中心点
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    getGeoCoordMap(arr) {
      const result = {}
      arr.forEach(item => {
        const [longitude, latitude] = item.central_point.split(',').map(parseFloat)
        result[`${item.area_name}`] = [longitude, latitude]
      })
      return result
    },
    scatterData2() {
      return datas.map((item) => ({
        name: item.name,
        value: this.geoCoordMap[item.name]
      }))
    },
    async initMapEcharts(datas) {
      if (this.mapChart) {
        this.mapChart.dispose()
      }
      this.mapChart = echarts.init(document.getElementById('echart-map-wrap'))
      echarts.registerMap('map', this.mapJson)
      echarts.registerMap('mapBorder', this.mapBorderJson)
      this.mapChart.off('click')
      const option = this.getOption(datas)
      this.mapChart.setOption(option)
      this.mapChart.on('click', (params) => {
        console.log(params.data)
        // if (params.data.value <= 0) return
        if (params.data.areaLevel < 6) { //行政村边界为6，继续加载边界无数据
          this.$emit('updataCode', params.data) //6是行政村，5是网格，4是区县，3是地市
          this.getDimAreaByPid(params.data)
        } else {
          this.currentArea = params.data
        }
      })
    },
    getOption(datas) {
      var that = this
      console.log(datas)
      function scatterData2() {
        return datas.map((item) => ({
          name: item.name,
          areaId: item.areaId,
          value: that.geoCoordMap[item.name]
        }))
      }
      return {
        geo: [
          // 第一层地图
          {
            map: 'map',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              borderColor: '#D8E6FF4D',
              borderWidth: 1
            },
            emphasis: {
              itemStyle: {
                areaColor: 'transparent'
              },
              label: {
                show: 0,
                color: '#fff'
              }
            },
            zlevel: 3
          },
          // 第二层地图和第一层重叠用作边框
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#F7F8FF',
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.25)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: fitChartSize(3.72)
            },
            zlevel: 2,
            silent: true
          },
          // 第三层地图底层
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '52%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#B6C1FB99',
              borderWidth: 1
            },
            zlevel: 1,
            silent: true
          }
        ],
        visualMap: {
          show: false,
          type: 'piecewise',
          bottom: fitChartSize(40),
          right: fitChartSize(40),
          itemGap: fitChartSize(10),
          align: 'left',
          itemWidth: fitChartSize(16),
          itemHeight: fitChartSize(12),
          textStyle: {
            fontSize: fitChartSize(14),
            color: '#EDEDED'
          },
          seriesIndex: 0,
          pieces: [
            {
              gt: 1000,
              lte: 99999,
              color: '#A9FFDA80',
              label: '<250'
            },
            {
              gt: 500,
              lte: 1000,
              color: '#37D89280',
              label: '250-500'
            },
            {
              gt: 250,
              lte: 500,
              color: '#FF838380',
              label: '500-1000'
            },
            {
              gt: 0,
              lte: 250,
              color: '#FF393980',
              label: '>1000'
            }
          ]
        },
        series: [
          {
            type: 'map',
            selectedMode: false,
            map: 'map',
            geoIndex: 0,
            data: datas,
          },
          // 地市弹框撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: '{b}',
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#fff',
              fontSize: fitChartSize(20),
              fontWeight: '500',
              fontFamily: '',
              show: true
            },
            symbol: 'rect',
            // symbolSize: function(data, params) {
            //   const name = params.data.name
            //   const nameLength = name.length || 0
            //   const width = fitChartSize(nameLength * 20)
            //   const height = fitChartSize(30)
            //   return [width, height]
            // },
            symbolOffset: [0, fitChartSize(-30)],
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              borderWidth: fitChartSize(0.5),
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
          // 地市对应值撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: function (name) {
                const item = datas.find(item => item.name === name.name)
                return item ? item.value : ''
              },
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#ffffff',
              fontSize: fitChartSize(23),
              fontFamily: 'Milibus',
              show: true
            },
            symbol: 'rect',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
        ]
      }
    },
    handleResize() {
      if (this.mapChart) {
        this.mapChart.resize()
        this.$nextTick(() => {
          const updatedOption = this.getOption(this.mapData)
          this.mapChart.setOption(updatedOption)
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "~@/views/market/index.scss";
#echart-map-wrap {
  margin-top: vh(52);
  margin-left: vw(523);
  width: vw(865);
  height: vh(813);
}
</style>
