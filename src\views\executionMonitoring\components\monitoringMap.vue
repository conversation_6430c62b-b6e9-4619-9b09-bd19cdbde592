<template>
  <div class="customerHeatMap">
    <EchartsMap ref="echartsMap" />
    <div class="echarts-legend">
      <div class="legend-title">预约量</div>
      <div v-for="item, index in legendList" :key="index" class="legend-icon">
        <div :style="{ backgroundColor: item.color, borderColor: item.borderColor }" class="legend-icon-box" />
        <div>{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import EchartsMap from './EchartsMap.vue'
import market from '@/api/market'
import common from '@/api/common'
export default {
  name: 'CustomerHeatMap',
  components: {
    EchartsMap,
  },
  data() {
    return {
      activeName: 1,
      searchKey: '',
      isstrategy: true,
      dialogVisible: true,
      currentStep: 0,
      currentData: [],
      checkIdData: [],
      checkList: [],
      page: {
        currentPage: 1,
        total: '6',
        pageSize: 6,
      },
      selectForm: {
        cityCode: '',
        countyCode: '',
        areacode: '',
        villageCode: '',
        streetCode: ''
      },
      selectAreaList: {
        cityList: [],
        countyList: [],
        areaList: [],
        villageList: [],
        streetList: []
      },
      flag: true,
      legendList: [
        { color: '#FF393980', borderColor: '#FF8787', text: '<250' },
        { color: '#FF838380', borderColor: '#FFBCB9', text: '250-500' },
        { color: '#37D89280', borderColor: '#A9FFDA', text: '500-1000' },
        { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '>1000' }
      ],
      customerTotalCount: 0
    }
  },
  async mounted() {
    //首次进入获取地市的选择
    // await this.getAreaByPid(999, 'cityList')
    // //第一次获取策略清单列表，要默认添加
    // await this.getStrategyList()
    // //首次进入页面加载地图子组件边界
    // await this.$refs.echartsMap.getShengArea() //获取云南省边界
    // await this.$refs.echartsMap.getDimAreaByPid({ //获取地市边界
    //     areaId: 999,
    //     areaLevel: 2
    // })
  },
  methods: {
    //从子组件获取customerTotalCount传给创建任务弹窗
    getTotalCount(data) {
      console.log('接收子组件数据：', data);
      this.customerTotalCount = data;
    },
    updataCode(data) {
      if (data.areaLevel === 3) {
        this.selectForm.cityCode = data.areaId
      } else if (data.areaLevel === 4) {
        this.selectForm.countyCode = data.areaId
      } else if (data.areaLevel === 5) {
        this.selectForm.areacode = data.areaId
      }
    },
    async onCityChange() {
      this.selectForm.countyCode = ''
      this.selectForm.areacode = ''
      this.selectForm.villageCode = ''
      this.selectForm.streetCode = ''
      this.selectAreaList.countyList = []
      this.selectAreaList.areaList = []
      this.selectAreaList.villageList = []
      this.selectAreaList.streetList = []
      await this.$refs.echartsMap.getDimAreaByPid({ //获取昆明区县边界
        areaId: this.selectForm.cityCode,
        areaLevel: 3
      });
      await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
    },
    async onCountyChange() {
      this.selectForm.areacode = ''
      this.selectForm.villageCode = ''
      this.selectForm.streetCode = ''
      this.selectAreaList.areaList = []
      this.selectAreaList.villageList = []
      this.selectAreaList.streetList = []
      await this.$refs.echartsMap.getDimAreaByPid({ //获取网格边界
        areaId: this.selectForm.countyCode,
        areaLevel: 4
      });
      await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
    },
    async onAreaChange() {
      this.selectForm.villageCode = ''
      this.selectForm.streetCode = ''
      this.selectAreaList.villageList = []
      this.selectAreaList.streetList = []
      await this.$refs.echartsMap.getDimAreaByPid({ //获取行政村边界
        areaId: this.selectForm.areacode,
        areaLevel: 5
      });
      await this.getAreaByPid(this.selectForm.areacode, 'villageList')
    },
    async onVillageChange() {
      this.selectForm.streetCode = ''
      this.selectAreaList.streetList = []
      // await this.$refs.echartsMap.getDimAreaByPid({ //只获取客群信息展示 level>6
      //     areaId: this.selectForm.villageCode,
      //     areaLevel: 6
      // });
      // await this.getAreaByPid(this.selectForm.villageCode, 'streetList')
    },
    async onStreetChange() {
      //    await this.$refs.echartsMap.getDimAreaByPid({ //只获取客群信息展示
      //         areaId: this.selectForm.villageCode,
      //         areaLevel: 7
      //     });
    },
    // 获取下拉框选择列表
    async getAreaByPid(code, listName) {
      await common.getAreaByPid({
        areaPid: code
      }).then(res => {
        if (res.code === 200) {
          this.selectAreaList[listName] = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //获取策略清单列表
    async getStrategyList() {
      await market.getStrategyList({
        cityCode: this.selectForm.cityCode || '',
        countyCode: this.selectForm.countyCode || '',
        areaCode: this.selectForm.areacode || '',
        villageCode: this.selectForm.villageCode || '',
        level: 0,
        pageNum: this.page.currentPage,
        pageSize: this.page.pageSize,
        strategyName: this.searchKey
      }).then((res) => {
        if (res.code === 200) {
          this.currentData = res.data.list
          if (this.flag) {
            this.checkIdData = [this.currentData[0].strategyId];
            this.checkList = [this.currentData[0]];
            this.page.total = (Math.ceil(res.data.total / 6));
            this.flag = false
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 上一页
    prvPage() {
      // 只有当前页大于1时才执行翻页操作
      if (this.page.currentPage > 1) {
        this.page.currentPage--;
        this.getStrategyList();
      }
    },
    // 下一页
    nextPage() {
      // 只有当前页小于总页数时才执行翻页操作
      if (this.page.currentPage < this.page.total) {
        this.page.currentPage++;
        this.getStrategyList();
      }
    },
    //下一步按钮
    creatTask() {
      this.$emit('creatTask', {
        checkList: this.checkList,
        totalCount: this.customerTotalCount
      })
    },
    // 搜索策略清单
    async search() {
      console.log(this.searchKey)
      await this.getStrategyList()
    },
    // 清单列表改变时加载地图数据
    async handleChange(item) {
      console.log(item)
      const index = this.checkList.findIndex(i => i.strategyId === item.strategyId);
      if (index === -1) {
        // 不存在则添加到列表
        this.checkList.push(item);
      } else {
        // 存在则从列表中删除
        this.checkList.splice(index, 1);
      }
      console.log(this.checkIdData)
      console.log(this.checkList)
      const strategyBatchList = this.checkList.map(item => {
        // 只保留 id、name、number 三个属性，自动过滤其他属性
        return {
          strategyId: item.strategyId,
          customerGroupId: item.customerGroupId,
          batch: item.batch
        };
      });
      await this.$refs.echartsMap.calculateCustomerGroup(1)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";

.customerHeatMap {
  .echarts-legend {
    position: fixed;
    left: vw(77);
    bottom: vh(80);

    .legend-title {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      color: #ffffff;
    }

    .legend-icon {
      color: #ffffff;
      display: flex;
      align-items: center;
      margin-top: 10px;

      >div {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);

        &:first-child {
          margin-right: vw(12);
          width: vw(13);
          height: vw(13);
          border: vw(2) solid;
        }
      }
    }
  }
}
</style>