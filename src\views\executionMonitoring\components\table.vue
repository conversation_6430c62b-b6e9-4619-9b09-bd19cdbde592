<template>
    <div class="table-box">
        <div class="select-part">
            <div class="search">
                <div>
                    <span>搜索</span>
                    <el-input placeholder="请输入" v-model="searchText" class="search-input"
                        @keyup.enter.native="searchInfo">
                        <i slot="prefix" class="el-input__icon"></i>
                    </el-input>
                </div>
                <div>
                    <span>任务状态</span>
                    <el-select v-model="taskStatus" :clearable="false" placeholder="全部状态" popper-class="custom-select"
                        size="mini" @change="searchInfo">
                        <el-option v-for="item in taskStatusList" :key="item.value" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </div>
                <div>
                    <span>任务层级</span>
                    <el-select v-model="taskLevel" :clearable="false" placeholder="任务层级" popper-class="custom-select"
                        size="mini" @change="searchInfo">
                        <el-option v-for="item in taskLevelList" :key="item.value" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </div>
            </div>
            <!-- 暂时隐藏 -->
            <div class="btn-box">
                <div class="rest common-btn" @click="reset">
                    <div>重置</div>
                </div>
                <!-- <div class="transfer common-btn" >
                    <div>导出</div>
                </div>
                <div class="transfer common-btn" >
                    <div>筛选</div>
                </div> -->
            </div>
        </div>
        <div class="table">
            <div class="content">
                <div class="el-table">
                    <el-table
                        ref="tableRef"
                        :data="currentData" :cell-style="cellStyle" :header-cell-style="headerStyle"
                        :cell-class-name="cellClassName" :header-cell-class-name="headerCellClassName"
                        :fit="true">
                        <!-- <el-table-column type="selection" width="55"></el-table-column> -->
                        <el-table-column v-for="column in columns" :key="column.prop" :prop="column.prop"
                            :label="column.label">
                            <template slot-scope="scope">
                                <span v-if="column.prop === 'taskLevel'">
                                    <span class="custom-tag" :style="{
                                        backgroundColor: getIconColor(scope.row.taskLevel),
                                        color: getLevelTextColor(scope.row.taskLevel)
                                    }">
                                        {{ getLevelText(scope.row[column.prop]) }}
                                    </span>
                                </span>
                                <span v-else-if="column.prop === 'taskStatus'">
                                    <span class="status-tag"
                                        :style="{ backgroundColor: getBgColor(scope.row.taskStatus) }">
                                        {{ getStatusText(scope.row[column.prop]) }}
                                    </span>
                                </span>
                                <span v-else>{{ scope.row[column.prop] }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="170">
                            <template slot-scope="scope">
                                <!-- 查看操作要区分 -->
                                <el-button @click="viewDetail(scope.row)" type="text" size="medium">
                                    查看详情
                                </el-button>
                                <!-- 暂时隐藏 -->
                                <!-- <el-button @click="distribute" type="text" size="medium">
                                    暂停
                                </el-button>
                                <el-button @click="termination" type="text" size="medium">
                                    终止
                                </el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination popper-class="select-dropdown" background @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" :current-page.sync="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]" :page-size="page.pageSize" :total="page.total"
                        layout="sizes, prev, pager, next, jumper">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import market from '@/api/market';
export default {
    name: "Table",
    props: {
        menuActive: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            page: { pageSize: 10, currentPage: 1, total: 100 },
            tableData: [],
            currentData: [],
            columns: [
                // { prop: 'taskId', label: '任务编码' },
                { prop: 'taskName', label: '任务名称' },
                { prop: 'createUserName', label: '创建人' },
                 { prop: 'taskLevel', label: '任务层级' },
                { prop: 'strategyName', label: 'IOP策略' },
                { prop: 'areaName', label: '客群区域' },
                { prop: 'customerTotalCount', label: '客群总量' },
                // { prop: 'createTime', label: '创建时间' },
                { prop: 'taskStartTime', label: '开始时间' },
                { prop: 'taskEndTime', label: '结束时间' },
                { prop: 'taskStatus', label: '状态' }
            ],
            searchText: '',
            taskStatus: '',
            taskLevel: '',
            taskLevelList: [
                {
                    value: '2',
                    name: '地市'
                },
                {
                    value: '3',
                    name: '区县'
                },
                {
                    value: '4',
                    name: '网格'
                },
                {
                    value: '5',
                    name: '行政村'
                }
            ],
            taskStatusList: [
                {
                    value: '1',
                    name: '生成中'
                },
                {
                    value: '2',
                    name: '执行中'
                },
                {
                    value: '3',
                    name: '已结束'
                },
                {
                    value: '4',
                    name: '暂停'
                },
            ],
        }
    },
    mounted() {
        this.getMonitorTaskInfo()
    },
    methods: {
        dynamicCalculationWidth(prop, tableData, title, num = 0) {
            console.log(prop)
            if (tableData.length === 0) {
                // 表格没数据不做处理
                return
            }
            let flexWidth = 0 // 初始化表格列宽
            let columnContent = '' // 占位最宽的内容
            let canvas = document.createElement('canvas')
            let context = canvas.getContext('2d')
            const fontsize = 16/1920 * window.innerWidth
            context.font = `${fontsize}px PingFang SC`
            if (prop === '' && title) {
                // 标题长内容少的，取标题的值,
                columnContent = title
            } else {
                // 获取该列中占位最宽的内容
                let index = 0
                for (let i = 0; i < tableData.length; i++) {
                    const now_temp = tableData[i][prop] + ''
                    const max_temp = tableData[index][prop] + ''
                    const now_temp_w = context.measureText(now_temp).width
                    const max_temp_w = context.measureText(max_temp).width
                    if (now_temp_w > max_temp_w) {
                        index = i
                    }
                }
                columnContent = tableData[index][prop]
                // 比较占位最宽的值跟标题、标题为空的留出四个位置
                const column_w = context.measureText(columnContent).width
                const title_w = context.measureText(title).width
                if (column_w < title_w) {
                    columnContent = title || '占位符呀'
                }
            }
            // 计算最宽内容的列宽
            let width = context.measureText(columnContent)
            flexWidth = width.width + 40 + num
            return flexWidth + 'px'
        },
        getMonitorTaskInfo() {
            market.getTaskInfo({
                searchText: this.searchText,
                taskStatus: this.taskStatus,
                taskLevel: this.taskLevel,
                pageNum: this.page.currentPage,
                pageSize: this.page.pageSize
            }).then(res => {
                if (res.code === 200) {
                    this.page.total = res.data.total;
                    this.tableData = res.data.list;
                    // this.tableData.forEach(item => {
                    //     item.taskEndTime = new Date(item.taskEndTime).toLocaleString()
                    //     item.taskStartTime = new Date(item.taskStartTime).toLocaleString()
                    //     item.createTime = new Date(item.createTime).toLocaleString()
                    // })
                    // this.tableData.sort((a, b) => {
                    //     // 注意：这里使用原始时间戳比较，而非格式化后的字符串
                    //     return new Date(b.createTime) - new Date(a.createTime);
                    // });
                    this.currentData = this.currentChangePage(this.page.pageSize, this.page.currentPage)
                }
            })
        },
        //搜索
        searchInfo() {
            this.getMonitorTaskInfo()
        },
        getIconColor(taskLevel) {
            switch (taskLevel) {
                case '2':
                    return '#59a5df';
                case '3':
                    return '#d4c8e8';
                case '4':
                    return '#eee1d3';
                case '5':
                    return '#d1eeef';
            }
        },
        getLevelTextColor(taskLevel) {
            switch (taskLevel) {
                case '2':
                    return '#f6f6f6';
                case '3':
                    return '#893fff';
                case '4':
                    return '#d47312';
                case '5':
                    return '#15adb3';
            }
        },
        getLevelText(taskLevel) {
            switch (taskLevel) {
                case '2':
                    return '地市';
                case '3':
                    return '区县';
                case '4':
                    return '网格';
                case '5':
                    return '行政村';
            }
        },
        //列表回显方法
        getBgColor(status) {
            // 根据状态返回对应圆点颜色
            switch (status) {
                case '1':
                    return '#409eff'; // 转派中橙色
                case '2':
                    return '#67c23a'; // 执行中青色
                case '3':
                    return '#909399'; // 已完成浅灰色
                case '4':
                    return '#f56c6c';
            }
        },
        getStatusText(status) {
            // 根据状态返回对应文本
            switch (status) {
                case '1':
                    return '生成中';
                case '2':
                    return '执行中';
                case '3':
                    return '已结束';
                case '4':
                    return '暂停';
            }
        },
        //前端表格分页功能
        handleSizeChange(val) {
            this.page.pageSize = val
            this.getMonitorTaskInfo()
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`, this.page.currentPage)
            this.getMonitorTaskInfo()
            // this.currentData = this.currentChangePage(this.page.pageSize, val);
        },
        currentChangePage(pageSize, currentPage) {
            const currentData = [];
            let array = JSON.parse(JSON.stringify(this.tableData));
            array.forEach((item, index) => {
                if (pageSize * (1 - 1) <= index && index <= pageSize * 1 - 1) {
                    currentData.push(item);
                }
            });
            return currentData;
        },
        //表格样式
        cellClassName() {
            return 'cell-style'
        },
        headerCellClassName() {
            return 'header-common';
        },
        // 表头样式
        headerStyle() {
            return {
                'white-space': 'nowrap',  // 表头不换行
                'padding': '0 12px'       // 调整内边距
            };
        },
        cellStyle({ row, rowIndex }) {
            return {
                backgroundColor: rowIndex % 2 !== 0 ? 'rgba(36, 63, 93, 1)' : 'rgba(32, 66, 105, 1)',
                // 'white-space': 'nowrap',  // 内容不换行
                'padding': '0 12px'       // 调整内边距
            };
        },
        //操作功能
        viewDetail(row) {
            this.$router.push({
                path: '/market/executionMonitoring/detail',
                query: {
                    taskId: row.taskId,
                    taskName: row.taskName
                }
            })
        },
        pause() {
            console.log('暂停')
        },
        termination() {
            console.log('终止')
        },
        reset() {
            this.searchText = '';
            this.taskStatus = '';
            this.taskLevel = '';
            this.getMonitorTaskInfo()
        }
    }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.table-box {
    width: 100%;
    margin: 0 auto;
    height: 100%;
    // background-color: #8cb2ff;
    padding-top: vh(20);
    .select-part {
        width: vw(1840);
        height: vh(32);
        // background-color: rgb(225, 39, 39);
        margin: 0 auto;
        margin-bottom: vh(20);
        display: flex;
        justify-content: space-between;
        .search {
            width: vw(700);
            display: flex;
            justify-content: space-between;
            align-items: center;
            // margin-top: vh(20);
            color: white;
            span {
                font-size: vw(16);
                margin-right: vw(6);
            }
            .search-input {
                width: vw(220) !important;
                ::v-deep .el-input__inner {
                    width: vw(220);
                    height: vh(32) !important;
                    background-color: #1d334b !important;
                    // padding-left: vw(60);
                    color: white;
                }
                ::v-deep .el-input__icon {
                    display: inline-block;
                    width: vw(32);
                    height: vh(32);
                    background: url("../../../assets/images/market/search.png") no-repeat;
                    background-size: 100% 100%;
                    cursor: pointer;
                }
            }
            ::v-deep .el-input__inner {
                width: vw(130);
                height: vh(32);
                line-height: vh(32);
                background-color: #1d334b !important;
                border-color: #677aa5;
                border-radius: vw(4);
                font-size: vw(14);
                color: #ffffff !important;
            }
            ::v-deep .el-select {
                font-size: vw(14);
            }
            ::v-deep .el-input__icon {
                line-height: vh(32) !important;
            }
        }
        .btn-box {
            width: vw(320);
            height: vh(32);
            display: flex;
            justify-content: space-between;
            align-items: center;
            .common-btn {
                border-radius: vw(2);
                color: rgba(228, 238, 255, 1);
                box-shadow: 0px 0px 20px 0px rgba(143, 181, 255, 0.64) inset;
                background: linear-gradient(0deg, #DEF1FE 0%, rgba(222, 241, 254, 0) 100%);
                padding: vw(2);
                div {
                    width: 100%;
                    height: vh(28);
                    cursor: pointer;
                    text-align: center;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: vw(16);
                    line-height: vh(28);
                    background: linear-gradient(181.96deg, #6498FF -38.96%, #1D4BA7 39.59%, #142D60 98.35%);
                }
            }
            .rest {
                width: vw(80);
                height: vh(32);
            }
            .transfer {
                width: vw(110);
                height: vh(32);
            }
        }
    }
    .table {
        width: vw(1840);
        height: vh(840);
        margin: 0 auto;
        padding: 2px;
        background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));
        border-radius: vw(10);
        .content {
            /* 内层div作为内容容器 */
            padding: vh(20) vw(20);
            border-radius: vw(8);
            /* 比外层小2px，避免露出底色 */
            background: #1a3059;
            box-sizing: border-box;
            .el-table::before {
                height: 0;
            }
            .el-table {
                width: vw(1840);
                height: vh(800);
                border-radius: vw(10);
                background-color: transparent;
                .custom-tag {
                    display: inline-block;
                    width: fit-content;
                    height: fit-content;
                    padding: vh(2) vw(8);
                    border-radius: vw(22);
                    color: #fff;
                    /* 文字颜色为白色 */
                    font-size: vw(18);
                    border: none;
                    line-height: vh(20);
                }
                .status-tag {
                    display: inline-block;
                    width: fit-content;
                    height: fit-content;
                    padding: vh(2) vw(8);
                    border-radius: vw(4);
                    font-size: vw(18);
                    color: #fff;
                    line-height: vh(24);
                }
                ::v-deep .el-table {
                    width: vw(1800);
                    height: vh(736);
                    border-radius: vw(10);
                    overflow: auto;
                    border: 0;
                    background-color: transparent;
                    .header-common {
                        width: vw(198);
                        height: vh(48);
                        background-color: #265282 !important;
                        /* 强制生效 */
                        color: rgba(228, 238, 255, 1) !important;
                        border: vw(1) solid rgba(230, 235, 240, 0.2);
                        border-top: 0;
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: vw(18);
                        line-height: vh(22);
                        padding: 0 !important;
                    }
                    .cell-style {
                        // width: vw(198);
                        height: vh(48);
                        color: rgba(228, 238, 255, 1) !important;
                        border: vw(1) solid rgba(230, 235, 240, 0.2);
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: vw(16);
                        line-height: vh(22);
                        padding: 0 !important;
                    }
                }
            }
        }
    }
}
::v-deep .el-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: vh(64);
}
::v-deep .el-pagination .el-pager li {
    border: 1px solid rgba(173, 176, 189, 1);
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination .btn-prev {
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-pager li.active {
    background-color: #8cb2ff !important;
}
::v-deep .el-pagination__jump {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(14);
    line-height: vh(22);
    color: rgba(173, 176, 189, 1);
}
::v-deep .el-input__inner {
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-table {
    ::-webkit-scrollbar {
        width: vw(8);
        height: vh(8);
    }
    ::-webkit-scrollbar-track {
        border-radius: vw(4);
        background-color: #265282;
    }
    ::-webkit-scrollbar-corner {
        background-color: #efefef;
    }
    ::-webkit-scrollbar-thumb {
        border-radius: vw(4);
        background-color: #65a4e5;
    }
}
::v-deep .el-table__body-wrapper {
    ::-webkit-scrollbar {
        width: vw(8);
        height: vh(8);
    }
    ::-webkit-scrollbar-track {
        border-radius: vw(4);
        background-color: #265282;
    }
    ::-webkit-scrollbar-corner {
        background-color: #efefef;
    }
    ::-webkit-scrollbar-thumb {
        border-radius: vw(4);
        background-color: #65a4e5;
    }
}
</style>
<style lang="scss">
.custom-select,
.select-dropdown {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;
    .popper__arrow {
        border-bottom-color: #677aa5 !important;
        &::after {
            border-bottom-color: #141e32 !important;
        }
    }
    .el-select-dropdown__item {
        color: #ccd3d9;
        font-size: vw(14) !important;
        height: vh(34);
        line-height: vh(34);
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background-color: #147bd119 !important;
        color: #4bb6ff;
    }
}
</style>
