<template>
    <div class="execution-detail-page">
        <div class="page-header">
            <div class="header-left">
                <el-button @click="goBack" type="text" class="back-btn">
                    <i class="el-icon-arrow-left"></i>
                    返回
                </el-button>
                <div class="page-title">
                    任务执行详情 - {{ taskName }}
                </div>
            </div>
        </div>
        
        <div class="page-content">
            <div class="detail-table-container">
                <div class="content">
                    <div class="el-table">
                        <el-table :data="detailTableData" 
                            :cell-style="cellStyle" 
                            :header-cell-style="headerStyle" 
                            :cell-class-name="cellClassName"
                            :header-cell-class-name="headerCellClassName"
                            :fit="true" 
                            table-layout='auto'
                            v-loading="loading">
                            <el-table-column prop="subInstanceId" label="任务ID"></el-table-column>
                            <el-table-column prop="strategyId" label="流程ID"></el-table-column>
                            <el-table-column prop="customMobile" label="客户手机号">
                                <template slot-scope="scope">
                                    {{ maskMobile(scope.row.customMobile) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="nodeName" label="当前节点">
                                <template slot-scope="scope">
                                    <span class="node-tag" :style="{ backgroundColor: getNodeColor(scope.row.nodeId) }">
                                        {{ getNodeName(scope.row.nodeId) }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="staffName" label="当前节点执行人"></el-table-column>
                            <el-table-column label="操作" width="170">
                                <template slot-scope="scope">
                                    <el-button @click="viewExecutionDetail(scope.row)" type="text" size="medium">
                                        查看执行详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    
                    <el-pagination 
                        popper-class="select-dropdown" 
                        background 
                        @size-change="handleDetailSizeChange"
                        @current-change="handleDetailCurrentChange"
                        :current-page.sync="detailPage.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="detailPage.pageSize"
                        :total="detailPage.total"
                        layout="sizes, prev, pager, next, jumper">
                    </el-pagination>
                </div>
            </div>
        </div>
        
        <!-- 执行详情抽屉 -->
        <el-drawer
            :visible.sync="executionDrawerVisible"
            title="执行详情"
            direction="rtl"
            size="60%"
            :modal="true"
            :append-to-body="true"
            custom-class="execution-drawer"
            @close="closeExecutionDrawer">
            <div class="execution-content">
                <div class="execution-list">
                    <div v-for="(item, index) in executionDetailData" :key="index" class="execution-item">
                        <div class="execution-header">
                            <div class="node-info">
                                <span class="node-tag" :style="{ backgroundColor: getNodeColor(item.nodeId) }">
                                    {{ getNodeName(item.nodeId) }}
                                </span>
                                <span class="handler-info">处理人：{{ item.handler }} ({{ maskMobile(item.handlerMobile) }})</span>
                            </div>
                            <div class="time-info">
                                <span>创建时间：{{ formatTime(item.createTime) }}</span>
                                <span>更新时间：{{ formatTime(item.updateTime) }}</span>
                            </div>
                            <div class="status-info">
                                <span class="status-tag" :style="{ backgroundColor: getNodeStatusColor(item.nodeStatus) }">
                                    {{ item.nodeStatus }}
                                </span>
                            </div>
                        </div>
                        <div class="feedback-content">
                            <div class="feedback-title">反馈内容:</div>
                            <div class="feedback-items" v-if="item.parsedFeedback">
                                <div v-for="(feedbackItem, fIndex) in item.parsedFeedback" :key="fIndex" class="feedback-item">
                                    <div v-if="feedbackItem.type === 'table'" class="table-feedback">
                                        <div class="feedback-label">{{ feedbackItem.name }}:</div>
                                        <div class="custom-feedback-table">
                                            <!-- 表头 -->
                                            <div class="feedback-table-header">
                                                <div v-for="col in feedbackItem.columns" :key="col.key" class="feedback-header-cell">
                                                    {{ col.name }}
                                                </div>
                                            </div>
                                            <!-- 表格主体 -->
                                            <div class="feedback-table-body">
                                                <div v-for="(row, rowIndex) in feedbackItem.tableData" :key="rowIndex" 
                                                     class="feedback-table-row"
                                                     :class="{ 'even-row': rowIndex % 2 === 0, 'odd-row': rowIndex % 2 !== 0 }">
                                                    <div v-for="col in feedbackItem.columns" :key="col.key" class="feedback-table-cell">
                                                        {{ row[col.key] || '—' }}
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 无数据提示 -->
                                            <div v-if="!feedbackItem.tableData || feedbackItem.tableData.length === 0" class="no-feedback-data">
                                                暂无数据
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="normal-feedback">
                                        <div class="feedback-label">{{ feedbackItem.name }}:</div>
                                        <div class="feedback-value">{{ feedbackItem.val || '无' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="no-feedback">暂无反馈内容</div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import market from '@/api/market';

export default {
    name: "ExecutionDetail",
    data() {
        return {
            taskId: '',
            taskName: '',
            loading: false,
            detailTableData: [],
            detailPage: { pageSize: 10, currentPage: 1, total: 0 },
            // 执行详情抽屉相关数据
            executionDrawerVisible: false,
            executionDetailData: [],
            // 节点映射
            nodeMap: {
                'INITIATOR_NODE': { name: '发起', color: '#67c23a' },
                'ASSIT_NODE1': { name: '首次预约反馈', color: '#409eff' },
                'ASSIT_NODE2': { name: '二次预约外呼', color: '#e6a23c' },
                'ASSIT_NODE3': { name: '上门服务', color: '#f56c6c' },
                'ASSIT_NODE4': { name: '上门服务', color: '#f56c6c' },
                'END_NODE': { name: '结束节点', color: '#909399' }
            },
        }
    },
    mounted() {
        console.log(this.$route.query)
        this.taskId = this.$route.query.taskId;
        this.taskName = this.$route.query.taskName || '';
        if (this.taskId) {
            this.getDetailList();
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            this.$router.go(-1);
        },
        // 获取详情列表
        getDetailList() {
            this.loading = true;
            market.getIndicatorInsightList({
                taskId: this.taskId,
                pageNum: this.detailPage.currentPage,
                pageSize: this.detailPage.pageSize
            }).then(res => {
                if (res.code === 200) {
                    this.detailPage.total = res.data.total;
                    this.detailTableData = res.data.list;
                }
            }).catch(err => {
                console.error('获取详情列表失败:', err);
                this.$message.error('获取详情列表失败');
            }).finally(() => {
                this.loading = false;
            });
        },
        // 查看执行详情
        viewExecutionDetail(row) {
            this.executionDrawerVisible = true;
            this.getExecutionDetail(row.subInstanceId);
        },
        // 获取执行详情
        getExecutionDetail(subInstanceId) {
            market.getIndicatorInsightDetail({
                subInstanceId: subInstanceId,
                pageNum: 1,
                pageSize: 100
            }).then(res => {
                if (res.code === 200) {
                    this.executionDetailData = res.data.list.map(item => {
                        // 解析feedbackContent
                        item.parsedFeedback = this.parseFeedbackContent(item.feedbackContent);
                        return item;
                    }).sort((a, b) => new Date(a.createTime) - new Date(b.createTime));
                }
            }).catch(err => {
                console.error('获取执行详情失败:', err);
                this.$message.error('获取执行详情失败');
            });
        },
        // 解析反馈内容
        parseFeedbackContent(feedbackContent) {
            if (!feedbackContent) return null;
            try {
                const feedback = JSON.parse(feedbackContent);
                return feedback.map(item => {
                    if (item.type === 'table' && item.val && Array.isArray(item.val) && item.val.length > 0) {
                        // 表格类型数据
                        const columns = item.val[0] || [];
                        const tableData = item.val.slice(1) || [];
                        return {
                            ...item,
                            columns: columns,
                            tableData: tableData.map(row => {
                                const rowData = {};
                                columns.forEach((col, index) => {
                                    rowData[col.key] = row[col.key] || '';
                                });
                                return rowData;
                            })
                        };
                    }
                    return item;
                });
            } catch (e) {
                console.error('解析反馈内容失败:', e);
                return null;
            }
        },
        // 手机号脱敏
        maskMobile(mobile) {
            if (!mobile) return '';
            return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
        },
        // 获取节点颜色
        getNodeColor(nodeId) {
            return this.nodeMap[nodeId]?.color || '#909399';
        },
        // 获取节点名称
        getNodeName(nodeId) {
            return this.nodeMap[nodeId]?.name || nodeId;
        },
        // 获取节点状态颜色
        getNodeStatusColor(status) {
            switch (status) {
                case '已回单':
                    return '#67c23a';
                case '处理中':
                    return '#409eff';
                case '待处理':
                    return '#e6a23c';
                case '已取消':
                    return '#f56c6c';
                default:
                    return '#909399';
            }
        },
        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            return new Date(time).toLocaleString();
        },
        // 关闭执行详情抽屉
        closeExecutionDrawer() {
            this.executionDrawerVisible = false;
            this.executionDetailData = [];
        },
        // 详情列表分页
        handleDetailSizeChange(val) {
            this.detailPage.pageSize = val;
            this.getDetailList();
        },
        handleDetailCurrentChange(val) {
            this.detailPage.currentPage = val;
            this.getDetailList();
        },
        // 动态计算列宽
        dynamicCalculationWidth(prop, tableData, title, num = 0) {
            // if (tableData.length === 0) {
            //     // 表格没数据不做处理
            //     return
            // }
            // let flexWidth = 0 // 初始化表格列宽
            // let columnContent = '' // 占位最宽的内容
            // let canvas = document.createElement('canvas')
            // let context = canvas.getContext('2d')
            // context.font = '16px PingFang SC'
            // if (prop === '' && title) {
            //     // 标题长内容少的，取标题的值,
            //     columnContent = title
            // } else {
            //     // 获取该列中占位最宽的内容
            //     let index = 0
            //     for (let i = 0; i < tableData.length; i++) {
            //         const now_temp = tableData[i][prop] + ''
            //         const max_temp = tableData[index][prop] + ''
            //         const now_temp_w = context.measureText(now_temp).width
            //         const max_temp_w = context.measureText(max_temp).width
            //         if (now_temp_w > max_temp_w) {
            //             index = i
            //         }
            //     }
            //     columnContent = tableData[index][prop]
            //     // 比较占位最宽的值跟标题、标题为空的留出四个位置
            //     const column_w = context.measureText(columnContent).width
            //     const title_w = context.measureText(title).width
            //     if (column_w < title_w) {
            //         columnContent = title || '占位符呀'
            //     }
            // }
            // // 计算最宽内容的列宽
            // let width = context.measureText(columnContent)
            // flexWidth = width.width + 40 + num
            // return flexWidth + 'px'
        },
        // 表格样式
        cellClassName() {
            return 'cell-style'
        },
        headerCellClassName() {
            return 'header-common';
        },
        // 表头样式
        headerStyle() {
            return {
                'white-space': 'nowrap',
                'padding': '0 12px'
            };
        },
        cellStyle({ row, rowIndex }) {
            return {
                backgroundColor: rowIndex % 2 !== 0 ? 'rgba(36, 63, 93, 1)' : 'rgba(32, 66, 105, 1)',
                'white-space': 'nowrap',
                'padding': '0 12px'
            };
        }
    }
}
</script>

<style lang="scss" scoped>
@import "~@/views/operation/index.scss";

.execution-detail-page {
    width: 100%;
    margin: 0 auto;
    height: 100%;
    // background-color: #8cb2ff;
    padding-top: vh(20);
    .page-header {
        height: vh(60);
        margin-bottom: vh(20);
        display: flex;
        align-items: center;
        
        .header-left {
            display: flex;
            align-items: center;
            gap: vw(20);
            
            .back-btn {
                color: #e4eeff;
                font-size: vw(16);
                padding: vh(8) vw(16);
                
                &:hover {
                    color: #409eff;
                }
                
                i {
                    margin-right: vw(8);
                }
            }
            
            .page-title {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: vw(20);
                color: #e4eeff;
            }
        }
    }
    
    .page-content {
        // height: calc(100vh - #{vh(100)});
        
        .detail-table-container {
            width: vw(1840);
            height: vh(840);
            margin: 0 auto;
            padding: 2px;
            background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));
            border-radius: vw(10);
            
            .content {
                padding: vh(20) vw(20);
                border-radius: vw(8);
                background: #1a3059;
                box-sizing: border-box;
                height: 100%;
                
                .el-table::before {
                    height: 0;
                }
                .el-table {
                    width: vw(1840);
                    height: vh(736);
                    border-radius: vw(10);
                    background-color: transparent;
                    
                    .node-tag {
                        display: inline-block;
                        width: fit-content;
                        height: fit-content;
                        padding: vh(2) vw(8);
                        border-radius: vw(22);
                        color: #fff;
                        font-size: vw(18);
                        border: none;
                        line-height: vh(20);
                    }
                    
                    ::v-deep .el-table {
                        width: vw(1800);
                        height: vh(736);
                        border-radius: vw(10);
                        overflow: auto;
                        border: 0;
                        background-color: transparent;
                        
                        .header-common {
                            width: vw(198);
                            height: vh(48);
                            background-color: #265282 !important;
                            color: rgba(228, 238, 255, 1) !important;
                            border: vw(1) solid rgba(230, 235, 240, 0.2);
                            border-top: 0;
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: vw(18);
                            line-height: vh(22);
                            padding: 0 !important;
                        }
                        
                        .cell-style {
                            height: vh(48);
                            color: rgba(228, 238, 255, 1) !important;
                            border: vw(1) solid rgba(230, 235, 240, 0.2);
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: vw(16);
                            line-height: vh(22);
                            padding: 0 !important;
                        }
                    }
                }
            }
        }
    }
}

// 分页样式
::v-deep .el-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: vh(64);
}
::v-deep .el-pagination .el-pager li {
    border: 1px solid rgba(173, 176, 189, 1);
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination .btn-prev {
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-pager li.active {
    background-color: #8cb2ff !important;
}
::v-deep .el-pagination__jump {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(14);
    line-height: vh(22);
    color: rgba(173, 176, 189, 1);
}
::v-deep .el-input__inner {
    color: white !important;
    background-color: transparent !important;
}

// 执行详情抽屉样式
::v-deep .execution-drawer {
    background: linear-gradient(270deg, #212739 0%, #252c41 100%);
    opacity: 0.95;
    
    .el-drawer__header {
        padding: vh(20) vw(24);
        border-bottom: 1px solid #445369;
        
        span {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: vw(18);
            color: #e4eeff;
        }
    }
    
    .el-drawer__body {
        padding: vh(20) vw(24);
        background: #1a3059;
    }
}

.execution-content {
    height: 100%;
    
    .execution-list {
        .execution-item {
            margin-bottom: vh(24);
            background: rgba(36, 63, 93, 0.6);
            border: 1px solid #445369;
            border-radius: vw(8);
            padding: vh(16) vw(20);
            
            .execution-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: vh(16);
                padding-bottom: vh(12);
                border-bottom: 1px solid #445369;
                
                .node-info {
                    display: flex;
                    align-items: center;
                    gap: vw(12);
                    
                    .handler-info {
                        color: #e4eeff;
                        font-size: vw(14);
                    }
                }
                
                .time-info {
                    display: flex;
                    flex-direction: column;
                    gap: vh(4);
                    
                    span {
                        color: #ccd3d9;
                        font-size: vw(12);
                    }
                }
                
                .status-info {
                    .status-tag {
                        display: inline-block;
                        padding: vh(4) vw(8);
                        border-radius: vw(4);
                        color: #fff;
                        font-size: vw(12);
                    }
                }
            }
            
            .feedback-content {
                .feedback-title {
                    color: #e4eeff;
                    font-size: vw(16);
                    font-weight: 500;
                    margin-bottom: vh(12);
                }
                
                .feedback-items {
                    .feedback-item {
                        margin-bottom: vh(12);
                        
                        .feedback-label {
                            color: #ccd3d9;
                            font-size: vw(14);
                            margin-bottom: vh(4);
                        }
                        
                        .feedback-value {
                            color: #e4eeff;
                            font-size: vw(14);
                            padding: vh(6) vw(12);
                            background: rgba(29, 51, 75, 0.6);
                            border-radius: vw(4);
                            border: 1px solid #445369;
                        }
                        
                        .table-feedback {
                            .custom-feedback-table {
                                margin-top: vh(8);
                                border: vw(1) solid rgba(230, 235, 240, 0.2);
                                border-radius: vw(4);
                                overflow: hidden;
                                
                                .feedback-table-header {
                                    display: flex;
                                    background-color: #265282;
                                    
                                    .feedback-header-cell {
                                        flex: 1;
                                        padding: vh(8) vw(12);
                                        color: rgba(228, 238, 255, 1);
                                        font-family: PingFang SC;
                                        font-weight: 500;
                                        font-size: vw(14);
                                        line-height: vh(20);
                                        border-right: vw(1) solid rgba(230, 235, 240, 0.2);
                                        text-align: center;
                                        
                                        &:last-child {
                                            border-right: none;
                                        }
                                    }
                                }
                                
                                .feedback-table-body {
                                    .feedback-table-row {
                                        display: flex;
                                        border-bottom: vw(1) solid rgba(230, 235, 240, 0.2);
                                        
                                        &.even-row {
                                            background-color: rgba(32, 66, 105, 0.8);
                                        }
                                        
                                        &.odd-row {
                                            background-color: rgba(36, 63, 93, 0.8);
                                        }
                                        
                                        &:hover {
                                            background-color: rgba(44, 74, 107, 0.8) !important;
                                        }
                                        
                                        &:last-child {
                                            border-bottom: none;
                                        }
                                        
                                        .feedback-table-cell {
                                            flex: 1;
                                            padding: vh(8) vw(12);
                                            color: rgba(228, 238, 255, 1);
                                            font-family: PingFang SC;
                                            font-weight: 400;
                                            font-size: vw(14);
                                            line-height: vh(20);
                                            border-right: vw(1) solid rgba(230, 235, 240, 0.2);
                                            text-align: center;
                                            word-break: break-all;
                                            
                                            &:last-child {
                                                border-right: none;
                                            }
                                        }
                                    }
                                }
                                
                                .no-feedback-data {
                                    padding: vh(20) vw(12);
                                    text-align: center;
                                    color: #909399;
                                    font-size: vw(14);
                                    font-style: italic;
                                    background-color: rgba(32, 66, 105, 0.4);
                                }
                            }
                        }
                    }
                }
                
                .no-feedback {
                    color: #909399;
                    font-size: vw(14);
                    font-style: italic;
                }
            }
        }
    }
}

.node-tag {
    display: inline-block;
    padding: vh(4) vw(8);
    border-radius: vw(12);
    color: #fff;
    font-size: vw(12);
    white-space: nowrap;
}
</style>

<style lang="scss">
.custom-select,
.select-dropdown {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;
    .popper__arrow {
        border-bottom-color: #677aa5 !important;
        &::after {
            border-bottom-color: #141e32 !important;
        }
    }
    .el-select-dropdown__item {
        color: #ccd3d9;
        font-size: vw(14) !important;
        height: vh(34);
        line-height: vh(34);
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background-color: #147bd119 !important;
        color: #4bb6ff;
    }
}
</style>