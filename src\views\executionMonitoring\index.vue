<template>
  <div class="monitor">
      <MonitoringMap v-show="tableShow"></MonitoringMap>
      <Table v-show="!tableShow"></Table>
  </div>
</template>
<script>
import MonitoringMap from './components/monitoringMap.vue';
import Table from './components/table.vue';
export default {
  name: 'ExecutionMonitoring',
  components: {
    Table,
    MonitoringMap
  },
  data() {
    return {
      tableShow: false,
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.monitor {
  width: 100%;
  height: 100%;
  // background-color: aqua;
  
}
</style>
