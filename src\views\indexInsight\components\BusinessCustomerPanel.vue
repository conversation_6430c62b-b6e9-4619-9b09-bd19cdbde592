<script>
import PanelCard from '@/views/indexInsight/components/PanelCard.vue'

export default {
  name: 'BusinessCustomerPanel',
  components: { PanelCard },
  props: {
    dataSource: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    dataSource: {
      handler(newVal) {
        const sk101 = this.mathUnit(newVal.skValue101, "个")
        const sk102 = this.mathUnit(newVal.skValue102, "个")
        const sk103 = this.mathUnit(newVal.skValue103, "个")
        const sk104 = this.mathUnit(newVal.skValue104, "个")
        const sk105 = this.mathUnit(newVal.skValue105, "个")
        const sk106 = this.mathUnit(newVal.skValue106, "个")

        const sk201 = this.mathUnit(newVal.skValue201, "个")
        const sk202 = this.mathUnit(newVal.skValue202, "个")
        const sk203 = this.mathUnit(newVal.skValue203, "个")
        const sk204 = this.mathUnit(newVal.skValue204, "个")

        const sk301 = this.mathUnit(newVal.skValue301, "元")

        const sk401 = this.mathUnit(newVal.skValue401, "户")
        const sk403 = this.mathUnit(newVal.skValue403, "个")
        this.targetCustom = [
          { name: '楼宇目标数', persons: sk101 || {}, areas: sk102 || {}},
          { name: '泛住宿目标数', persons: sk103 || {}, areas: sk104 || {}},
          { name: '沿街商铺目标数', persons: sk105 || {}, areas: sk106 || {}}
        ]
        this.areaFight = [
          { icon: '1', name: '任务总量', value: sk201 || {}},
          { icon: '2', name: '作战区域数', value: sk202 || {}},
          { icon: '1', name: '正在作战区域数', value: sk203 || {}},
          { icon: '2', name: '区域平均参与人数', value: sk204 || {}}
        ]
        this.income = [
          { name: '收入金额', value: sk301.value || '--', unit: sk301.unit },
          { name: '收入增幅', value: newVal.skValue302 || '--', unit: '%' }
        ]
        this.scale = [
          { name: '建档纳管客户净值', value: sk401.value || '--', unit: sk401.unit },
          { name: '建档纳管增幅', value: newVal.skValue402 || '--', unit: '%' },
          { name: '低占区域减少数', value: sk403.value || '--', unit: sk403.unit },
          { name: '--', value: newVal.skValue404 || '--', unit: '%' },
          { name: '--', value: newVal.skValue405 || '--', unit: '%' },
          { name: '--', value: newVal.skValue406 || '--', unit: '%' }
        ]
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      targetCustom: [
        // { name: '楼宇目标客户数', persons: '--', areas: '--' },
        // { name: '泛住宿目标客户数', persons: 'XXX', areas: 'XX' },
        // { name: '沿街商铺目标客户数', persons: 'XXX', areas: 'XX' }
      ],
      areaFight: [
        // { icon: '1', name: '任务总量', value: 'XX' },
        // { icon: '2', name: '作战区域数', value: 'XXX' },
        // { icon: '1', name: '正在作战区域数', value: 'XXX' },
        // { icon: '2', name: '区域平均参与人数', value: 'XXX' }
      ],
      income: [
        // { name: '收入金额', value: 'XXX', unit: '万元' },
        // { name: '收入增幅', value: 'XX', unit: '%' }
      ],
      scale: [
        // { name: '建档纳管客户净值', value: 'XXX', unit: '个' },
        // { name: '建档纳管增幅', value: 'XX', unit: '%' },
        // { name: '低占区域减少数', value: 'XXX', unit: '个' },
        // { name: '泛住宿场景渗透客户增幅', value: 'XX', unit: '%' },
        // { name: '楼宇场景渗透客户增幅', value: 'XX', unit: '%' },
        // { name: '沿街商铺场景渗透客户增幅', value: 'XX', unit: '%' }
      ]
    }
  },
  methods: {
    mathUnit(str, defaultUnit) {
      if (!str) {
        return { value: '--', unit: defaultUnit || '--' }
      }
      const result = str.match(/[\u4e00-\u9fa5]+/g) || []
      if (result.length) {
        return {
          value: str.split(result[0])[0],
          unit: result[0] + defaultUnit
        }
      } else {
        return {
          value: str,
          unit: defaultUnit || '--'
        }
      }
    }
  }
}
</script>

<template>
  <div class="business-customer-panel">
    <PanelCard title="目标客群">
      <div class="target-customer">
        <div v-for="item in targetCustom" :key="item.persons" class="target-item">
          <div>
            <div class="custom-avatar"></div>
            <div class="text-desc">{{ item.name }}</div>
          </div>
          <div class="text-value">
            <div class="num">{{ item.persons.value }}</div>{{ item.persons.unit }}
          </div>
          <div class="text-value">
            低占区域
            <div class="num">{{ item.areas.value }}</div>{{ item.areas.unit }}
          </div>
        </div>
      </div>
    </PanelCard>
    <PanelCard title="区域式作战">
      <div class="area-fight">
        <div v-for="item in areaFight" :key="item.value" class="area-fight-item">
          <div class="left-img" :class="'area-fight-img-' + item.icon"></div>
          <div class="right-info">
            <div class="text-desc">{{ item.name }}</div>
            <div class="text-value">
              <div class="value">{{ item.value.value }}</div>{{ item.value.unit }}
            </div>
          </div>
        </div>
      </div>
    </PanelCard>
    <PanelCard title="收入">
      <div class="in-come">
        <div v-for="item in income" :key="item.value" class="income-item">
          <div class="text-value">
            <div class="value">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
          <div class="text-desc">{{ item.name }}</div>
        </div>
      </div>
    </PanelCard>
    <PanelCard title="规模">
      <div class="scale">
        <div v-for="item in scale" :key="item.value" class="scale-item">
          <div class="absolut-position">
            <div class="value-unit">
              <div class="value">{{ item.value }}</div>
              <div class="unit">{{ item.unit }}</div>
            </div>
            <div class="text-desc">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </PanelCard>
  </div>
</template>

<style scoped lang="scss">
@import "~@/views/operation/index";
.business-customer-panel{
  width: vw(440);
  height: vh(875);
  background: #25417760;
  border: 2px solid;
  border-image-source: linear-gradient(180deg, #59B7FF 0%, rgba(41, 33, 98, 0.0001) 100%);
  border-radius: 16px;
  //angle: 0 deg;
  //opacity: 0.6;
  //border-width: 2px;
  padding: vh(10) vw(12);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.target-customer{
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  .target-item{
    height: vh(32);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: vh(10);
    & > div:first-child {
      display: flex;
    }
    .custom-avatar{
      width: vw(20);
      height: vw(20);
      background-size: 100% 100%;
      background-image: url("../../../assets/images/indexInsight/custom_avatar.png");
      margin-right: vw(5);
    }
    .text-desc{
      font-size: vw(16);
      color: #B5D3FF;
      line-height: vh(20);
      letter-spacing: 0;
      width: vw(145);
      text-align: left;
    }
    .text-value{
      color: #B5D3FF;
      font-size: vw(14);
      display: flex;
      align-items: center;
      .num{
        font-size: vw(24);
        color: #ffffff;
        margin-left: vw(14);
        margin-right: vw(4);
      }
    }
  }
}
.area-fight{
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  .area-fight-item{
    width: 49%;
    height: vh(82);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .left-img{
      width: vw(64);
      height: vw(64);
      background-size: 100% 100%;
      margin-right: 5px;
      &.area-fight-img-1{
        background-image: url("../../../assets/images/indexInsight/area-fight-img-1.png");
      }
      &.area-fight-img-2{
        background-image: url("../../../assets/images/indexInsight/area-fight-img-2.png");
      }
    }
    .right-info{
      font-size: vw(16);
      height: vw(50);
      color: #B5D3FF;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      .text-desc{}
      .text-value{
        display: flex;
        align-items: flex-end;
        justify-content: flex-start;
        .value{
          color: #ffffff;
          font-size: vw(24);
        }
      }
    }
  }
}
.in-come{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .income-item{
    width: 49.9%;
    height: vh(76);
    background-image: url("../../../assets/images/indexInsight/income-back.png");
    background-size: 100% 100%;
    color: #B5D3FF;
    font-size: vw(16);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    .text-value{
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;
      .value{
        font-size: vw(24);
        line-height: vw(24);
        color: #ffffff;
      }
      .unit{
        font-size: vw(16);
        margin-left: 3px;
      }
    }
    .text-desc{
      font-size: vw(18);
    }
  }
}
.scale{
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  .scale-item{
    //display: flex;
    //align-items: center;
    //flex-direction: column;
    //justify-content: flex-end;
    height: vh(121);
    width: vw(131);
    background-size: 100% 100%;
    background-image: url("../../../assets/images/indexInsight/scale-back.png");
    font-size: vw(14);
    color: #B5D3FF;
    position: relative;
    .absolut-position{
      width: vw(131);
      position: absolute;
      top: vh(70);
      left: 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-direction: column;
      .value-unit{
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        .value{
          font-size: vw(24);
          color: #ffffff;
        }
        .unit{
          font-size: vw(16);
          margin-left: 4px;
        }
      }
      .text-desc{
        text-align: center;
        margin-top: vh(6);
      }
    }
  }
}
</style>
