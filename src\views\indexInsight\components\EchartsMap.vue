<script>
import * as echarts from 'echarts'
import { getMapIndexInfo } from '@/api/indexInsight'
import { fitChartSize } from '@/utils/dataUtil'

export default {
  name: 'EchartsMap',
  data() {
    return {
      mapChart: null,
      currentArea: null
    }
  },
  methods: {
    initEchartsMap(datas) {
      if (this.mapChart) {
        this.mapChart.dispose()
      }
      this.mapChart = echarts.init(document.getElementById('echarts-map-wrapper'))
      echarts.registerMap('map', this.mapJson)
      echarts.registerMap('mapBorder', this.mapBorderJson)
      this.mapChart.off('click')
      const option = this.getOption(datas)
      this.mapChart.setOption(option)
      this.mapChart.on('click', (params) => {
        console.log(params.data)
        this.currentArea = params.data
        if (params.data.areaLevel <= 3) {
          this.$emit('map-init', params.data)
        }
      })
    },
    async initMapData(data) {
      this.currentArea = { areaId: data.areaId, areaLevel: parseInt(data.areaLevel) }
      this.mapBorderJson = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            properties: {
              name: data.areaName
            },
            geometry: {
              type: 'MultiPolygon',
              coordinates: this.polygonPath(data.boundary)
            }
          }
        ]
      }
      this.mapJson = {
        type: 'FeatureCollection',
        features: this.polygonFullPath(data.children)
      }
      this.geoCoordMap = this.getGeoCoordMap(data.children)
      this.mapData = data.children.map(item => {
        return {
          name: item.areaName,
          areaId: item.areaId,
          areaLevel: item.areaLevel,
          centralPoint: item.centralPoint,
          value: 0,
          showValue: 0
        }
      })
      await this.getIndexNum()
      this.$nextTick(() => {
        this.initEchartsMap(this.mapData)
      })
    },
    getOption(datas) {
      var that = this
      function scatterData2() {
        return datas.map((item) => ({
          name: item.name,
          areaId: item.areaId,
          value: that.geoCoordMap[item.name]
        }))
      }
      const rangeArr = parseInt(this.currentArea.areaLevel) === 1 ? [500000, 1000000, 2000000] : [100000, 200000, 500000]
      return {
        geo: [
          // 第一层地图
          {
            map: 'map',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              borderColor: '#D8E6FF4D',
              borderWidth: 1
            },
            emphasis: {
              itemStyle: {
                areaColor: 'transparent'
              },
              label: {
                show: 0,
                color: '#fff'
              }
            },
            zlevel: 3
          },
          // 第二层地图和第一层重叠用作边框
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#F7F8FF',
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.25)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: fitChartSize(3.72)
            },
            zlevel: 2,
            silent: true
          }
          // 第三层地图底层
          // {
          //   map: 'mapBorder',
          //   aspectScale: 1,
          //   layoutSize: '96%',
          //   layoutCenter: ['50%', '52%'],
          //   itemStyle: {
          //     areaColor: 'transparent',
          //     borderColor: '#B6C1FB99',
          //     borderWidth: 1
          //   },
          //   zlevel: 1,
          //   silent: true
          // }
        ],
        visualMap: {
          show: false,
          type: 'piecewise',
          bottom: fitChartSize(40),
          right: fitChartSize(40),
          itemGap: fitChartSize(10),
          align: 'left',
          itemWidth: fitChartSize(16),
          itemHeight: fitChartSize(12),
          textStyle: {
            fontSize: fitChartSize(14),
            color: '#EDEDED'
          },
          seriesIndex: 0,
          pieces: [
            {
              gt: rangeArr[2],
              // lte: 10000,
              color: '#A9FFDA80',
              label: '≥200'
            },
            {
              gt: rangeArr[1],
              lte: rangeArr[2],
              color: '#37D89280',
              label: '100-200'
            },
            {
              gt: rangeArr[0],
              lte: rangeArr[1],
              color: '#FF838380',
              label: '50-100'
            },
            {
              gt: 0,
              lte: rangeArr[0],
              color: '#FF393980',
              label: '≤50'
            }
          ]
        },
        series: [
          {
            type: 'map',
            selectedMode: false,
            map: 'map',
            geoIndex: 0,
            data: datas
          },
          // 地市弹框撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: '{b}',
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#fff',
              fontSize: fitChartSize(20),
              fontWeight: '500',
              fontFamily: '',
              show: true
            },
            symbol: 'rect',
            // symbolSize: function(data, params) {
            //   const name = params.data.name
            //   const nameLength = name.length || 0
            //   const width = fitChartSize(nameLength * 20)
            //   const height = fitChartSize(30)
            //   return [width, height]
            // },
            symbolOffset: [0, fitChartSize(-30)],
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              borderWidth: fitChartSize(0.5),
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
          // 地市对应值撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: function(name) {
                const item = datas.find(item => item.name === name.name)
                return item ? item.showValue : ''
              },
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#ffffff',
              fontSize: fitChartSize(23),
              fontFamily: 'Milibus',
              show: true
            },
            symbol: 'rect',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          }
        ]
      }
    },
    scatterData2() {
      return datas.map((item) => ({
        name: item.name,
        value: this.geoCoordMap[item.name]
      }))
    },
    polygonPath(data) {
      const points = data.split(';')
      const polygon = points.map(item => {
        const [lon, lat] = item.split(',')
        return [parseFloat(lon.trim()), parseFloat(lat.trim())]
      })
      return [[polygon]]
    },
    /**
     * @description 格式化边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonFullPath(data) {
      return data.map(item => {
        return {
          type: 'Feature',
          properties: {
            name: item.areaName
          },
          geometry: {
            type: 'MultiPolygon',
            coordinates: this.polygonPath(item.boundary)
          }
        }
      })
    },
    getGeoCoordMap(arr) {
      const result = {}
      arr.forEach(item => {
        const [longitude, latitude] = item.centralPoint.split(',').map(parseFloat)
        result[`${item.areaName}`] = [longitude, latitude]
      })
      return result
    },
    async getIndexNum() {
      await getMapIndexInfo({ ...this.currentArea }).then(({ code, data, msg }) => {
        if (code === 200) {
          this.mapData.forEach(item => {
            const obj = data.find(el => el.areaId === item.areaId)
            if (obj) {
              item.value = obj.num
              item.showValue = obj.showNum || 0
            } else {
              item.value = 0
              item.showValue = 0
            }
          })
        } else {
          this.$message.error(msg)
        }
      })
    }
  }
}
</script>

<template>
  <div id="echarts-map-wrapper" class="echarts-map-wrapper" />
</template>

<style scoped lang="scss">
.echarts-map-wrapper{
  width: 100%;
  height: 100%;
}
</style>
