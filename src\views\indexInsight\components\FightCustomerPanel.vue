<script>
import PanelCard from "@/views/indexInsight/components/PanelCard.vue";

export default {
  name: "BusinessCustomerPanel",
  components: { PanelCard },
  props: {
    dataSource: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    dataSource: {
      handler(newVal) {
        const { value: zk101Value, unit: zk101Unit } = this.mathUnit(newVal.zkValue101, "家")
        const { value: zk103Value, unit: zk103Unit } = this.mathUnit(newVal.zkValue103, "家")
        const { value: zk104Value, unit: zk104Unit } = this.mathUnit(newVal.zkValue104, "元")
        const { value: zk105Value, unit: zk105Unit } = this.mathUnit(newVal.zkValue105, "元")
        const { value: zk108Value, unit: zk108Unit } = this.mathUnit(newVal.zkValue108, "户")
        const { value: zk203Value, unit: zk203Unit } = this.mathUnit(newVal.zkValue203, "户")
        const { value: zk204Value, unit: zk204Unit } = this.mathUnit(newVal.zkValue204, "元")
        const { value: zk304Value, unit: zk304Unit } = this.mathUnit(newVal.zkValue304, "笔")
        const { value: zk301Value, unit: zk301Unit } = this.mathUnit(newVal.zkValue301, "条")
        const { value: zk302Value, unit: zk302Unit } = this.mathUnit(newVal.zkValue302, "条")
        
        this.operations = [
          { name: "战客建档纳管客户", value: zk101Value || '--', unit: zk101Unit },
          { name: "战客建档纳管纳管率", value: newVal.zkValue102 || '--', unit: "%" },
          { name: "净值战客数", value: zk103Value || '--', unit: zk103Unit },
          {
            name: "战客收入",
            value: zk104Value || '--',
            unit: zk104Unit
          },
          {
            name: "战客收入同比增长金额",
            value: zk105Value || '--',
            unit: zk105Unit
          },
          { name: "战客收入增幅", value: newVal.zkValue106 || '--', unit: "%" },
          { name: "标品渗透率", value: newVal.zkValue107 || '--', unit: "%" },
          { name: "标品万元等效客户数", value: zk108Value || '--', unit: zk108Unit },
          { name: "标品万元等效客户占比", value: newVal.zkValue109 || '--', unit: "%" },
        ];
        this.members = [
          { name: "战客成员价值保有率", value: newVal.zkValue201 || '--', unit: "%" },
          { name: "战客成员规模保有率", value: newVal.zkValue202 || '--', unit: "%" },
          { name: "战客成员总规模", value: zk203Value || '--', unit: zk203Unit },
          { name: "战客成员价值", value: zk204Value || '--', unit: zk204Unit },
          {
            name: "战客成员低占集团摘牌率",
            value: newVal.zkValue205 || '--',
            unit: "%",
          },
          { name: "战客成员金虎比", value: newVal.zkValue206 || '--', unit: "" },
        ];
        this.importantBusiness = {
          rowOne: [
            { name: "8大产品续约成功数", value: zk304Value || '--', unit: zk304Unit },
            { name: "8大产品续约成功率", value: newVal.zkValue305 || '--', unit: "%" },
          ],
          rowTwo: [
            { name: "系统工单数", value: zk301Value || '--', unit: zk301Unit },
            { name: "工单执行数", value: zk302Value || '--', unit: zk302Unit },
            { name: "工单执行率", value: newVal.zkValue303 || '--', unit: "%" },
          ],
        };
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      operations: [],
      members: [],
      importantBusiness: {
        rowOne: [],
        rowTwo: [],
      },
    };
  },
  methods: {
    mathUnit(str, defaultUnit) {
      if (!str) {
        return { value: '--', unit: defaultUnit || '--' }
      }
      const result = str.match(/[\u4e00-\u9fa5]+/g) || []
      if (result.length) {
        return {
          value: str.split(result[0])[0],
          unit: result[0] + defaultUnit
        }
      } else {
        return {
          value: str,
          unit: defaultUnit
        }
      }
    }
  }
};
</script>

<template>
  <div class="fight-customer-panel">
    <PanelCard title="战客运营">
      <div class="fight-operation">
        <div v-for="item in operations" class="operation-item">
          <div class="operation-img"></div>
          <div class="operation-img-2"></div>
          <div class="info">
            <div class="value-unit">
              <div class="value">{{ item.value }}</div>
              <div class="unit">{{ item.unit }}</div>
            </div>
            <div class="text-desc">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </PanelCard>
    <PanelCard title="战客成员运营">
      <div class="fight-member">
        <div v-for="item in members" class="member-item">
          <div class="member-header">
            <div class="icon"></div>
            {{ item.name }}
          </div>
          <div class="value-unit">
            <div class="value">{{ item.value }}</div>
            {{ item.unit }}
          </div>
        </div>
      </div>
    </PanelCard>
    <PanelCard title="重点业务运营">
      <div class="important-business">
        <div v-for="item in importantBusiness.rowOne" class="important-item">
          <div class="back-img"></div>
          <div class="text-desc">{{ item.name }}</div>
          <div class="value-unit">
            <div class="value">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </div>
      </div>
      <div class="important-business">
        <div v-for="item in importantBusiness.rowTwo" class="important-item">
          <div class="back-img"></div>
          <div class="text-desc">{{ item.name }}</div>
          <div class="value-unit">
            <div class="value">{{ item.value }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </div>
      </div>
    </PanelCard>
  </div>
</template>

<style scoped lang="scss">
@import "~@/views/operation/index";

.fight-customer-panel {
  width: vw(440);
  height: vh(875);
  background: #25417760;
  border: 2px solid;
  border-image-source: linear-gradient(180deg,
      #59b7ff 0%,
      rgba(41, 33, 98, 0.0001) 100%);
  border-radius: 16px;
  //angle: 0 deg;
  //opacity: 0.6;
  //border-width: 2px;
  padding: vh(10) vw(12);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.fight-operation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;

  .operation-item {
    position: relative;
    width: vw(120);
    height: vh(100);

    .operation-img {
      position: absolute;
      left: 0;
      top: 0;
      height: vw(49);
      width: vw(49);
      background-size: 100% 100%;
      background-image: url("../../../assets/images/indexInsight/fight-operation-back.png");
    }

    .operation-img-2 {
      position: absolute;
      left: vw(9.5);
      top: vw(9.5);
      height: vw(30);
      width: vw(30);
      background-size: 100% 100%;
      background-image: url("../../../assets/images/indexInsight/fight-operation-back-2.png");
    }

    .info {
      position: absolute;
      left: vw(20);
      top: vw(20);
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      flex-direction: column;
      font-size: vw(16);
      color: #b5d3ff;

      .value-unit {
        display: flex;
        align-items: flex-end;
        justify-content: flex-start;

        .value {
          font-size: vw(24);
          color: white;
          margin-right: 3px;
        }
      }

      .text-desc {
        text-align: left;
      }
    }
  }
}

.fight-member {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;

  .member-item {
    width: 49%;
    height: vw(60);
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    flex-direction: column;
    margin: vh(4) 0;
    font-size: vw(16);
    color: #b5d3ff;

    .member-header {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .icon {
        width: vw(14);
        height: vw(14);
        background-size: 100% 100%;
        background-image: url("../../../assets/images/indexInsight/fight-member-title-icon.png");
        margin-right: 5px;
      }
    }

    .value-unit {
      width: vw(105);
      height: vh(26);
      background-image: url("../../../assets/images/indexInsight/fight-member-info-back.png");
      background-size: 100% 100%;
      padding-left: vw(10);
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;

      .value {
        font-size: vw(24);
        color: #ffffff;
        margin-right: 3px;
      }
    }
  }
}

.important-business {
  display: flex;
  align-items: center;
  justify-content: space-around;

  .important-item {
    width: vw(122);
    height: vh(92);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    color: #b5d3ff;
    font-size: vw(14);

    .back-img {
      width: vw(76);
      height: vh(90);
      background-size: 100% 100%;
      background-image: url("../../../assets/images/indexInsight/import-business-back.png");
      position: absolute;
      left: vw(22);
      top: 0;
      z-index: 1000;
    }

    .text-desc {}

    .value-unit {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      z-index: 1001;

      .value {
        font-size: vw(24);
        color: #ffffff;
        margin-right: 5px;
      }

      .unit {
        font-size: vw(16);
        color: #eff8febf;
      }
    }
  }
}
</style>
