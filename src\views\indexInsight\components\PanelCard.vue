<script>
export default {
  name: 'PanelCard',
  props: {
    title: {
      type: String,
      required: true
    }
  },
  data() {
    return {

    }
  }
}
</script>

<template>
  <div class="panel-card">
    <div class="card-header">
      <div class="point"/>
      <div class="title">{{ title }}</div>
    </div>
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "~@/views/operation/index";
.panel-card{
  width: 100%;
  .card-header{
    height: vh(28);
    background: linear-gradient(90deg, rgba(38, 73, 147, 0.3) 0%, rgba(76, 107, 172, 0) 100%);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .point{
      width: vh(5);
      height: vh(5);
      border-radius: 50%;
      background-color: white;
      margin: 0 vw(14);
    }
    .title{
      color: white;
      font-size: vw(14);
      line-height: vh(20);
    }
  }
  .card-content{
    padding: vh(14) 0;
  }
}
</style>
