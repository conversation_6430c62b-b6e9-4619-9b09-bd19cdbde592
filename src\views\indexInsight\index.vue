<template>
  <div class="index-insight">
    <div class="right-top-handle-info">
      <div class="select-choice">
        <span>地市</span>
        <el-select
          v-model="selectForm.cityCode"
          @change="cityChange"
          :clearable="false"
          placeholder="请选择"
          popper-class="custom-select"
          size="mini"
        >
          <el-option :key="'-1'" label="全部" :value="'999'" />
          <el-option
            :key="item.areaId"
            :label="item.areaName"
            :value="item.areaId"
            v-for="item in selectgridList.cityList"
          />
        </el-select>
      </div>
      <div class="select-choice">
        <span>区县</span>
        <el-select
          v-model="selectForm.countyCode"
          @change="countyChange"
          :clearable="false"
          placeholder="请选择"
          popper-class="custom-select"
          size="mini"
        >
          <el-option :key="'-1'" label="全部" :value="'-1'" />
          <el-option
            :key="item.areaId"
            :label="item.areaName"
            :value="item.areaId"
            v-for="item in selectgridList.countyList"
          />
        </el-select>
      </div>
      <div class="select-choice">
        <span>网格</span>
        <el-select
          v-model="selectForm.gridCode"
          @change="gridChange"
          :clearable="false"
          placeholder="请选择"
          popper-class="custom-select"
          size="mini"
        >
          <el-option :key="'-1'" label="全部" :value="'-1'" />
          <el-option
            :key="item.areaId"
            :label="item.areaName"
            :value="item.areaId"
            v-for="item in selectgridList.gridList"
          />
        </el-select>
      </div>
      <!-- todo干掉tab切换 - 有问题询问@李树鹏 -->
      <!-- <div class="index-types">
        <div
          v-for="type in indexTypes"
          :key="'index_type_btn_' + type.key"
          class="type-item"
          :class="type.key === indexType ? 'active' : ''"
          @click="toIndexTypeShow(type)"
        >
          {{ type.name }}
        </div>
      </div> -->
      <div class="handle-form">
        <!-- <div class="area-chose">
          <el-select v-model="currentArea.areaId" @change="areaChange">
            <el-option v-for="opt in areaOpts" :key="opt.areaId" :label="opt.areaName" :value="opt.areaId" />
          </el-select>
        </div> -->
        <div class="time-chose">
          <span>日期</span>
          <el-date-picker
            @change="initData"
            v-model="queryDate"
            format="yyyy-MM-dd"
            popper-class="pop-date-cls"
            :picker-options="pickerOptions"
            x-placement="left"
            type="date"
            placeholder="选择日期"
            :clearable="false"
          ></el-date-picker>
        </div>
      </div>
    </div>
    <div v-show="indexType === 1" class="left-business-customer">
      <TitlePart :question="false" title="商客" width="238" />
      <business-customer-panel :dataSource="infoData" />
    </div>
    <div v-show="indexType === 1" class="right-fight-customer">
      <TitlePart :question="false" title="战客" width="238" />
      <fight-customer-panel :dataSource="infoData" />
    </div>
    <div v-show="indexType === 1" class="echarts-map">
      <echarts-map ref="echartsMap" @map-init="getMapInfo" />
    </div>
    <div v-show="indexType === 1" class="echarts-map-back"></div>
    <EchartsLegend
      v-show="indexType === 1"
      :has-panel="true"
      title="资管端口总数"
      :legend-list="legendList"
    />
  </div>
</template>
<script>
import TitlePart from "@/views/operation/components/TitlePart.vue";
import BusinessCustomerPanel from "@/views/indexInsight/components/BusinessCustomerPanel.vue";
import FightCustomerPanel from "@/views/indexInsight/components/FightCustomerPanel.vue";
import EchartsMap from "@/views/indexInsight/components/EchartsMap.vue";
import { getMapInfo } from "@/api/operation";
import EchartsLegend from "@/components/common/EchartsLegend.vue";

import common from "@/api/common";
import { getDataDates, getOverviewKpi } from "@/api/indexInsight";

export default {
  name: "",
  components: {
    EchartsLegend,
    EchartsMap,
    FightCustomerPanel,
    BusinessCustomerPanel,
    TitlePart,
  },
  filters: {},
  data() {
    return {
      indexType: 1, // 首页显示类型 1地图 2表单
      indexTypes: [
        { name: "地图", key: 1 },
        { name: "表单", key: 2 },
      ],
      currentArea: {
        // 当前区域信息 下拉选择也取这里面的值 初步决定 不晓得会出问题不
        areaId: "999",
        areaName: "全省",
        areaLevel: 1,
      },
      queryDate: "",
      minDataDate: null,
      maxDataDate: null,
      infoData: {},
      areaOpts: [
        // 默认全省
        { areaId: "999", areaLevel: 1, areaName: "全省" },
      ],
      areaMap: {},
      legendList: [
        { color: "#FF393980", borderColor: "#FF8787", text: "≤50万" },
        { color: "#FF838380", borderColor: "#FFBCB9", text: "50万-100万" },
        { color: "#37D89280", borderColor: "#A9FFDA", text: "100万-200万" },
        { color: "#A9FFDA80", borderColor: "#A9FFDA", text: "≥200万" },
      ],
      // 选择框改造
      selectForm: {
        cityCode: "999",
        countyCode: "-1",
        gridCode: "-1",
      },
      selectgridList: {
        cityList: [],
        countyList: [],
        gridList: [],
      },
    };
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate: (time) => {
          return (
            time.getTime() < this.minDataDate ||
            time.getTime() > this.maxDataDate
          );
        },
      };
    },
  },
  created() {
    const date = new Date();
    const stamp = date.getTime();
    this.queryDate = new Date(stamp - 1000 * 60 * 60 * 24);
  },
  mounted() {
    this.getMapInfo({ areaId: '999', areaLevel: 1 })
    this.getAroundTime();
    this.getAreaByPid(999, "cityList");
  },
  methods: {
    cityChange(value) {
      this.selectForm.cityCode = value;
      this.selectForm.countyCode = "-1";
      this.selectForm.gridCode = "-1";
      if (value !== '-1') {
        this.getAreaByPid(value, "countyList");
      }
      this.areaChange({ areaId: value, areaLevel: value === "999" ? 1 : 2 })
    },
    countyChange(value) {
      this.selectForm.countyCode = value;
      // 网格编码清空
      this.selectForm.gridCode = "-1";
      if (value !== '-1') {
        this.getAreaByPid(value, "gridList");
      }
      this.areaChange({ areaId: value === '-1' ? this.selectForm.cityCode : value, areaLevel: value === '-1' ? 2: 3 })
    },
    gridChange(value) {
      this.selectForm.gridCode = value;
      // 四级点击不触发查询
      // this.areaChange({ areaId: value, areaLevel: 4 })
    },
    // 获取下拉框选择列表
    async getAreaByPid(code, listName) {
      await common
        .getAreaByPid({
          areaPid: code,
        })
        .then((res) => {
          if (res.code === 200) {
            this.selectgridList[listName] = res.data;
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    // 20180621 => 2018-06-21
    transformTime(time) {
      if (!time || time.includes("-")) {
        return time;
      } else {
        const time_ =
          time.slice(0, 4) + "-" + time.slice(4, 6) + "-" + time.slice(6, 8);
        return new Date(time_).getTime();
      }
    },
    getAroundTime() {
      getDataDates().then((res) => {
        if (res.code === 200) {
          const { maxDataDate, minDataDate } = res.data;
          const max_ = this.transformTime(maxDataDate);
          this.queryDate = max_;
          this.maxDataDate = max_;
          this.minDataDate = this.transformTime(minDataDate);
          this.$nextTick(() => {
            this.initData();
          });
        }
      });
    },
    initData() {
      const params = {
        areaId: this.currentArea.areaId, // 区域编码
        areaLevel: this.currentArea.areaLevel, // 区域等级，1：省；2：地市；3：区县；4：网格
        dataDate: new Date(this.queryDate).Format("yyyyMMdd"),
      };
      getOverviewKpi(params).then((res) => {
        if (res.code === 200) {
          this.infoData = res.data;
        }
      });
    },
    areaChange(currentArea_) {
      this.currentArea = { ...currentArea_ };
      this.getMapInfo(this.currentArea);
      this.initData();
    },
    getMapInfo(value) {
      const { areaId, areaLevel } = value;
      if (areaLevel === 1) {
        this.legendList = [
          { color: "#FF393980", borderColor: "#FF8787", text: "≤50万" },
          { color: "#FF838380", borderColor: "#FFBCB9", text: "50万-100万" },
          { color: "#37D89280", borderColor: "#A9FFDA", text: "100万-200万" },
          { color: "#A9FFDA80", borderColor: "#A9FFDA", text: "≥200万" },
        ];
      } else {
        this.legendList = [
          { color: "#FF393980", borderColor: "#FF8787", text: "≤10万" },
          { color: "#FF838380", borderColor: "#FFBCB9", text: "10万-20万" },
          { color: "#37D89280", borderColor: "#A9FFDA", text: "20万-50万" },
          { color: "#A9FFDA80", borderColor: "#A9FFDA", text: "≥50万" },
        ];
      }
      this.currentArea = value;

      this.selectForm[
        areaLevel === '2' ? 'cityCode'
          : areaLevel === '3' ? 'countyCode'
            : areaLevel === '4' ? 'gridCode'
              : 'provinceCode'
      ] = areaId

      getMapInfo({
        areaId,
        areaLevel,
      }).then(async ({ code, data }) => {
        if (code === 200) {
          this.getAreaListInfo(data)
          await this.$refs.echartsMap.initMapData(data);
          if (areaLevel !== 1) return false;
          this.areaOpts = [{ areaId: "999", areaLevel: 1, areaName: "全省" }];
          this.areaMap = {
            999: { areaId: "999", areaLevel: 1, areaName: "全省" },
          };
          data.children.map((item) => {
            this.areaMap[item.areaId] = item;
            this.areaOpts.push({
              areaId: item.areaId,
              areaLevel: item.areaLevel,
              areaName: item.areaName,
            });
          });
        }
      });
    },
    getAreaListInfo(data) {
      console.log(data);
      const { areaLevel, children } = data
      if (areaLevel == '1') {
        this.selectgridList.cityList = children || []
        this.selectgridList.countyList = []
        this.selectgridList.gridList = []
      } else if (areaLevel == '2') {
        this.selectgridList.countyList = children || []
        this.selectgridList.gridList = []
      } else if (areaLevel == '3') {
        this.selectgridList.gridList = children || []
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.select-choice {
  & > span {
    color: #fff;
    margin: 0 8px;
  }

  ::v-deep .el-input__inner {
    width: vw(175);
    height: vh(32);
    line-height: vh(32);
    background-color: transparent;
    border-color: #727687;
    border-radius: 4px;
    font-size: vw(14);
    color: #b4b9bf !important;
  }
  ::v-deep .el-form-item__label {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(16);
    color: #ffffff;
    padding-right: vw(10);
  }
  ::v-deep .el-form-item {
    margin-right: wv(10);
    margin-bottom: vh(10);
  }
}

.index-insight {
  .right-top-handle-info {
    position: absolute;
    top: vh(-15);
    right: vw(30);
    display: flex;
    align-items: center;
    z-index: 999;
    font-size: vw(14) !important;

    .index-types {
      display: flex;

      .type-item {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: vw(14);
        line-height: vh(32);
        padding: 0 vw(20);
        color: #8ac0ff;
        cursor: pointer;
        background: linear-gradient(
          180deg,
          rgba(15, 27, 52, 0.5) 0%,
          rgba(46, 97, 199, 0.5) 100%
        );
        position: relative;
        border: 2px solid transparent;
        border-top: 2px solid #111421;

        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: -2px;
          width: calc(100% + 4px);
          height: 2px;
          background: linear-gradient(
            90deg,
            rgba(106, 145, 197, 0.4) 0%,
            rgba(170, 212, 255, 0.8) 49.52%,
            rgba(106, 145, 197, 0.4) 100%
          );
        }

        margin-right: 1px;

        &.active {
          color: #d9f7ff;
          background: linear-gradient(180deg, #2669ef 0%, #142d60 100%);
          border: 2px solid transparent;
          border-image: linear-gradient(
              90deg,
              #def1fe 3.37%,
              rgba(222, 241, 254, 0) 100%
            )
            1;
        }
      }
    }

    .handle-form {
      display: flex;

      .area-chose {
        margin-right: vw(10);
        //width: vw(140);
        //height: vh(32);
      }

      .time-chose {
        display: flex;
        align-items: center;
        & > span {
          color: #fff;
          margin: 0 8px;
          white-space: nowrap;
        }
        //width: vw(140);
        height: vh(32);

        ::v-deep .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          width: 100%;
          //height: vh(32);
        }

        ::v-deep .el-input__inner {
          background: none;
          height: vh(32);
          line-height: vh(32);
          font-size: vw(14);
          width: vw(175);
          border-color: #727687;
          color: #b4b9bf !important;
        }

        ::v-deep .el-select .el-input .el-select__caret {
          font-size: vw(14);
        }

        ::v-deep .el-input__icon {
          height: vh(14);
          line-height: vh(14);
        }

        ::v-deep .el-input__prefix {
          line-height: vh(32);
        }

        ::v-deep .el-select-dropdown__item {
          line-height: vh(32);
          height: vh(32);
          font-size: vw(14);
        }
      }
    }
  }

  .left-business-customer {
    position: absolute;
    top: vh(10);
    left: vw(30);
    z-index: 998;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .right-fight-customer {
    position: absolute;
    top: vh(10);
    right: vw(30);
    z-index: 998;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .echarts-map {
    position: absolute;
    left: vw(500);
    top: vh(32);
    bottom: vh(30);
    right: vw(500);
    z-index: 997;
  }

  .echarts-map-back {
    position: absolute;
    left: vw(130);
    top: vh(32);
    bottom: vh(30);
    right: vw(130);
    z-index: 996;
    background-image: url("../../assets/images/indexInsight/echarts-back.png");
    background-size: 100% 100%;
  }
}
</style>
<style>
.pop-date-cls {
  .el-date-table td.disabled div {
    background-color: #a3a3a4;
  }
}

.el-select-dropdown {
  background-color: #142a4e;
}

.el-select-dropdown__item {
  background-color: #142a4e;
  color: #afafaf;
}

.el-select-dropdown__item.selected {
  background-color: #041c4d;
  color: white;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #6a95f6;
  color: white;
}

.el-picker-panel {
  background-color: #142a4e;
}
</style>
<style lang="scss">
@import "~@/views/operation/index.scss";
.custom-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #141e32 !important;
    }
  }
  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    height: vh(34);
    line-height: vh(34);
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}
</style>
