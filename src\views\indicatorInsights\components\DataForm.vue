<template>
  <div class="data-form">
    <div class="table-box">
      <div class="content">
        <el-table class="table" 
          :cell-class-name="cellClassName" 
          :header-cell-class-name="headerCellClassName"
          :data="flattenData" style="width: 100%" :span-method="handleSpan">
          <el-table-column label="模块" prop="module" />
          <el-table-column label="分类" prop="category">
          </el-table-column>
          <el-table-column label="指标" prop="indicator" />
          <el-table-column label="数值类型（数值/率）" prop="value" />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "DataForm",
  props: {
    indicatorData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      spanArr: [],
      tableData: [
        {
          module: '竞对压制', // 模块名称
          categories: [
            {
              category: '双频精准压制', // 分类名称
              items: [
                { indicator: '净增', value: this.indicatorData.sxjzyzJz || 'xx', },
                { indicator: '新增', value: this.indicatorData.sxjzyzXz || 'xx', },
                { indicator: '份额低占区域压制', value: this.indicatorData.sxjzyzDzyz || 'xx' },
                { indicator: '39大流量主卡', value: this.indicatorData.sxjzyz39LlMain || 'xx', },
                { indicator: '39大流量副卡', value: this.indicatorData.sxjzyz39LlBak || 'xx', },
                { indicator: '外出营销场次', value: this.indicatorData.sxjzyzYxcc || 'xx', },
              ]
            }
          ]
        },
        {
          module: '爱家计划',
          categories: [
            {
              category: '爱家进攻',
              items: [
                { indicator: '家庭宽带', value: this.indicatorData.kdjgJtkd || 'xx', },
                { indicator: '金牛', value: this.indicatorData.kdjgJn || 'xx', },
                { indicator: 'CC', value: this.indicatorData.kdjgCc || 'xx', },
                { indicator: '金虎比', value: this.indicatorData.kdjgJhb || 'xx' },
              ]
            },
            {
              category: '爱家升档',
              items: [
                { indicator: '爱家升档', value: this.indicatorData.ajsdAjsd || 'xx' },
                { indicator: '其中：升1享2', value: this.indicatorData.ajsdS1x2 || 'xx', },
                { indicator: '其中：爱家流量包', value: this.indicatorData.ajsdAjllb || 'xx', },
                { indicator: '其中：尊享百G包', value: this.indicatorData.ajsdZxbjb || 'xx', },
              ]
            },
            {
              category: '爱家加固',
              items: [
                { indicator: '整体修复', value: this.indicatorData.ajjgZtxf || 'xx' },
                { indicator: '其中：高风险即将流失', value: this.indicatorData.ajjgGfxls || 'xx', },
                { indicator: '其中：次高风险2个月后流失', value:this.indicatorData.ajjgCgfxls || 'xx', },
                { indicator: '其中：当年新增异网双卡', value: this.indicatorData.ajjgXzywsk || 'xx', },
              ]
            },
            {
              category: '爱家福袋',
              items: [
                { indicator: '爱家福袋', value: this.indicatorData.ajfdAjfd || 'xx' },
              ]
            },
            {
              category: '爱家融合',
              items: [
                { indicator: '畅享包', value: this.indicatorData.ajrhWlhk || 'xx', },
                { indicator: '网络回馈', value: this.indicatorData.ajrhCxb || 'xx', },
              ]
            }
          ]
        },
        {
            module: '雷霆拉新',
            categories: [
          {
              category: '拉新', // 分类名称
              items: [
                { indicator: '金牛CC拉新', value: this.indicatorData.lxJn || 'xx', },
                { indicator: '家庭异网拉新', value: this.indicatorData.lxZdkw || 'xx', },
                { indicator: '终端卡位拉新', value: this.indicatorData.lxCc || 'xx' },
              ]
            }
            ]
        },
        {
          module: '网络覆盖',
          categories: [
           {
              category: '进攻',
              items: [
                { indicator: '新开站点数', value: this.indicatorData.newCellCnt || 'xx', },
                { indicator: '网络优势区域数', value: 'xx', },
              ]
            },
            {
              category: '防守',
              items: [
                { indicator: '流量风险区域数', value: this.indicatorData.deCellCnt || 'xx' },
                { indicator: '无线网络劣势区域数', value: 'xx', },
                { indicator: '有线宽带劣势区域数', value: 'xx', },
              ]
            },
          ]
        }
      ],
      flattenData: [],
      spanConfig: { // 存储合并配置：moduleSpan、categorySpan
        module: [],
        category: []
      }
    }
  },
  mounted() {
    this.flattenData = this.flattenTableData();
    this.calculateSpan();
  },
  methods: {
    cellClassName() {
        return 'cell-style'
    },
    headerCellClassName() {
        return 'header-common';
    },
    flattenTableData() {
      const result = [];
      this.tableData.forEach(module => {
        module.categories.forEach(categories => {
          categories.items.forEach(item => {
            result.push({
              module: module.module,
              category: categories.category,
              ...item
            });
          });
        });
      });
      return result;
    },
    calculateSpan() {
      const moduleMap = {}; // 统计每个模块的行数
      const categoryMap = {}; // 统计每个分类的行数

      // 1. 统计模块、分类的总行数
      this.flattenData.forEach((item, index) => {
        const moduleKey = item.module;
        const categoryKey = `${item.module}-${item.category}`;

        // 初始化模块统计
        if (!moduleMap[moduleKey]) {
          moduleMap[moduleKey] = {
            startIndex: index,
            count: 0
          };
        }
        moduleMap[moduleKey].count++;

        // 初始化分类统计
        if (!categoryMap[categoryKey]) {
          categoryMap[categoryKey] = {
            startIndex: index,
            count: 0
          };
        }
        categoryMap[categoryKey].count++;
      });

      // 2. 生成合并配置（moduleSpan、categorySpan）
      this.spanConfig.module = new Array(this.flattenData.length).fill(0);
      this.spanConfig.category = new Array(this.flattenData.length).fill(0);

      Object.keys(moduleMap).forEach(key => {
        const { startIndex, count } = moduleMap[key];
        this.spanConfig.module[startIndex] = count;
      });

      Object.keys(categoryMap).forEach(key => {
        const { startIndex, count } = categoryMap[key];
        this.spanConfig.category[startIndex] = count;
      });
    },

    // 🔵 方法3：Element-UI 合并单元格回调
    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 模块列
        const span = this.spanConfig.module[rowIndex];
        return span > 0 ? [span, 1] : [0, 0];
      }
      if (columnIndex === 1) { // 分类列
        const span = this.spanConfig.category[rowIndex];
        return span > 0 ? [span, 1] : [0, 0];
      }
      return [1, 1]; // 指标、数值列不合并
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";

.data-form {
  width: 100%;
  height: 100%;
  padding-top: vh(34);

  .table-box {
    width: vw(1840);
    height: vh(880);
    margin: 0 auto;
    border-radius: vw(10);
    padding: vw(2);
    background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));

    .content {
      width: 100%;
      height: 100%;
      border-radius: vw(8);
      /* 比外层小2px，避免露出底色 */
      background: #13274a;
      padding-top: vh(20);

      ::v-deep .el-table::before {
        height: 0;
      }
      ::v-deep .el-table {
        width: vw(1800) !important;
        height: vh(840) !important;
        overflow: auto;
        margin: 0 auto;
        .header-common {
            height: vh(48);
            background-color: #265282 !important;
            /* 强制生效 */
            color: rgba(228, 238, 255, 1) !important;
            border: vw(1) solid rgba(230, 235, 240, 0.2) !important;
            border-top: 0;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(14);
            line-height: vh(22);
            padding: 0 !important;
        }
        .cell-style {
            width: vw(198);
            height: vh(48);
            background-color: #204269 !important;
            color: rgba(228, 238, 255, 1) !important;
            border: vw(1) solid rgba(230, 235, 240, 0.2);
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(14);
            line-height: vh(22);
            padding: 0 !important;
        }
      }
    }
  }
}
</style>