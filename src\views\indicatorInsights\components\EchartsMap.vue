<template>
  <div class="echartsMap">
    <div id="indicator-map-wrap" />
  </div>
</template>
<script>
import { fitChartSize } from '@/utils/dataUtil.js'
import * as echarts from 'echarts'
import common from '@/api/common'
import market from '@/api/market'
export default {
  name: 'EchartsMap',
  props: {
    indicatorActive: {
      type: String,
      default: ''
    },
    dataTime: {
      type: String,
      default: new Date().toLocaleDateString()
    },
    dataCycle: {
      type: String,
      default: '日'
    }
  },
  data() {
    return {
      mapChart: null,
      map: null,
      mapJson: {}, // 地图边界值
      mapBorderJson: {}, // 地图白色边框以及地图底层边界值
      mapData: {}, // 地图悬浮数据
      geoCoordMap: {}, // 悬浮数据中心点
      currentArea: {},
      indicatorData: [],
      indicatorValue: [],
      regionsLabel: {}, //色阶划分
      regionsNumber: {},
      // indicatorActive: '',
    }
  },
  watch: {
  },
  async mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.mapChart) {
      this.mapChart.dispose()
    }
  },
  methods: {
    //获取不同指标的数据
    //t=1,切换指标，t=2,切换时间周期,t=3,切换时间
    async getIndicatorInsightByPid(t,data) {
      if (t === 1) {
        this.indicatorActive = data
      }
      if (t === 2) {
        this.dataCycle = data
      }
      if (t === 3) {
        this.dataTime = data
      }
      await market.getIndicatorInsightByPid({
        statDate: this.dataTime,
        areaPid: this.currentArea.areaId,
        areaLevel: this.currentArea.areaLevel-1,
        kpiName: this.indicatorActive,
        indexType:  this.dataCycle
      }).then(res => {
        if(res.code === 200) {
          this.indicatorData = res.data;
          this.indicatorValue = this.indicatorData.reduce((map, item) => {
            // 假设customerNumberData中的每个项有regionCode和count属性
            map[item.areaCode] = item.kpiValue;
            return map;
          }, {});
          if (this.indicatorActive === 'sxjzyzDzyz' || this.indicatorActive === 'kdjgJhb') {
            this.regionsLabel = {
              label1: `<0.25` ,
              label2: `0.25 - 0.5` ,
              label3: `0.5 - 0.75` ,
              label4: `≥ 0.75` 
            };
            this.regionsNumber = {
              number1: 0.25,
              number2: 0.5,
              number3: 0.75,
            }
            const legendList = { 
              label1: '<25%' ,
              label2: '25%-50%',
              label3: '50%-75%', 
              label4: '>75%' 
            }
            this.$emit('updadeLegendList', legendList)
          } else {
            if (this.indicatorData.length > 0) { 
              const maxItem = this.indicatorData.reduce((prev, current) => {
                return (prev.kpiValue > current.kpiValue) ? prev : current;
              });
              const minItem = this.indicatorData.reduce((prev, current) => {
                return (prev.kpiValue < current.kpiValue) ? prev : current;
              });
              console.log(maxItem, minItem)
              const totalDiff = maxItem.kpiValue - minItem.kpiValue;
              const interval = totalDiff / 4;
              const region1End = Math.round(minItem.kpiValue + interval);
              const region2End = Math.round(minItem.kpiValue + interval * 2);
              const region3End = Math.round(minItem.kpiValue + interval * 3);
              this.regionsLabel = {
                label1: `< ${region1End}` ,
                label2: `${region1End} - ${region2End}` ,
                label3: `${region2End} - ${region3End}` ,
                label4: `≥ ${region3End}` 
              };
              this.regionsNumber = {
                number1: region1End,
                number2: region2End,
                number3: region3End,
              }
              this.$emit('updadeLegendList', this.regionsLabel)
            }
          }
          if (t===1 || t===2 || t===3) {
            this.mapData = this.mapData.map(item => {
              return {
                ...item, 
                value: this.indicatorValue[item.areaId] ?? 0 
              };
            });
            this.$nextTick(() => {
              this.initMapEcharts(this.mapData)
            })
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },

    //获取区域边界 获取云南省0，1
    async getShengArea() {
      await common.getDimAreaByPid({
        areaPid: 0,
        areaLevel: 1
      }).then(async (res) => {
        if (res.code === 200) {
          this.mapBorderJson = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {
                  name: res.data[0].area_name
                },
                geometry: {
                  type: 'MultiPolygon',
                  coordinates: this.polygonPath(res.data[0].area_location)
                }
              }
            ]
          }
        }
      })
    },
    //获取地图边界
    async getDimAreaByPid(params) {
      this.currentArea = params
      await this.getIndicatorInsightByPid()
      await common.getDimAreaByPid({
        areaPid: params.areaId,
        areaLevel: params.areaLevel
      }).then(async ({ code, data }) => {
        if (code === 200) {
          this.mapJson = {
            type: 'FeatureCollection',
            features: this.polygonFullPath(data)
          }
          this.geoCoordMap = this.getGeoCoordMap(data)
          this.mapData = data.map(item => {
            return {
              name: item.area_name,
              areaId: item.area_id,
              areaLevel: params.areaLevel+1,
              centralPoint: item.central_point,
              value: this.indicatorValue[item.area_id] ?? 0
            }
          })
          this.$nextTick(() => {
            this.initMapEcharts(this.mapData)
          })
        }
      })
    },
    /**
     * @description 格式化白色边框边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonPath(data) {
      const area = data.split('|')
      const areaMap = area.map(item => {
        const points = data.split(';')
        const polygon = points.map(item => {
          const [lon, lat] = item.split(',')
          return [parseFloat(lon.trim()), parseFloat(lat.trim())]
        })
        return polygon
      })
      return [areaMap]
    },
    /**
     * @description 格式化边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonFullPath(data) {
      return data.map(item => {
        return {
          type: 'Feature',
          properties: {
            name: item.area_name
          },
          geometry: {
            type: 'MultiPolygon',
            coordinates: this.polygonPath(item.area_location)
          }
        }
      })
    },
    /**
     * @description 获取悬浮中心点
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    getGeoCoordMap(arr) {
      const result = {}
      arr.forEach(item => {
        const [longitude, latitude] = item.central_point.split(',').map(parseFloat)
        result[`${item.area_name}`] = [longitude, latitude]
      })
      return result
    },
    scatterData2() {
      return datas.map((item) => ({
        name: item.name,
        value: this.geoCoordMap[item.name]
      }))
    },
    async initMapEcharts(datas) {
      if (this.mapChart) {
        this.mapChart.dispose()
      }
      this.mapChart = echarts.init(document.getElementById('indicator-map-wrap'))
      echarts.registerMap('map', this.mapJson)
      echarts.registerMap('mapBorder', this.mapBorderJson)
      this.mapBorderJson = {}
      this.mapChart.off('click')
      const option = this.getOption(datas)
      this.mapChart.setOption(option)
      this.mapChart.on('click', (params) => {
        console.log(params.data)
        // if (params.data.value <= 0) return
        if (params.data.areaLevel < 6) { //行政村边界为6，继续加载边界无数据
          this.$emit('updataCode', params.data) //6是行政村，5是网格，4是区县，3是地市
          // this.getDimAreaByPid(params.data)
        } else {
          this.currentArea = params.data
        }
      })
    },
    getOption(datas) {
      var that = this
      console.log(datas)
      function scatterData2() {
        return datas.map((item) => ({
          name: item.name,
          areaId: item.areaId,
          value: that.geoCoordMap[item.name]
        }))
      }
      console.log(this.regionsLabel)
      return {
        geo: [
          // 第一层地图
          {
            map: 'map',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              borderColor: '#D8E6FF4D',
              borderWidth: 1
            },
            emphasis: {
              itemStyle: {
                areaColor: 'transparent'
              },
              label: {
                show: 0,
                color: '#fff'
              }
            },
            zlevel: 3,
            roam: true, // 核心：开启缩放和平移（拖动）
          },
          // 第二层地图和第一层重叠用作边框
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#F7F8FF',
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.25)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: fitChartSize(3.72)
            },
            zlevel: 2,
            silent: true
          },
          // 第三层地图底层
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '52%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#B6C1FB99',
              borderWidth: 1
            },
            zlevel: 1,
            silent: true
          }
        ],
        visualMap: {
          show: false,
          type: 'piecewise',
          bottom: fitChartSize(40),
          right: fitChartSize(40),
          itemGap: fitChartSize(10),
          align: 'left',
          itemWidth: fitChartSize(16),
          itemHeight: fitChartSize(12),
          textStyle: {
            fontSize: fitChartSize(14),
            color: '#EDEDED'
          },
          seriesIndex: 0,
          pieces: [
            {
              lt: this.regionsNumber.number1,
              color: '#FF393980',
              label: this.regionsLabel.label1
            },
            {
              gte: this.regionsNumber.number1,
              lte: this.regionsNumber.number2,
              color: '#FF838380',
              label: this.regionsLabel.label2
            },
            {
              gte: this.regionsNumber.number2,
              lt: this.regionsNumber.number3,
              color: '#37D89280',
              label: this.regionsLabel.label3
            },
            {
              gte: this.regionsNumber.number3,
              color: '#A9FFDA80',
              label: this.regionsLabel.label4
            }
          ]
        },
        series: [
          {
            type: 'map',
            selectedMode: false,
            map: 'map',
            geoIndex: 0,
            data: datas,
          },
          // 地市弹框撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: '{b}',
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#fff',
              fontSize: fitChartSize(20),
              fontWeight: '500',
              fontFamily: '',
              show: true
            },
            symbol: 'rect',
            // symbolSize: function(data, params) {
            //   const name = params.data.name
            //   const nameLength = name.length || 0
            //   const width = fitChartSize(nameLength * 20)
            //   const height = fitChartSize(30)
            //   return [width, height]
            // },
            symbolOffset: [0, fitChartSize(-30)],
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              borderWidth: fitChartSize(0.5),
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
          // 地市对应值撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: function (name) {
                const item = datas.find(item => item.name === name.name)
                if (!item) return ''
                
                // 判断是否为百分比指标，如果是则转换为百分比格式
                if (that.indicatorActive === 'sxjzyzDzyz' || that.indicatorActive === 'kdjgJhb') {
                  return item.value ? (Number(item.value * 100).toFixed(1)) + '%' : '0%'
                }
                
                return item.value || 0
              },
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#ffffff',
              fontSize: fitChartSize(23),
              fontFamily: 'Milibus',
              show: true
            },
            symbol: 'rect',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
        ]
      }
    },
    handleResize() {
      if (this.mapChart) {
        this.mapChart.resize()
        this.$nextTick(() => {
          const updatedOption = this.getOption(this.mapData)
          this.mapChart.setOption(updatedOption)
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "~@/views/market/index.scss";
#indicator-map-wrap {
  position: relative;
  margin-top: vh(52);
  margin-left: vw(523);
  width: vw(865);
  height: vh(813);
  // background-color: antiquewhite;
  z-index: 98;
}
</style>
