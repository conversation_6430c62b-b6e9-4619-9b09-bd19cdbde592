<template>
  <div class="data-box">
    <div class="left-box">
      <div class="title title1"></div>
      <div class="common box1">
        <div class="content">
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">双限精准压制</div>
          </div>
          <div class="indicator1">
            <div class="container" v-for="item in indicatorList1" :key="item.id"
              :class="{ 'active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
              <div :class="[item.id % 2 === 0 ? 'icon2' : 'icon']"></div>
              <div class="text">
                <div class="name">{{ item.name }}</div>
                <div class="number"><span>{{ item.value || '--' }}</span>{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="title title2"></div>
      <div class="common box2">
        <div class="content">
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">拉新</div>
          </div>
          <div class="indicator2">
            <div class="bg" v-for="item in indicatorList2" :key="item.id"
              :class="{ 'active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
              <div class="text">
                <div class="name">{{ item.name }}</div>
                <div class="number shadow">{{ item.value || '--' }}<span>{{ item.unit }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="title title3"></div>
      <div class="common box3">
        <div class="content">
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">网络覆盖</div>
          </div>
          <div class="top">
            <div class="container" v-for="item in indicatorList3" :key="item.id"
              :class="{ 'active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
              <div :class="[item.id === 10 ? 'icon' : 'icon2']"></div>
              <div class="text">
                <div class="name">{{ item.name }}</div>
                <div class="number"><span>{{ item.value || '--' }}</span>{{ item.unit }}</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="bg" v-for="item in indicatorList4" :key="item.id"
              :class="{ 'active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
              <div class="info">
                <div class="icon4-2">
                  <div class="icon-inner"></div>
                </div>
                <div class="text">
                  <div class="number"><span>{{item.value}}</span>{{ item.unit }}</div>
                  <div class="name">{{ item.name || '--' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-box">
      <div class="title"></div>
      <div class="common">
        <div class="content">
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">宽带进攻</div>
          </div>
          <div class="info1">
            <div class="container" v-for="item in data1" :key="item.id"
              :class="{ 'active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
              <div class="name">
                <div class="icon"></div>
                <span>{{ item.name }}</span>
              </div>
              <div class="number">
                <!-- <div class="bg"></div> -->
                <div class="text"><span>{{ item.value || '--' }}</span>{{ item.unit }}</div>
              </div>
            </div>
          </div>
          <div class="info2" :class="{ 'active': indicatorActive === 'kdjgJhb' }" @click="changeIndicator('kdjgJhb')">
            <div class="box">
              <div class="icon"></div>
              <div class="name">金虎比</div>
              <div class="number">{{ (this.indicatorData && this.indicatorData.kdjgJhb) ? (Number(this.indicatorData.kdjgJhb*100).toFixed(2)) : 0 }}%</div>
            </div>
          </div>
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">爱家升档</div>
          </div>
          <div class="info3">
            <div class="left-bg">
              <div class="box">
                <div class="name">爱家升档完成量</div>
                <div class="number"><span>{{ (this.indicatorData && this.indicatorData.ajsdAjsd) || 0 }}</span>人</div>
              </div>
            </div>
            <div class="right">
              <div class="text" v-for="item in data2" :key="item.id"
                :class="{ 'text-active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
                <div class="name">{{ item.name }}</div>
                <div class="number"><span>{{ item.value || '--' }}</span>{{ item.unit }}</div>
              </div>
            </div>
          </div>
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">爱家加固</div>
          </div>
          <div class="info3">
            <div class="left-bg">
              <div class="box">
                <div class="name">整体修复客户数</div>
                <div class="number"><span>{{ (this.indicatorData && this.indicatorData.ajjgZtxf) || 0 }}</span>人</div>
              </div>
            </div>
            <div class="right">
              <div class="text" v-for="item in data3" :key="item.id"
                :class="{ 'text-active': item.id === indicatorActive }" @click="changeIndicator(item.id)">
                <div class="name">{{ item.name }}</div>
                <div class="number"><span>{{ item.value || '--' }}</span>{{ item.unit }}</div>
              </div>
            </div>
          </div>
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">爱家福袋</div>
          </div>
          <div class="info4" :class="{ 'active': indicatorActive === 'ajfdAjfd' }" @click="changeIndicator('ajfdAjfd')">
            <div class="box">
              <div class="icon"></div>
              <div class="name">爱家福袋完成量</div>
              <div class="number"><span>{{ (this.indicatorData && this.indicatorData.ajfdAjfd) || 0 }}</span>个</div>
            </div>
          </div>
          <div class="topic">
            <div class="title-icon"></div>
            <div class="title-text">爱家融合</div>
          </div>
          <div class="info5">
            <div class="bg" v-for="item in data4" :key="item.id" :class="{ 'active': item.id === indicatorActive }"
              @click="changeIndicator(item.id)">
              <div class="box">
                <div :class="[item.id === 27 ? 'icon' : 'icon2']"></div>
                <div class="text">
                  <div class="name">{{ item.name }}</div>
                  <div class="number"><span>{{ item.value || '--' }}</span>{{ item.unit }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'IndicatorDashboard',
  props: {
    indicatorActive: {
      type: String,
      default: 0
    },
    indicatorData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      indicatorList1: [],  //双限精准压制
      indicatorList2: [],  //拉新
      indicatorList3: [  //网络覆盖
        {
          id: 10,
          name: '新开站点数',
          value: '--',
          unit: '个'
        },
        {
          id: 11,
          name: '网络优势区域数',
          value: '--',
          unit: '个'
        }
      ], 
      indicatorList4: [  ////网络覆盖
        {
          id: 12,
          name: '流量风险区域数',
          value: '--',
          unit: '个'
        },
        {
          id: 13,
          name: '无线网络劣势区域数',
          value: '--',
          unit: '个'
        },
        {
          id: 14,
          name: '有线宽带劣势区域数',
          value: '--',
          unit: '个'
        }
      ],
      data1: [],   //宽带进攻
      data2: [],  //爱家升档
      data3: [],  //爱家加固
      data4: []  //爱家融合
    }
  },
  watch: {
    indicatorData: {
      handler(newData) {
        // 无论数据是否为空，都要更新指标列表以确保显示默认值
        this.updateIndicatorLists()
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // 组件挂载时初始化数据，确保有默认值显示
    this.updateIndicatorLists()
  },
  methods: {
    updateIndicatorLists() {
      // 防御性编程：确保 indicatorData 存在，如果不存在则使用空对象
      const data = this.indicatorData || {}
      
      // 更新双限精准压制数据
      this.indicatorList1 = [
        {
          id: 'sxjzyzJz',
          name: '净增',
          value: data.sxjzyzJz || 0,
          unit: '人'
        },
        {
          id: 'sxjzyzXz',
          name: '新增',
          value: data.sxjzyzXz || 0,
          unit: '人'
        },
        {
          id: 'sxjzyz39LlMain',
          name: '39大流量主卡',
          value: data.sxjzyz39LlMain || 0,
          unit: '个'
        },
        {
          id: 'sxjzyz39LlBak',
          name: '39大流量副卡',
          value: data.sxjzyz39LlBak || 0,
          unit: '个'
        },
        {
          id: 'sxjzyzDzyz',
          name: '份额低占区域',
          value: data.sxjzyzDzyz ? Number(data.sxjzyzDzyz*100).toFixed(2) : 0,
          unit: '%'
        },
        {
          id: 'sxjzyzYxcc',
          name: '外出营销场次',
          value: data.sxjzyzYxcc || 0,
          unit: '次'
        },
      ]

      // 更新拉新数据
      this.indicatorList2 = [
        {
          id: 'lxJn',
          name: '金牛CC拉新',
          value: data.lxJn || 0,
          unit: '人'
        },
        {
          id: 'lxZdkw',
          name: '家庭异网拉新',
          value: data.lxZdkw || 0,
          unit: '人'
        },
        {
          id: 'lxCc',
          name: '终端卡位拉新',
          value: data.lxCc || 0,
          unit: '人'
        },
      ]

      // 更新宽带进攻数据
      this.data1 = [
        {
          id: 'kdjgJtkd',
          name: '家庭宽带完成量',
          value: data.kdjgJtkd || 0,
          unit: ''
        },
        {
          id: 'kdjgJn',
          name: '金牛完成量',
          value: data.kdjgJn || 0,
          unit: ''
        },
        {
          id: 'kdjgCc',
          name: 'CC完成量',
          value: data.kdjgCc || 0,
          unit: ''
        }
      ]

      // 更新爱家升档数据
      this.data2 = [
        {
          id: 'ajsdS1x2',
          name: '升1享2',
          value: data.ajsdS1x2 || 0,
          unit: '人'
        },
        {
          id: 'ajsdAjllb',
          name: '爱家流量包',
          value: data.ajsdAjllb || 0,
          unit: '人'
        },
        {
          id: 'ajsdZxbjb',
          name: '尊享百G包',
          value: data.ajsdZxbjb || 0,
          unit: '人'
        }
      ]

      // 更新爱家加固数据
      this.data3 = [
        {
          id: 'ajjgGfxls',
          name: '高风险即将流失',
          value: data.ajjgGfxls || 0,
          unit: '人'
        },
        {
          id: 'ajjgCgfxls',
          name: '次高风险2个月后流失',
          value: data.ajjgCgfxls || 0,
          unit: '人'
        },
        {
          id: 'ajjgXzywsk',
          name: '当年新增异网双卡',
          value: data.ajjgXzywsk || 0,
          unit: '人'
        }
      ]

      // 更新爱家融合数据
      this.data4 = [
        {
          id: 'ajrhWlhk',
          name: '网龄回馈',
          value: data.ajrhWlhk || 0,
          unit: '人'
        },
        {
          id: 'ajrhCxb',
          name: '畅享包',
          value: data.ajrhCxb || 0,
          unit: '人'
        },
      ]
    },
    changeIndicator(id) {
      this.$emit('changeIndicator', id)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";
.data-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.left-box {
  position: absolute;
  top: 0;
  left: vw(40);
  width: vw(440);
  height: vh(955);
  padding-top: vh(22);
  .title {
    width: vw(440);
    height: vh(35);
    margin-bottom: vh(22);
  }
  .common {
    width: vw(440);
    // background-color: rgba(37, 65, 119, 1);
    padding: vw(2);
    border-radius: vw(24);
    background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));
    .content {
      width: 100%;
      height: 100%;
      border-radius: vw(22);
      /* 比外层小2px，避免露出底色 */
      background: #13274a;
      color: white;
      .topic {
        display: flex;
        justify-content: center;
        position: relative;
        padding-top: vh(9);
        top: vh(-1);
        width: vw(318);
        height: vh(49);
        margin: 0 auto;
        background: url("../../../assets/images/dashboard/title.png") no-repeat;
        background-size: 100% 100%;
        .title-icon {
          width: vw(24);
          height: vh(24);
          background: url("../../../assets/images/dashboard/title-icon.png") no-repeat;
          background-size: 100% 100%;
          margin-right: vw(9.03);
        }
        .title-text {
          width: fit-content;
          height: vh(29);
          line-height: vh(29);
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          font-size: vw(22);
          letter-spacing: vw(1.3);
        }
      }
      .active {
        background-color: rgba(44, 163, 255, 0.6);
        border-radius: vw(10);
      }
      .indicator1 {
        width: vw(384);
        height: vh(258);
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        .container {
          width: vw(192);
          height: vh(86);
          display: flex;
          align-items: center;
          padding-left: vw(13);
          display: flex;
          cursor: pointer;
          .icon {
            width: vw(64);
            height: vh(64);
            background: url("../../../assets/images/dashboard/data2-1.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon2 {
            width: vw(64);
            height: vh(64);
            background: url("../../../assets/images/dashboard/data2-2.png") no-repeat;
            background-size: 100% 100%;
          }
          .text {
            margin-left: vw(8);
            width: vw(100);
            // height: vh(58);
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(16);
            line-height: vh(24);
            color: rgba(255, 255, 255, 0.75);
            .number {
              height: vh(34);
              span {
                font-family: MiSans;
                font-weight: 630;
                font-style: Bold;
                font-size: vw(25.3);
                line-height: vh(34);
                text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
              }
            }
          }
        }
      }
      .indicator2 {
        width: vw(379);
        height: vh(113);
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        .bg {
          cursor: pointer;
          width: vw(379);
          height: vh(100);
          .text {
            width: vw(103);
            height: vh(113);
            margin: 0 auto;
            background: url("../../../assets/images/dashboard/data-bottom-icon.png") no-repeat;
            background-size: 100% 100%;
            .name {
              width: fit-content;
              height: vh(34);
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(16);
              line-height: vh(34);
              margin: 0 auto;
            }
            .number {
              width: fit-content;
              height: vh(38);
              font-family: MiSans;
              font-weight: 630;
              font-size: vw(28);
              line-height: vh(38);
              margin: 0 auto;
              text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
              span {
                display: inline-block;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: vw(16);
                line-height: vh(30);
                vertical-align: bottom;
                margin-left: vw(4);
                background: linear-gradient(0deg, rgba(239, 248, 254, 0.75), rgba(239, 248, 254, 0.75)),
                  linear-gradient(180deg, #EEF5FF 26.85%, #8AB6F8 72.22%);
                background-clip: text;
                color: transparent;
                text-shadow: none;
              }
            }
          }
        }
      }
      .top {
        width: vw(384);
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        .container {
          width: vw(192);
          height: vh(86);
          display: flex;
          align-items: center;
          padding-left: vw(13);
          display: flex;
          cursor: pointer;
          .icon {
            width: vw(64);
            height: vh(64);
            background: url("../../../assets/images/dashboard/data2-1.png") no-repeat;
            background-size: 100% 100%;
          }
          .icon2 {
            width: vw(64);
            height: vh(64);
            background: url("../../../assets/images/dashboard/data2-2.png") no-repeat;
            background-size: 100% 100%;
          }
          .text {
            margin-left: vw(8);
            width: vw(100);
            // height: vh(58);
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(14);
            line-height: vh(20);
            color: rgba(228, 238, 255, 1);
            .number {
              height: vh(34);
              span {
                font-family: MiSans;
                font-weight: 630;
                font-style: Bold;
                font-size: vw(25.3);
                line-height: vh(34);
                text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
              }
            }
          }
        }
      }
      .bottom {
        width: vw(367);
        height: vh(84);
        // background-color: blueviolet;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        .bg {
          width: vw(120);
          height: vh(105);
          cursor: pointer;
          .info {
            width: vw(100);
            height: vh(82);
            position: relative;
            margin: 0 auto;
            .icon4-2 {
              position: absolute;
              width: vw(49);
              height: vh(49);
              background: url("../../../assets/images/dashboard/data4-out-circle.png") no-repeat;
              background-size: 100% 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              .icon-inner {
                width: vw(30);
                height: vh(30);
                background: url("../../../assets/images/dashboard/data4-inner-circle.png") no-repeat;
                background-size: 100% 100%;
              }
            }
            .text {
              width: 100%;
              height: vh(69);
              position: absolute;
              bottom: 0;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(14);
              line-height: vh(20.75);
              color: rgba(181, 211, 255, 1);
              padding-left: vw(21);
              .number {
                height: vh(44);
                span {
                  display: inline-block;
                  color: white;
                  margin-right: vw(9);
                  font-family: MiSans;
                  font-weight: 630;
                  font-size: vw(40);
                  line-height: vh(44);
                  text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
                }
              }
              .name {
                width: vw(80);
                font-size: vw(16);
              }
            }
          }
        }
      }
    }
  }
  .box1 {
    height: vh(339);
    margin-bottom: vh(8);
  }
  .box2 {
    height: vh(156);
    margin-bottom: vh(8);
  }
  .box3 {
    height: vh(250);
  }
  .title1 {
    background: url("../../../assets/images/market/left-1.png") no-repeat;
    background-size: 100% 100%;
  }
  .title2 {
    background: url("../../../assets/images/market/left-2.png") no-repeat;
    background-size: 100% 100%;
  }
  .title3 {
    background: url("../../../assets/images/market/left-3.png") no-repeat;
    background-size: 100% 100%;
  }
}
.right-box {
  position: absolute;
  top: vh(22);
  right: vw(40);
  width: vw(440);
  height: vh(955);
  .title {
    width: vw(440);
    height: vh(35);
    margin-bottom: vh(22);
    background: url("../../../assets/images/market/right-2.png") no-repeat;
    background-size: 100% 100%;
  }
  .common {
    width: vw(440);
    height: vh(875);
    // background-color: rgba(37, 65, 119, 1);
    padding: vw(2);
    border-radius: vw(24);
    background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));
    .content {
      width: 100%;
      height: 100%;
      border-radius: vw(22);
      /* 比外层小2px，避免露出底色 */
      background: #13274a;
      color: white;
      .topic {
        display: flex;
        justify-content: center;
        position: relative;
        padding-top: vh(9);
        top: vh(-1);
        width: vw(318);
        height: vh(49);
        margin: 0 auto;
        background: url("../../../assets/images/dashboard/title.png") no-repeat;
        background-size: 100% 100%;
        .title-icon {
          width: vw(24);
          height: vh(24);
          background: url("../../../assets/images/dashboard/title-icon.png") no-repeat;
          background-size: 100% 100%;
          margin-right: vw(9.03);
        }
        .title-text {
          width: fit-content;
          height: vh(29);
          line-height: vh(29);
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          font-size: vw(22);
          letter-spacing: vw(1.3);
        }
      }
      .active {
        background-color: rgba(44, 163, 255, 0.6);
        border-radius: vw(10);
      }
      .info1 {
        width: vw(407);
        height: vh(50);
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        margin-bottom: vh(14);
        .container {
          width: vw(125);
          height: vh(55);
          cursor: pointer;
          .name {
            display: flex;
            align-items: center;
            .icon {
              width: vw(14);
              height: vh(12);
              background: url("../../../assets/images/market/right-icon.png") no-repeat;
              background-size: 100% 100%;
              margin-right: vw(6);
            }
            span {
              display: inline-block;
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(14);
              line-height: vh(20);
            }
          }
          
          .number {
            position: relative;
            // width: vw(84);
            height: vh(32);
            .bg {
              position: absolute;
              left: vw(5);
              bottom: 0;
              width: vw(89);
              height: vh(22);
              background: url("../../../assets/images/market/right-bg1.png") no-repeat;
              background-size: 100% 100%;
            }
            .text {
              position: absolute;
              left: vw(14);
              bottom: vh(3);
              width: fit-content;
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(14);
              line-height: vh(20);
              span {
                font-family: MiSans;
                font-weight: 630;
                font-size: vw(22);
                line-height: vh(29);
                text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
              }
            }
          }
        }
      }
      .info2 {
        width: vw(173);
        height: vh(35);
        margin: 0 auto;
        cursor: pointer;
        .box {
          width: vw(163);
          height: vh(32);
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .icon {
            width: vw(20);
            height: vh(20);
            background: url("../../../assets/images/dashboard/people-icon.png") no-repeat;
            background-size: 100% 100%;
          }
          .name {
            width: vw(63);
            font-family: PingFang SC;
            font-weight: 600;
            font-size: vw(20);
            line-height: vh(24);
          }
          .number {
            width: vw(70);
            font-family: MiSans;
            font-weight: 630;
            font-size: vw(24);
            line-height: vh(30);
            text-align: right;
            text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
          }
        }
      }
      .info3 {
        display: flex;
        align-items: center;
        height: vh(190);
        width: vw(440);
        .left-bg {
          position: relative;
          left: vw(-25);
          width: vw(311);
          height: vh(190);
          background: url("../../../assets/images/market/right-bg2.png") no-repeat;
          background-size: 100% 100%;
          .box {
            position: absolute;
            top: vh(70);
            left: vw(55);
            width: vw(98);
            height: vh(54);
          }
          .name {
            width: fit-content;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: vw(14);
            line-height: vh(22);
            text-align: center;
          }
          .number {
            width: vw(98);
            height: vh(32);
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(12);
            line-height: vh(24);
            text-align: center;
            span {
              font-family: MiSans;
              font-weight: 630;
              font-size: vw(24);
              line-height: vh(32);
              text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
            }
          }
        }
        .right {
          position: absolute;
          right: vw(17);
          width: vw(221);
          height: vh(146);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          // background-color: aliceblue;
          .text {
            width: vw(221);
            height: vh(42);
            background: url("../../../assets/images/market/right-bg3.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-left: vw(13);
            padding-right: vw(6);
            cursor: pointer;
            .name {
              font-family: PingFang SC;
              font-weight: 600;
              font-size: vw(14);
              line-height: vh(20);
            }
            .number {
              font-family: PingFang SC;
              font-weight: 400;
              font-size: vw(14);
              line-height: vh(22);
              span {
                font-family: MiSans;
                font-weight: 630;
                font-size: vw(16);
                line-height: vh(21);
                text-align: right;
                text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
              }
            }
          }
          .text-active {
            background: url("../../../assets/images/market/right-bg4.png") no-repeat;
            background-size: 100% 100%;
          }
        }
      }
      .info4 {
        width: vw(250);
        height: vh(65);
        margin: 0 auto;
        cursor: pointer;
        .box {
          width: vw(245);
          height: vh(60);
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .icon {
            width: vw(60);
            height: vh(60);
            background: url("../../../assets/images/market/right-bg5.png") no-repeat;
            background-size: 100% 100%;
          }
          .name {
            width: vw(98);
            font-family: PingFang SC;
            font-weight: 600;
            font-size: vw(14);
            line-height: vh(20);
          }
          .number {
            text-align: right;
            span {
              font-family: MiSans;
              font-weight: 630;
              font-size: vw(24);
              line-height: vh(32);
              text-shadow: 0px 2.3px 8.05px rgba(34, 141, 188, 0.8);
            }
          }
        }
      }
      .info5 {
        width: vw(298);
        height: vh(64);
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        // padding-left: vw(71);
        .bg {
          cursor: pointer;
          width: max-content;
          height: vh(68);
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 vw(10);
          .box {
            width: fit-content;
            height: vh(64);
            display: flex;
            align-items: center;
            .icon {
              width: vw(64);
              height: vh(64);
              background: url("../../../assets/images/dashboard/data2-1.png") no-repeat;
              background-size: 100% 100%;
            }
            .icon2 {
              width: vw(64);
              height: vh(64);
              background: url("../../../assets/images/dashboard/data2-2.png") no-repeat;
              background-size: 100% 100%;
            }
            .text {
              height: vh(54);
              margin-left: vw(8);
              .name {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: vw(14);
                line-height: vh(20);
                width: vw(56);
              }
              .number {
                // position: absolute;
                width: max-content;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(16);
                line-height: vh(24);
                span {
                  font-family: MiSans;
                  font-weight: 630;
                  font-size: vw(16);
                  line-height: vh(34);
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>