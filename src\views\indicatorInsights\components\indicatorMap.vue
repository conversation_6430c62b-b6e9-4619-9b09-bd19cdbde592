<template>
  <div class="IndictaorHeatMap">
    <div class="echarts-map-back"></div>
    <EchartsMap ref="echartsMap" 
      @updataCode="updataCode" 
      v-show="menuActive === 1" 
      :indicatorActive="indicatorActive" 
      :dataTime="dataTime" 
      :dataCycle="dataCycle"
      @updadeLegendList="updadeLegendList"/>
    <!-- <div class="operation-tabs">
      <div v-for="item in menu" :key="item.id" :class="{ 'operation-tabs-active': item.id === menuActive }"
        class="operation-tabs-item" @click="navClick(item)">{{ item.name }}</div>
    </div> -->
    <div class="back" @click="backMap" v-if="areaLevel !== 1">
        <div class="icon"></div>
        <div>返回</div>
    </div>
    <div class="search">
      <el-select v-model="selectForm.cityCode" :clearable="false" placeholder="全省" popper-class="indicator-select"
        size="mini" @change="onCityChange" :disabled="userLevel > 1">
        <el-option v-for="item in selectAreaList.cityList" :key="item.areaId" :label="item.areaName"
          :value="item.areaId" />
      </el-select>
      <el-select v-model="selectForm.countyCode" :clearable="false" placeholder="区县" popper-class="indicator-select"
        size="mini" @change="onCountyChange" :disabled="userLevel > 2">
        <el-option v-for="item in selectAreaList.countyList" :key="item.areaId" :label="item.areaName"
          :value="item.areaId" />
      </el-select>
      <el-select v-model="selectForm.areacode" :clearable="false" placeholder="网格" popper-class="indicator-select"
        size="mini" @change="onAreaChange">
        <el-option v-for="item in selectAreaList.areaList" :key="item.areaId" :label="item.areaName"
          :value="item.areaId" />
      </el-select>
      <el-date-picker v-model="dataTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" @change="dataTimeChange" popper-class="dataTime-select">
      </el-date-picker>
      <el-select v-model="dataCycle" :clearable="false" placeholder="日" popper-class="indicator-select" size="mini"
        @change="dataCycleChange">
        <el-option v-for="item in dataCycleList" :key="item.value" :label="item.name" :value="item.name" />
      </el-select>
    </div>
    <IndicatorDashboard 
      v-if="menuActive === 1 && loading" 
      @changeIndicator="changeIndicator" 
      :indicatorActive="indicatorActive" 
      :indicatorData="indicatorData"
    ></IndicatorDashboard>
    <!-- <DataForm v-if="menuActive === 2 && loading" :indicatorData="indicatorData"></DataForm> -->
    <div class="echarts-legend" v-if="menuActive === 1">
      <!-- <div class="legend-title">净增目标完成率</div> -->
      <div v-for="item, index in legendList" :key="index" class="legend-icon">
        <div :style="{ backgroundColor: item.color, borderColor: item.borderColor }" class="legend-icon-box" />
        <div>{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import EchartsMap from './EchartsMap.vue'
import IndicatorDashboard from './IndicatorDashboard.vue'
import DataForm from './DataForm.vue'
import market from '@/api/market'
import common from '@/api/common'
export default {
  name: 'indicatorMap',
  components: {
    EchartsMap, IndicatorDashboard, DataForm
  },
  data() {
    return {
      menuActive: 1,
      indicatorActive: 'sxjzyzJz', //指标id
      dataTime: '2025-08-06',
      dataCycle: '日',
      dataCycleList: [
        {
          name: '日',
          value: '0'
        },
        {
          name: '月',
          value: '1'
        },
        {
          name: '季度',
          value: '2'
        },
      ],
      selectForm: {
        cityCode: '',
        countyCode: '',
        areacode: '',
      },
      selectAreaList: {
        cityList: [],
        countyList: [],
        areaList: [],
      },
      legendList: [
        { color: '#FF393980', borderColor: '#FF8787', text: '' },
        { color: '#FF838380', borderColor: '#FFBCB9', text: '' },
        { color: '#37D89280', borderColor: '#A9FFDA', text: '' },
        { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '' }
      ],
      menu: [
        {
          id: 1,
          name: '地图'
        },
        {
          id: 2,
          name: '表单'
        }
      ],
      areaLevel: null, //1是省级， 2是地市，3是区县，4是网格
      areaId: '', //999是省级 不同人员接口入参,
      indicatorData: {},
      loading: false,
      userLevel: '',
    }
  },
  created() {
    this.getUserInfo()
  },
  async mounted() {
    //首次进入获取地市的选择
    await this.getLatestDataDate()
    await this.getAreaByPid(999, 'cityList')
    await this.getUserCode()
  },
  methods: {
    async getLatestDataDate() {
      await market.getLatestDataDate().then(res => {
        if (res.code === 200) {
          this.dataTime = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    updadeLegendList(data) {
      this.legendList = [
        { color: '#FF393980', borderColor: '#FF8787', text: data.label1 },
        { color: '#FF838380', borderColor: '#FFBCB9', text: data.label2 },
        { color: '#37D89280', borderColor: '#A9FFDA', text: data.label3 },
        { color: '#A9FFDA80', borderColor: '#A9FFDA', text: data.label4 }
      ]
      console.log(this.legendList)
    },
    async changeIndicator(data) {
      this.indicatorActive = data
      await this.$refs.echartsMap.getIndicatorInsightByPid(1,data)
    },
    //获取指标洞察数据
    async getIndicatorInsightData() {
      this.loading = false
      await market.getIndicatorInsightData({
        cityCode: this.selectForm.cityCode ||'-1' , 
        countyCode: this.selectForm.countyCode || '-1', 
        areaCode: this.selectForm.areacode || '-1', 
        villageCode: '-1', 
        indexType:  this.dataCycle,
        statDate: this.dataTime,
      }).then(res => {
        if (res.code === 200) {
          this.indicatorData = res.data[0]
          this.loading = true
        } else {
          this.$message.error(res.msg)
          this.loading = true
        }
      })
    },
    //获取当前登录用户信息
    getUserInfo() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      console.log(userInfo)
      this.areaLevel = userInfo.roleType 
      this.userLevel = userInfo.roleType 
    },
    //根据当前用户加载地图等信息
    async getUserCode() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      switch (this.areaLevel) {
        case 1:
          this.areaId = userInfo.provId //999
          await this.getIndicatorInsightData()
          // await this.$refs.echartsMap.getShengArea() //获取云南省边界
          await this.$refs.echartsMap.getDimAreaByPid({ //获取地市边界
            areaId: this.areaId,
            areaLevel: this.areaLevel + 1
          })
          break
        case 2:
          this.areaId = userInfo.cityId
          this.selectForm.cityCode = userInfo.cityId || ''
          this.onCityChange()
          break
        case 3:
          this.areaId = userInfo.countyId
          this.selectForm.cityCode = userInfo.cityId || ''
          this.selectForm.countyCode = userInfo.countyId || ''
          await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
          this.onCountyChange()
          break
        case 4:
          this.areaId = userInfo.gridId
          this.selectForm.cityCode = userInfo.cityId || ''
          this.selectForm.countyCode = userInfo.countyId || ''
          this.selectForm.areacode = userInfo.gridId || ''
          await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
          await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
          this.onAreaChange()
      }
    },
    async onCityChange() {
      this.areaLevel = 2
      this.selectForm.countyCode = ''
      this.selectForm.areacode = ''
      this.selectAreaList.countyList = []
      this.selectAreaList.areaList = []
      await this.getIndicatorInsightData()
      await this.$refs.echartsMap.getDimAreaByPid({ //获取昆明区县边界, arealvel=2
        areaId: this.selectForm.cityCode,
        areaLevel: this.areaLevel + 1 //接口获取为3
      });
      await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
    },
    async onCountyChange() {
      this.areaLevel = 3
      this.selectForm.areacode = ''
      this.selectAreaList.areaList = []
      await this.getIndicatorInsightData()
      await this.$refs.echartsMap.getDimAreaByPid({ //获取网格边界, arealvel=3
        areaId: this.selectForm.countyCode,
        areaLevel: this.areaLevel + 1 //接口获取为4
      });
      await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
    },
    async onAreaChange() {
      this.areaLevel = 4
      await this.getIndicatorInsightData()
      await this.$refs.echartsMap.getDimAreaByPid({ //获取行政村边界, arealvel=4
        areaId: this.selectForm.areacode,
        areaLevel: this.areaLevel + 1 //接口获取为5
      });
      // await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
    },
    updataCode(data) {
      //只到网格
      if (data.areaLevel === 3) { //点击地市触发获取区县
        this.selectForm.cityCode = data.areaId
        this.onCityChange()
      } else if (data.areaLevel === 4) {
        this.selectForm.countyCode = data.areaId
        this.onCountyChange()
      } else if (data.areaLevel === 5) {
        this.selectForm.areacode = data.areaId
        this.onAreaChange()
      }
    },
    //地图返回上一级
    backMap() {
      console.log(this.areaLevel);
      switch (this.areaLevel) {
        case 2: 
          this.selectForm.cityCode = ''   //重新选回云南省查客群数量入参需要为空
          this.selectForm.countyCode = ''
          this.selectForm.areacode = ''
          this.selectAreaList.countyList = []
          this.selectAreaList.areaList = []
          this.areaLevel = 1
          this.areaId = 999
          this.getUserCode()
          break;
        case 3: 
          this.onCityChange()
          break;
        case 4: 
          this.onCountyChange()
          break;
      }
    },
    // 获取下拉框选择列表
    async getAreaByPid(code, listName) {
      await common.getAreaByPid({
        areaPid: code
      }).then(res => {
        if (res.code === 200) {
          this.selectAreaList[listName] = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async dataCycleChange() {
      await this.$refs.echartsMap.getIndicatorInsightByPid(2,this.dataCycle)
      await this.getIndicatorInsightData() // 同时更新指标面板数据
    },
    async dataTimeChange() {
      await this.$refs.echartsMap.getIndicatorInsightByPid(3,this.dataTime)
      await this.getIndicatorInsightData() // 同时更新指标面板数据
    },
    navClick(item) {
      if (item.id === this.menuActive) return
      this.menuActive = item.id
      // this.getUserCode()
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";

.IndictaorHeatMap {
  .echarts-map-back{
    position: absolute;
    left: vw(130);
    top: vh(-30);
    bottom: vh(30);
    right: vw(150);
    z-index: -1;
    background-image: url("../../../assets/images/indexInsight/echarts-back.png");
    background-size: 100% 100%;
  }
  .operation-tabs {
    position: absolute;
    top: vh(-14);
    left: vw(950);
    display: flex;
    z-index: 999;

    .operation-tabs-item {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      width: vw(80);
      height: vh(32);
      line-height: vh(32);
      // padding: vh(4.5) vw(20);
      text-align: center;
      color: #8ac0ff;
      cursor: pointer;
      background: linear-gradient(180deg,
          rgba(15, 27, 52, 0.5) 0%,
          rgba(46, 97, 199, 0.5) 100%);

      position: relative;
      border: 2px solid transparent;
      border-top: 2px solid #111421;

      &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: 2px;
        background: linear-gradient(90deg,
            rgba(106, 145, 197, 0.4) 0%,
            rgba(170, 212, 255, 0.8) 49.52%,
            rgba(106, 145, 197, 0.4) 100%);
      }

      margin-right: 1px;
    }

    .operation-tabs-active {
      @extend.operation-tabs-item;
      color: #d9f7ff;
      background: linear-gradient(180deg, #2669ef 0%, #142d60 100%);
      border: 2px solid transparent;
      border-image: linear-gradient(90deg,
          #def1fe 3.37%,
          rgba(222, 241, 254, 0) 100%) 1;
    }
  }
  .back {
      display: flex;
      align-items: center;
      cursor: pointer;
      .icon {
          width: vw(24);
          height: vh(24);
          background: url("../../../assets/images/market/icon.png") no-repeat;
          background-size: 100% 100%;
          margin-right: vw(6);
      }
      position: absolute;
      top: vh(23);
      left: vw(506);
      background-color: rgba(168, 216, 255, 1);
      background-clip: text;
      color: transparent;
      font-size: vw(16);
      z-index: 999;
  }

  .search {
    z-index: 999;
    width: vw(740);
    height: vh(32);
    position: absolute;
    top: vh(-14);
    right: vw(40);
    // background-color: aliceblue;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: vh(32);

    ::v-deep .el-input__inner {
      width: vw(140);
      height: vh(32);
      line-height: vh(32);
      background-color: #141e32;
      border-color: #677aa5;
      border-radius: vw(4);
      font-size: vw(14);
      color: #ffffff !important;
    }

    ::v-deep .el-select {
      font-size: vw(14);
    }

    ::v-deep .el-input__icon {
      line-height: vh(32) !important;
    }

    ::v-deep .el-date-editor {
      width: vw(140);
      .el-input__inner {
        padding-right: 0;
      }
      .el-input__suffix {
        display: none;
      }
    }
  }

  .echarts-legend {
    position: fixed;
    left: vw(511);
    bottom: vh(76);

    .legend-title {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      color: #ffffff;
    }

    .legend-icon {
      color: #ffffff;
      display: flex;
      align-items: center;
      margin-top: 10px;

      >div {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);

        &:first-child {
          margin-right: vw(12);
          width: vw(13);
          height: vw(13);
          border: vw(2) solid;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.indicator-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;

  .popper__arrow {
    border-bottom-color: #677aa5 !important;

    &::after {
      border-bottom-color: #141e32 !important;
    }
  }

  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    height: vh(34);
    line-height: vh(34);
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}

.dataTime-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;

  .el-picker-panel__body {
    color: white !important;

    .el-picker-panel__icon-btn,
    .el-date-picker__header-label {
      color: white !important;
    }

    .el-date-table th {
      color: white !important;
    }
  }
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #141e32 !important;
    }
  }
}
</style>