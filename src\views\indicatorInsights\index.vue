<template>
  <div class="indicator-insights">
    <IndicatorMap></IndicatorMap>
  </div>
</template>
<script>
import IndicatorMap from './components/indicatorMap.vue';
export default {
  name: 'IndicatorInsights',
  components: {
    IndicatorMap
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.indicator-insights {
  width: 100%;
}
</style>
