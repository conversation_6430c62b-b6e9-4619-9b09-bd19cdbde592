<template>
  <div class="login-container">
    <img src="../../assets/images/logo.png" class="img-logo" />
    <div class="login-box">
      <div class="login-box-item">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" label-position="left">
          <el-form-item prop="account">
            <div class="login-item-group">
              <span class="svg-container">
                <!-- <svg-icon icon-class="user" /> -->
                <img src="../../assets/images/username.png" />
              </span>
              <el-input
                ref="username"
                v-model="loginForm.account"
                placeholder="请输入账号"
                name="username"
                type="text"
                tabindex="1"
                clearable
                autocomplete="new-password"
              />
            </div>
          </el-form-item>

          <el-form-item prop="pwd">
            <div class="login-item-group">
              <span class="svg-container">
                <!-- <svg-icon icon-class="password" /> -->
                <img src="../../assets/images/password.png" />
              </span>
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.pwd"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                clearable
                autocomplete="new-password"
                @keyup.enter.native="handleLogin"
              />
              <!-- <span class="show-pwd" @click="showPwd">
                <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
              </span> -->
            </div>
          </el-form-item>

          <el-form-item prop="vcode">
            <div class="login-item-group" style="padding-right: 4px;">
              <span class="svg-container">
                <!-- <svg-icon icon-class="user" /> -->
                <img src="../../assets/images/vcode.png" />
              </span>
              <el-input
                ref="vcode"
                v-model="loginForm.vcode"
                placeholder="请输入验证码"
                name="vcode"
                type="text"
                tabindex="3"
                clearable
                autocomplete="new-password"
                @keyup.enter.native="handleLogin"
              />
              <VerifyCode :content-height="52" :identify-code="verifyCode" @refreshVerifyCode="refreshVerifyCode" />
            </div>
          </el-form-item>

          <el-form-item style="border: none; background-color: transparent; padding-left: 2px;">
            <!-- <el-checkbox v-model="loginForm.autoLogin">自动登录</el-checkbox> -->
            <div class="checkbox-group">
              <div class="check-box" @click="autoLoginClick">{{ loginForm.autoLogin === true ? checkedContent : '' }}</div>
              <div class="check-label" @click="autoLoginClick">自动登录</div>
            </div>
          </el-form-item>

          <el-button class="login-btn" :loading="loading" type="primary" @click.native.prevent="handleLogin">登录</el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
// import { validUsername } from '@/utils/validate'
import VerifyCode from '@/components/VerifyCode/VerifyCode.vue'
import { decryptStr, encryptStr, encryptPwd } from '@/utils/userCrypto'

export default {
  name: 'Login',
  components: {
    VerifyCode
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      const name = value.trim()
      if (name.length < 1) {
        callback(new Error('请输入账号'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 1) {
        callback(new Error('请输入密码'))
      } else if (value.length < 6) {
        callback(new Error('密码不能小于6位'))
      } else {
        callback()
      }
    }
    const validateVerifyCode = (rule, value, callback) => {
      const str = value.trim()
      if (str.length < 1) {
        callback(new Error('请输入验证码'))
      } else if (str.toLowerCase() !== this.verifyCode.toLowerCase()) {
        callback(new Error('验证码输入错误'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        account: '',
        pwd: '',
        vcode: '',
        autoLogin: false
      },
      loginRules: {
        account: [{ required: true, trigger: 'blur', validator: validateUsername }],
        pwd: [{ required: true, trigger: 'blur', validator: validatePassword }],
        vcode: [{ required: true, trigger: 'blur', validator: validateVerifyCode }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      verifyCode: '',
      checkedContent: '✓'
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  mounted() {
    this.refreshVerifyCode()
  },
  methods: {
    refreshVerifyCode() {
      this.$store.dispatch('user/getVerifyCode', {}).then(data => {
        // console.log(data)
        // 解密获得验证码
        this.verifyCode = decryptStr(data)
      })
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const account = this.loginForm.account.trim()
          // 对参数进行加密
          const param = {
            account: encryptStr(account),
            pwd: encryptPwd(this.loginForm.pwd, account),
            vcode: encryptStr(this.loginForm.vcode),
            autoLogin: this.loginForm.autoLogin ? 1 : 0
          }
          // console.log(param)
          // 提交参数
          this.$store.dispatch('user/login', param).then(() => {
            this.$router.push({ path: this.redirect || '/' })
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    autoLoginClick() {
      this.loginForm.autoLogin = !this.loginForm.autoLogin
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: rgba(25,130,209,0.8);
// $bg: rgba(80, 224, 255, 0.5);
// $light_gray:#ffffff;
$input_color: #ffffff;
$cursor: #ffffff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    // display: inline-block;
    // height: 47px;
    // width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 0px 5px 0px 15px;
      color: $input_color;
      height: 52px;
      caret-color: $input_color;
      font-size: 18px;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  //.el-checkbox {
  //  color: #ffffff;
  //  font-size: 17px;
  //}
  //
  //.el-checkbox__input.is-checked+.el-checkbox__label {
  //  color: #ffffff;
  //}

  .el-form-item {
    // background: rgba(25,130,209,0.22);
    // border: 1px solid #50E0FF;
    // border: 1px solid #E5E5E5;
    // background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
    margin-bottom: 24px;
  }
}
</style>

<style lang="scss" scoped>
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#eee;
$border_bg: rgba(80, 224, 255, 1);
$input_bg: rgba(25, 130, 209, 0.22);
$input_border: rgba(80, 224, 255, 0.35);
// $input_border: rgba(25, 130, 209, 0.6);

.login-container {
  min-height: 100%;
  width: 100%;
  // background-color: $bg;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: url('../../assets/images/bg.png');
  background-repeat: no-repeat;
  background-size: cover;

  .login-box {
    background: url('../../assets/images/loginform.png');
    background-repeat: no-repeat;
    background-size: cover;
    width: 716px;
    height: 626px;
  }

  .login-box-item {
    // margin: 62px 0 0 510px;
  }

  .login-form {
    // margin-top: 19px;
    // margin-left: 15px;
    position: relative;
    width: 484px;
    max-width: 100%;
    // padding: 160px 35px 0;
    // margin: 0 auto;
    overflow: hidden;
    margin: 172px auto 0 auto;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 0px 0px 0px 13px;
    // color: $dark_gray;
    // vertical-align: middle;
    // width: 34px;
    display: inline-flex;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .login-item-group {
    display: flex;
    align-items: center;
    padding-right: 8px;
    // background: url('../../assets/images/login-input-bg.png');
    // background-repeat: no-repeat;
    // background-size: cover;
    // background: rgba(25,130,209,0.22);
    // background-image: linear-gradient(rgba(80, 224, 255, 0.5), rgba(25,130,209,0.22) 14% 86%, rgba(80, 224, 255, 0.5));
    background-image: linear-gradient($input_border, $input_bg 20% 80%, $input_border), linear-gradient(to right, $input_border, transparent 3% 97%, $input_border);
    // border: 1px solid #50E0FF;
    border: 1px solid $border_bg;
    border-radius: 5px;
  }

  .login-btn {
    width: 483px;
    height: 79px;
    // background: linear-gradient(94deg, #5974FF, #33CDFF);
    background: url('../../assets/images/btn-login.png');
    background-repeat: no-repeat;
    background-size: cover;
    border: none;
    // box-shadow: 0px 6px 7px 0px rgba(43,74,207,0.24);
    // border-radius: 39px;
    font-size: 28px;
  }

  .img-logo {
    width: 689px;
    height: 93px;
  }

  .checkbox-group {
    display: flex;
    align-items: center;

    .check-box {
      background: url('../../assets/images/login-checkbox.png');
      background-repeat: no-repeat;
      background-size: cover;
      width: 33px;
      height: 33px;
      text-align: center;
      color: #ffffff;
      font-size: 17px;
      line-height: 36px;
      cursor: pointer;
    }

    .check-label {
      color: #ffffff;
      font-size: 17px;
      padding-left: 12px;
      cursor: pointer;
    }
  }

}
</style>
