<template>
  <div class="headerPart">
    <div class="header-content">
      <div class="header-tabs">
        <div
          v-for="item in menu"
          :key="item.name"
          :class="{'active': item.name === menuActive}"
          class="header-tabs-item"
          @click="navClick(item)"
        >{{ item.menuName }}</div>
      </div>
      <div class="heder-change">
        <div class="back" @click="back">
          <div class="icon"></div>
          <div>返回上级</div>
        </div>
        <!-- <div class="time-part">
          <div class="day">{{ dateDay }}</div>
          <div class="time">{{ dateTime }}</div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'HeaderPart',
  props: {
    menuActive: {
      type: String,
      default: ''
    },
    menu: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dateDay: null,
      dateTime: null,
    }
  },
  mounted() {
    this.timeFn()
  },
  methods: {
    timeFn() {
      this.timing = setInterval(() => {
        this.dateTime = new Date().Format('hh:mm:ss')
        this.dateDay = new Date().Format('yyyy/MM/dd')
      }, 1000)
    },
    navClick(menuItem) {
      if (menuItem === this.menuActive) return
      this.$emit('navclick', menuItem)
    },
    back() {
      this.$router.push('/')
    }
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
.headerPart {
  width: 100%;
  height: vh(86);
  line-height: vh(86);
  display: flex;
  background: url("../../../assets/images/dashboard/header-background.png")
    no-repeat;
  background-size: 100% 100%;
}
.header-content {
  width: 100%;
  margin-left: vw(547);
  display: flex;
  justify-content: space-between;
  .header-tabs {
    display: flex;
    margin-top: vh(11);
    .header-tabs-item {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      line-height: vh(32);
      background: url("../../../assets/images/operation/rectangle.png")
        no-repeat;
      color: #bed9ff;
      width: vw(140);
      height: vh(32);
      text-align: center;
      background-size: 100% 100%;
      cursor: pointer;
    }

    .active {
      @extend.header-tabs-item;
      background: url("../../../assets/images/operation/rectangle-active.png")
        no-repeat;
      color: #f2f8ff;
      background-size: 100% 100%;
    }
  }

  .heder-change {
    margin-top: vh(14);
    margin-right: vw(30);
    height: vh(25);
    color: #b0d0ff;
    display: flex;
    align-items: flex-start;
    align-items: center;
    cursor: pointer;
    line-height: vh(25);

    .back {
      width: vw(123);
      height: vh(30);
      background: url("../../../assets/images/operation/rectangle.png") no-repeat;
      background-size: 100% 100%;
      padding: vh(3.62) 0 0 vw(16.5);
      display: flex;
      cursor: pointer;
      div {
        width: vw(84);
        height: vh(21);
        font-size: vw(14);
        line-height: 150%;
      }
      .icon {
        width: vw(22);
        height: vh(20);
        background: url("../../../assets/images/market/icon.png") no-repeat;
        background-size: 100% 100%;
        margin-right: vw(6);
      }
    }
    .time-part {
      width: vw(100);
      height: vh(25);
      display: flex;
      justify-content: space-between;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(18);
      line-height: 100%;

    }
  }
}
</style>
