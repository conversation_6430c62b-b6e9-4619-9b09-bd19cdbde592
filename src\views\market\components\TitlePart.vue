<template>
  <div class="select-title">
    <img class="select-background" src="@/assets/images/operation/select-background.png">
    <div class="select-title-line">
      <img class="select-title-icon" src="@/assets/images/operation/select-title.svg">
      <div class="select-title-text">筛选条件</div>
      <el-popover v-if="question" :width="width" placement="bottom" popper-class="prop-class" title="口径揭示" trigger="hover">
        <slot>
          <div class="popoverContent" v-html="popoverContent" />
        </slot>
        <img slot="reference" class="select-question" src="@/assets/images/operation/select-question.svg">
      </el-popover>
    </div>
  </div>
</template>
<script>
export default {
  name: 'TitlePart',
  props: {
    width: {
      type: String,
      default: '238'
    },
    question: {
      type: Boolean,
      default: false
    },
    popoverContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
// 筛选条件标题
.select-title {
  position: relative;
  .select-title-line {
    position: absolute;
    top: vh(7);
    left: vw(23.5);
    display: flex;
    align-items: anchor-center;
    .select-title-text {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vw(24);
      color: #e4eeff;
      margin: 0 vw(5);
    }
  }
  margin: vh(16) 0 vh(14);
  width: vw(460);
  height: vh(46);
  .select-title-icon {
    width: vw(24);
  }
  .select-question {
    cursor: pointer;
    width: vw(16);
  }
  .select-background {
    position: absolute;
    width: 100%;
  }
}
.popoverContent {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: vw(14);
  color: #e4eeff;
}
</style>
<style>
.prop-class {
  padding: 20px 17px 33px;
  background: linear-gradient(180deg, #254177 0%, #1f4a9c 100%),
    radial-gradient(
      42.18% 58.91% at -8.46% 92.64%,
      rgba(58, 137, 255, 0.5) 0%,
      rgba(0, 0, 0, 0.5) 100%
    ),
    radial-gradient(
        50% 50% at 50% 50%,
        rgba(83, 140, 253, 0) 0%,
        rgba(83, 140, 253, 0.2) 100%
      )
      padding-box,
    linear-gradient(
        90deg,
        rgba(178, 209, 255, 0.5) 0%,
        rgba(123, 176, 255, 0.25) 50%,
        rgba(178, 209, 255, 0.5) 100%
      )
      border-box;
  border: 2px solid transparent;
  box-shadow: 0px 0px 80px 0px #2176ff inset;
}
.prop-class .popper__arrow::after {
  top: 0 !important;
  border-bottom-color: #1f4a9c !important;
}
.prop-class .el-popover__title {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #e4eeff;
}
</style>
