<template>
  <ScreenAdapter :h="screenHeight" :w="screenWidth">
    <HeaderPart :menu-active="menuActive" :menu="menuList" @navclick="navClick" />
    <div class="body-content">
      <router-view :key="key" />
    </div>
    <BottomPart />
  </ScreenAdapter>
</template>
<script>
import BottomPart from './components/BottomPart.vue'
import HeaderPart from './components/HeaderPart'
import ScreenAdapter from './components/ScreenAdapter.vue'

export default {
  name: 'Market',
  components: {
    ScreenAdapter,
    HeaderPart,
    BottomPart
  },
  // beforeRouteEnter(to, from, next) {
  //   console.log(to,99)
  //    next(vm => {
  //     vm.menuActive = to.name;
  // });
  // },
  
  data() {
    return {
      screenWidth: 1920, // 默认值
      screenHeight: 1080, // 默认值
      menuList: [
        { name: 'indicatorInsights', menuName: '指标洞察', path: '/market/indicatorInsights' },
        { name: 'taskScheduling', menuName: '任务调度', path: '/market/taskScheduling' },
        { name: 'executionMonitoring', menuName: '执行监控', path: '/market/executionMonitoring' },
      ]
    }
  },
  computed: {
    key() {
      return this.$route.path
    },
    menuActive() {
      return this.$route.name
    }
  },
  mounted() {
    this.updateScreenSize()
    this.onresize = this.debounce(() => this.updateScreenSize(), 100)
    window.addEventListener('resize', this.onresize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onresize)
  },
  methods: {
    debounce(fn, delay = 500) {
      let timer
      return function(...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          timer = null
          fn.apply(this, args)
        }, delay)
      }
    },
    updateScreenSize() {
      this.screenWidth = window.innerWidth
      this.screenHeight = window.innerHeight
    },
    navClick(menuItem) {
      // this.menuActive = menuItem.name
      this.$router.push({ path: menuItem.path })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "./index.scss";
.body-content {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  z-index: 99;
}
</style>
