<template>
  <div class="body-wired" v-loading="loading" element-loading-background="rgb(6, 21, 41)"
    element-loading-spinner="el-icon-loading loading iconfont icon-loading" element-loading-text="系统正在加载中......">
    <div class="body-top">
      <div class="body-top-left">
        <div class="chart-body">
          <div class="chart-pie">
            <DoughnutChart :data="pieData" :width="'100%'" :height="'100%'"
              :centerImage="'@/assets/images/network/centerImg.png'"
              :color="['#1E88E5', '#FFC107', '#4CAF50', '#9C27B0', '#FF5722']" :centerImageSize="0.3" />
          </div>
          <div class="chart-text">
            <div class="chart-text-box" v-for="(item, index) in pieData">
              <div class="rect" :style="{ background: gradientColors[index] }"></div>
              <div class="text">{{ item.name }}</div>
              <div class="value">{{ item.value + '%' }}</div>
            </div>
          </div>
        </div>

      </div>
      <div class="body-top-right">
        <div class="box-title">
          <div class="title-text">趋势</div>
        </div>
        <SmoothLinechart :data="lineData" :width="'100%'" :height="'100%'" :dataZoom="dataZoom" />
      </div>
    </div>
    <div class="body-middle">
      <div class="box-title">
        <div class="title-text">份额情况</div>
      </div>
      <!-- <StackedBar :width="'100%'" :height="'100%'" :xData="shareXData" :yAxisName="'百分比'" :yInterval="20" :yMax="100" /> -->
      <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="stackedChartData.xAxis"
        :seriesData="stackedChartData.series" :yAxisConfig="stackedChartData.yAxisConfig" :showLegend="true"
        barWidth="40%" stackName="total" :dataZoom="dataZoom" :flag="false" />
    </div>
    <div class="body-bottom">
      <div class="body-bottom-block">
        <div class="box-title">
          <div class="title-text">网格分布</div>
        </div>
        <!-- <StackedBar :width="'100%'" :height="'100%'" :xData="top5HighXData" /> -->
        <MixedChart :width="'100%'" :height="'100%'" :xAxisData="mixedChartData.xAxis" :barData="mixedChartData.bars"
          :lineData="mixedChartData.lines" :yAxisConfig="mixedChartData.yAxisConfig" :showLegend="true" barWidth="20%"
          :dataZoom="dataZoom" />
      </div>
      <div class="body-bottom-block">
        <div class="box-title">
          <div class="title-text">TOP5高市占行政村</div>
        </div>
        <StackedBar :width="'100%'" :height="'100%'" :data="top5HighData" :xData="top5HighXData" :yAxisName="'数量'"
          :yInterval="50" :yMax="250" />
      </div>
      <div class="body-bottom-block">
        <div class="box-title">
          <div class="title-text">TOP5低市占行政村</div>

        </div>
        <div class="view-more-btn" @click="openLowTop5Dialog">查看更多</div>
        <StackedBar :width="'100%'" :height="'100%'" :data="top5LowData" :xData="top5HighXData" :yAxisName="'数量'"
          :yInterval="50" :yMax="250" />
      </div>
    </div>

    <!-- 低市占行政村详情弹窗 -->
    <el-dialog 
      title="低市占行政村详情" 
      :visible.sync="lowTop5DialogVisible" 
      width="70%" 
      :before-close="closeLowTop5Dialog"
      class="low-top5-dialog"
      :modal-append-to-body="false"

      :z-index="3000">
      <div class="dialog-content">
        <el-table
          :data="lowTop5TableData"
          v-loading="tableLoading"
          stripe
          border
          height="500"
          :cell-style="tableCellStyle"
          :header-cell-style="tableHeaderStyle"
          :row-style="tableRowStyle">
          <el-table-column prop="cityName" label="地市" width="120" align="center"></el-table-column>
          <el-table-column prop="countyName" label="区县" width="120" align="center"></el-table-column>
          <el-table-column prop="areaName" label="网格" width="120" align="center"></el-table-column>
          <el-table-column prop="villageName" label="行政村" align="center">
            <template slot-scope="scope">
              <span :title="`${scope.row.countyName || ''}-${scope.row.areaName || ''}-${scope.row.villageName || ''}`">
                {{ scope.row.villageName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column width="80" prop="ydWifiRate" label="移动占比"  align="center">
            <template slot-scope="scope">
              {{ (scope.row.ydWifiRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column width="80" prop="dxWifiRate" label="电信占比"  align="center">
            <template slot-scope="scope">
              {{ (scope.row.dxWifiRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column width="80" prop="ltWifiRate" label="联通占比"  align="center">
            <template slot-scope="scope">
              {{ (scope.row.ltWifiRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <!-- <el-table-column prop="jkWifiRate" label="京宽占比" width="100" align="center">
            <template slot-scope="scope">
              {{ (scope.row.jkWifiRate * 100).toFixed(2) }}%
            </template>
          </el-table-column> -->
          <el-table-column width="80" prop="qtWifiRate" label="其他占比"  align="center">
            <template slot-scope="scope">
              {{ (scope.row.qtWifiRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, prev, pager, next"
            :total="pagination.total"
            background>
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DoughnutChart from '@/components/charts/DoughnutChart.vue'
import SmoothLinechart from '@/components/charts/SmoothLinechart.vue'
import StackedBar from '@/components/charts/StackedBar.vue'
import MixedChart from '@/components/charts/MixedChart.vue'
import StackedBarChart from '@/components/charts/StackedBarChart.vue'
import {
  getDefaultStatMon, getKdRate, getTrend,
  getShareTier,
  getHighTop5,
  getLowTop5,
  getLowTop5Page,
  getLowGrid
} from '@/api/network'
export default {
  name: 'BodyContent',
  props: {
    filterData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    DoughnutChart,
    SmoothLinechart,
    StackedBar,
    MixedChart,
    StackedBarChart
  },
  data() {
    return {
      paramForm: {
        statMon: new Date().toISOString().slice(0, 7),
        cityCode: '-1',
        countyCode: '-1',
        gridCode: '-1',

      },
      pieData: [
        { name: '中国移动', value: 50 },
        { name: '中国电信', value: 50 },
        { name: '中国联通', value: 15 },
        { name: '京宽网络', value: 10 },
        { name: '未知其他', value: 5 }
      ],
      lineData: [],
      top5HighData: [],
      top5LowData: [],
      shareXData: [
        '玉溪市', 'xx市', '江川区', '通海县', '华宁县',
        '易门县', '峨山彝族自治县', '新平彝族傣族自治县',
        '元江哈尼族傣族彝族自治县', '澄江市'
      ],
      top5HighXData: [
        'xxx', 'xxx', 'xxx', 'xxx', 'xxx'
      ],
      gradientColors: [
        'rgba(237,190,22,0.8)',
        '#40b6ed',
        'rgba(133,217,228,0.8)',
        'rgba(66,139,245,0.8)',
        'rgba(158,158,158,0.8)'
        // 'linear-gradient(148.47deg, #BE760A 34.82%, #EDBE16 77.78%)',
        // 'linear-gradient(130.32deg, #3480B4 0%, #41BEF5 51.82%, #53BFEF 97.3%)',
        // 'linear-gradient(117.95deg, #2DBACE 23.17%, #85D9E4 87.83%)',
        // 'linear-gradient(117.59deg, #0A52BC 24.66%, #1166E4 58.66%, #428BF5 88.51%)',
        // 'linear-gradient(130.32deg, #747474 0%, #9E9E9E 51.82%, #9E9E9E 97.3%)',
        // 'linear-gradient(130.32deg, #6A1B9A 0%, #BA68C8 97.3%)' // 广电或备用色
      ],
      storeData: {},
      stackedChartData: {},
      mixedChartData: {},
      dataZoom: [
        // {
        //   type: "slider", //隐藏或显示（true）组件
        //   show: true,
        //   backgroundColor: "rgb(19, 63, 100)", // 组件的背景颜色。
        //   fillerColor: "rgb(16, 171, 198)", // 选中范围的填充颜色。
        //   borderColor: "rgb(19, 63, 100)", // 边框颜色
        //   showDetail: false, //是否显示detail，即拖拽时候显示详细数值信息
        //   startValue: 0,
        //   endValue: 5,
        //   filterMode: "empty",
        //   width: "50%", //滚动条高度
        //   height: 8, //滚动条显示位置
        //   left: "center",
        //   zoomLoxk: true, // 是否锁定选择区域（或叫做数据窗口）的大小
        //   handleSize: 0, //控制手柄的尺寸
        //   bottom: 10, // dataZoom-slider组件离容器下侧的距离
        // },
        // {
        //   //没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
        //   type: "inside",
        //   zoomOnMouseWheel: false, //滚轮是否触发缩放
        //   moveOnMouseMove: true, //鼠标滚轮触发滚动
        //   moveOnMouseWheel: true,
        // },
      ],
      loading: false,
      // 弹窗相关数据
      lowTop5DialogVisible: false,
      lowTop5TableData: [],
      tableLoading: false,
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 表格样式
      tableCellStyle: {
        background: 'rgba(6, 21, 41, 0.9)',
        color: '#fff',
        borderColor: '#2c5aa0',
        borderWidth: '1px',
        borderStyle: 'solid'
      },
      tableHeaderStyle: {
        background: 'rgba(6, 21, 41, 0.95)',
        color: '#fff',
        borderColor: '#2c5aa0',
        borderWidth: '1px',
        borderStyle: 'solid',
        fontWeight: 'bold'
      },
      tableRowStyle: {
        background: 'rgba(6, 21, 41, 0.8)'
      }

    }
  },

  watch: {
    filterData: {
      handler(val) {
        if (val && val.coverType) {
          this.handleSearch(val);
          this.storeData = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSearch(form) {
      this.fetchWiredData(form);
    },
    fetchWiredData(form) {
      console.log('this.loading有线');

      this.loading = true
      let obj = {
        "statMon": form.statMon,
        "cityCode": form.cityCode,
        "countyCode": form.countyCode,
        "areaCode": form.gridCode
      }
      Promise.allSettled([
        this.fetchkdRate(obj),
        this.fetchTrend(obj),
        this.fetchShareTier(obj),
        this.fetchHighTop5(obj),
        this.fetchLowTop5(obj),
        this.fetchLowGrid(obj)
      ]).finally(() => {
        this.loading = false
      })

    },
    fetchkdRate(obj) {
      return getKdRate(obj).then(res => {
        // console.log('res', res);
        this.pieData = this.transformPieData(res.data)
      })
    },
    fetchTrend(obj) {
      return getTrend(obj).then(res => {
        // console.log('res', res);
        let maxMonth = ''
        this.lineData = res.data.map(item => {
          maxMonth = item.statMon.replace('0', '')
          return {
            ...item,
            statMon: item.statMon.replace('0', '') + '月'
          }
        })
        // 为了演示-多增加一个新的月份后面记得删除,组件内部也要删除
        this.lineData.push({
          dxWifiCnt: '',
          jkWifiCnt: '',
          ltWifiCnt: '',
          qtWifiCnt: '',
          statMon: (Number(maxMonth) > 12 ? 1 : Number(maxMonth) + 1) + '月',
          ydWifiCnt: ''
        })
      })
    },
    fetchShareTier(obj) {
      return getShareTier(obj).then(res => {
        // console.log('res', res);
        this.stackedChartData = this.getStackedChartData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)


      })
    },
    fetchHighTop5(obj) {
      return getHighTop5(obj).then(res => {
        // console.log('res', res);
        this.top5HighData = res.data
      })
    },
    fetchLowTop5(obj) {
      return getLowTop5(obj).then(res => {
        // console.log('res', res);
        this.top5LowData = res.data
      })
    },
    fetchLowGrid(obj) {
      return getLowGrid(obj).then(res => {
        // console.log('res', res);
        this.mixedChartData = this.getMixedChartData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)
      })
    },

    transformPieData(rawData) {
      console.log('rawData', rawData);
      if (!rawData || typeof rawData !== 'object') {
        return {};
      }
      const wifiRateMap = {
        ydWifiRate: '中国移动',
        jkWifiRate: '京宽网络',
        dxWifiRate: '中国电信',
        ltWifiRate: '中国联通',
        // gdWifiRate: '中国广电',
        qtWifiRate: '未知/其他'
      };
      const pieData = Object.entries(rawData)
        .filter(([key, value]) => wifiRateMap[key] !== undefined && value != null)
        .map(([key, value]) => {
          if (wifiRateMap[key] !== undefined) {
            return {
              name: wifiRateMap[key] || key,
              value: Math.round(value * 100)
            };
          }

        });


      return pieData;
    },
    getStackedChartData(dataList, cityCode, countyCode, areaCode) {
      // console.log('---', dataList, cityCode, countyCode, areaCode);
      const colorMap = {
        '移动': ['rgba(237,190,22,0.8)', 'rgba(237,190,22,0)'], // ['#ECBD15', '#E0A812', '#D4930F'],
        '电信': ['rgba(133,217,228,0.8)', 'rgba(133,217,228,0)'], // ['#7AC7D7', '#6AB5C5', '#5AA3B3'],
        '联通': ['rgba(66,139,245,0.8)', 'rgba(66,139,245,0)'], // ['#428AF4', '#3778E0', '#2C66CC'],
        // '广电': ['#53BFEF', '#47ABDB', '#3B97C7'],
        '其他': ['rgba(158,158,158,0.8)', 'rgba(158,158,158,0)'] // ['#9E9E9E', '#8A8A8A', '#767676']
      };
      const series = [
        {
          name: '移动',
          data: dataList.map(d => (d.ydWifiRate * 100).toFixed(2) ?? 0),
          color: colorMap['移动'],
          label: {
            show: true,          // 显示文本
            position: 'inside',  // 文本位置：柱状图内部
            formatter: params => `${params.value}%`,  // 加上百分号
            textStyle: {
              color: '#fff',    // 文字颜色设为白色
              fontSize: 12,
            },
          }
        },
        {
          name: '电信',
          data: dataList.map(d => (d.dxWifiRate * 100).toFixed(2) ?? 0),
          color: colorMap['电信']
        },
        {
          name: '联通',
          data: dataList.map(d => (d.ltWifiRate * 100).toFixed(2) ?? 0),
          color: colorMap['联通']
        },
        // {
        //   name: '广电',
        //   data: dataList.map(d => d.gdWifiRate * 100 ?? 0),
        //   color: colorMap['广电']
        // },
        {
          name: '其他',
          data: dataList.map(d => (d.qtWifiRate.toFixed(2)) * 100 ?? 0),
          color: colorMap['其他']
        }]
      let selectedName = []
      selectedName = dataList.map(item => {
        if (cityCode === "-1" && countyCode === "-1" && areaCode === "-1") {
          return item.cityName;
        } else if (cityCode !== "-1" && countyCode === "-1" && areaCode === "-1") {
          return item.countyName;
        } else if (cityCode !== "-1" && countyCode !== "-1" && areaCode === "-1") {
          return item.areaName;
        } else {
          return ""; // 如果有其它组合情况，可补充条件
        }
      });
      return {
        xAxis: selectedName,
        series,
        yAxisConfig: {
          name: '百分比',
          min: 0,
          max: 100
        }
      }
    },
    getMixedChartData(dataList, cityCode, countyCode, areaCode) {
      const colorMapBar = {
        '高市占数量': ['#85D9E4', '#6AC0D0', '#4FA7BC'],
        '低市占数量': ['#418AF4', '#3678D8', '#2B66BC'],
      };
      const colorMapLine = {
        '高市占占比': '#EDBE16',
        '低市占占比': '#E3925F',
      }
      const seriesBar = [
        {
          name: '高市占数量',
          data: dataList.map(d => d.highMarketCnt ?? 0),
          color: colorMapBar['高市占数量']
        },
        {
          name: '低市占数量',
          data: dataList.map(d => d.lowMarketCnt ?? 0),
          color: colorMapBar['低市占数量']
        },
      ]
      const seriesLine = [
        {
          name: '高市占占比',
          data: dataList.map(d => (d.highMarketRate * 100).toFixed(2) ?? 0),
          color: colorMapLine['高市占占比']
        },
        {
          name: '低市占占比',
          data: dataList.map(d => (d.lowMarketRate * 100).toFixed(2) ?? 0),
          color: colorMapLine['低市占占比']
        },
      ]

      let selectedName = []
      selectedName = dataList.map(item => {
        if (cityCode === "-1" && countyCode === "-1" && areaCode === "-1") {
          return item.cityName;
        }
        // else if (cityCode !== "-1" && countyCode === "-1" && areaCode === "-1") {
        //   return item.countyName;
        // }
        else if (cityCode !== "-1" ) {
          return item.countyName;
        }
        // else if (cityCode !== "-1" && countyCode !== "-1" && areaCode === "-1") {
        //   return item.areaName;
        // }
        else {
          return ""; // 如果有其它组合情况，可补充条件
        }
      });
      return {
        xAxis: selectedName,
        bars: seriesBar,
        lines: seriesLine,
        yAxisConfig: {
          left: {
            // name: '单位：万',
            min: 0,
            max: 'dataMax'
          },
          right: {
            name: '',
            min: 0,
            max: 100,
            noMax: true, // 干掉最大
            formatter: '{value}%'
          }

        }
      }

    },

    // 打开低市占详情弹窗
    openLowTop5Dialog() {
      this.lowTop5DialogVisible = true;
      this.pagination.currentPage = 1;
      this.fetchLowTop5PageData();

      // 确保弹窗在最顶层
      this.$nextTick(() => {
        const dialogWrapper = document.querySelector('.el-dialog__wrapper');
        if (dialogWrapper) {
          dialogWrapper.style.zIndex = '3000';
        }
        const modal = document.querySelector('.v-modal');
        if (modal) {
          modal.style.zIndex = '2999';
        }
      });
    },

    // 关闭弹窗
    closeLowTop5Dialog() {
      this.lowTop5DialogVisible = false;
      this.lowTop5TableData = [];
      this.pagination = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      };
    },

    // 获取分页数据
    fetchLowTop5PageData() {
      this.tableLoading = true;
      const params = {
        statMon: this.storeData.statMon,
        cityCode: this.storeData.cityCode,
        countyCode: this.storeData.countyCode,
        areaCode: this.storeData.gridCode,
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      };

      getLowTop5Page(params).then(res => {
        this.lowTop5TableData = res.data.list || [];
        this.pagination.total = res.data.total || 0;
      }).catch(err => {
        console.error('获取低市占分页数据失败：', err);
        this.$message.error('获取数据失败');
      }).finally(() => {
        this.tableLoading = false;
      });
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.fetchLowTop5PageData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.fetchLowTop5PageData();
    }

  },
  mounted() {

  },
  beforeDestroy() {
  },
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "./icon/font.css";
$designWidth: 1920;
$designHeight: 1080;

@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

::v-deep .v-modal {
  background: #cbc0c0;
}
::v-deep .el-table .el-table__cell {
  padding: 10px 0;
}
.body-wired {
  width: 100%;
  flex: 1;
  display: flex;
  padding-left: vw(40);
  z-index: 99;
  flex-direction: column;
  position: relative;

  .wired-content {
    display: flex;
    color: #ffffff;
    height: 100%;

    >div {
      flex: 1;
      flex-shrink: 0;
    }

    .form-part {
      padding: vh(20) vw(20) vh(10);
      margin-top: vh(20);
      margin-left: vw(46);
      background-color: #060f22b2;
      display: inline-block;

      ::v-deep .el-input__inner {
        width: vw(114);
        height: vh(32);
        line-height: vh(32);
        background-color: transparent;
        border-color: #727687;
        border-radius: 4px;
        font-size: vw(14);
        color: #b4b9bf !important;
      }

      ::v-deep .el-form-item__label {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
        color: #ffffff;
        padding-right: vw(10);
      }

      ::v-deep .el-form-item {
        margin-right: wv(10);
        margin-bottom: vh(10);
      }

      .dateClass {
        .el-date-editor.el-input {
          width: vw(120);
        }

        ::v-deep .el-input__inner {
          width: vw(120) !important;
        }
      }
    }
  }

  .body-top {
    width: vw(1835);
    height: vh(260);
    margin-top: vh(29);
    display: flex;
    justify-content: center;

    .body-top-left {
      width: vw(590);
      height: vh(260);
      border-radius: vw(24);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-body {
        width: vw(547);
        height: vh(151);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .chart-pie {
          width: vw(151);
          height: vh(151);
        }

        .chart-text {
          // border: 1px solid red;
          width: vw(366);
          height: vh(151);
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          color: #fff;

          .chart-text-box {
            // border: 1px solid red;
            width: vw(175);
            height: vh(38);
            display: flex;
            padding: vw(10);
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(90deg, rgba(86, 154, 255, 0.1) 0.11%, rgba(10, 82, 188, 0.05) 100.11%);
            border: 1px solid;
            border-image-source: linear-gradient(90deg, rgba(120, 175, 255, 0.256) -0.19%, rgba(78, 138, 226, 0.24) 99.81%);


            .rect {
              // border: 1px solid red;
              width: vw(10);
              height: vw(10);

            }

            .text {
              // border: 1px solid red;
              width: vw(72);
              height: vh(22);
              color: #F4F6FEBF;
              font-size: vh(16);
            }

            .value {
              // border: 1px solid red;
              width: vw(38);
              height: vh(22);
              font-weight: 900;
              color: #F7FAFD;
              font-size: vh(18);
            }
          }
        }
      }


    }

    .body-top-right {
      // position: relative;
      display: flex;
      flex-direction: column;
      width: vw(1211);
      height: vh(260);
      margin-left: vw(30);
      border-radius: vw(24);
      background: url("../../assets/images/dashboard/left-infobox.png");
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;

    }


  }

  .body-middle {
    // border: 1px solid red;
    width: vw(1835);
    height: vh(264);
    margin-top: vh(29);
    background: url("../../assets/images/dashboard/left-infobox.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: vw(24);
  }

  .body-bottom {
    // border: 1px solid red;
    width: vw(1835);
    height: vh(262);
    margin-top: vh(29);
    display: flex;
    justify-content: space-between;

    .body-bottom-block {
      width: vw(590);
      height: vh(262);
      border-radius: vw(24);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
      position: relative;

      .view-more-btn {
        position: absolute;
        right: vw(20);
        top: vh(15);
        color: #41BEF5;
        font-size: vw(14);
        cursor: pointer;
        transition: all 0.3s ease;

      &:hover {
        color: #66CCFF;
        text-shadow: 0 0 5px rgba(65, 190, 245, 0.5);
      }
    }
    }

  }

  .box-title {
    display: flex;
    justify-content: center;
    position: relative;
    // padding-left: vw(51);
    padding-top: vh(9);
    top: vh(-1);
    width: vw(318);
    height: vh(49);
    margin: 0 auto;
    background: url('../../assets/images/dashboard/title.png') no-repeat;
    background-size: 100% 100%;

    .title-text {
      color: #fff;
      width: fit-content;
      height: vh(29);
      line-height: vh(29);
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vw(22);
      letter-spacing: vw(1.3);
    }


  }
}



// 弹窗样式
::v-deep .el-dialog__wrapper {
  z-index: 3000 !important;
}

::v-deep .v-modal {
  z-index: 2999 !important;
}

::v-deep .low-top5-dialog {
  .el-dialog {
    background: rgba(6, 21, 41, 0.95) !important;
    border: 1px solid #2c5aa0 !important;
    border-radius: 8px !important;
    z-index: 3001 !important;
    margin-top: 5vh !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
  }

  .el-dialog__header {
    background: rgba(6, 21, 41, 0.95) !important;
    border-bottom: 1px solid #2c5aa0 !important;
    padding: 20px 20px 15px 20px !important;

    .el-dialog__title {
      color: #fff !important;
      font-size: 18px !important;
      font-weight: bold !important;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #fff !important;
        font-size: 18px !important;
        &:hover {
          color: #41BEF5 !important;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 20px !important;
    background: rgba(6, 21, 41, 0.9) !important;
  }

  .dialog-content {
    .el-table {
      background: rgba(6, 21, 41, 0.9) !important;
      border: 1px solid #2c5aa0 !important;
      border-radius: 4px !important;

      &::before {
        background-color: transparent !important;
        height: 0 !important;
      }

      .el-table__header-wrapper {
        th {
          background: rgba(6, 21, 41, 0.95) !important;
          color: #fff !important;
          border: 1px solid #2c5aa0 !important;
          font-weight: bold !important;
          font-size: 14px !important;
        }
      }

      .el-table--border {
        border: 1px solid #2c5aa0 !important;

        &::after {
          display: none !important;
        }
      }

      .el-table__body-wrapper {
        .el-table__row {
          &:hover {
            td {
              background: rgba(65, 190, 245, 0.3) !important;
            }
          }

          &.el-table__row--striped {
            td {
              background: rgba(6, 21, 41, 0.7) !important;
            }

            &:hover td {
              background: rgba(65, 190, 245, 0.3) !important;
            }
          }

          td {
            background: rgba(6, 21, 41, 0.85) !important;
            color: #fff !important;
            border: 1px solid #2c5aa0 !important;
            font-size: 13px !important;
          }
        }
      }

      .el-table__empty-block {
        background: rgba(6, 21, 41, 0.9) !important;

        .el-table__empty-text {
          color: #fff !important;
        }
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      text-align: center;
      background: rgba(6, 21, 41, 0.8) !important;
      padding: 15px !important;
      border-radius: 4px !important;
      border: 1px solid #2c5aa0 !important;

      .el-pagination {
        background: transparent !important;

        .el-pager li {
          background: rgba(6, 21, 41, 0.9) !important;
          color: #fff !important;
          border: 1px solid #2c5aa0 !important;
          margin: 0 2px !important;

          &:hover {
            color: #41BEF5 !important;
            background: rgba(65, 190, 245, 0.2) !important;
          }

          &.active {
            background: #41BEF5 !important;
            color: #fff !important;
            border-color: #41BEF5 !important;
          }
        }

        .btn-prev, .btn-next {
          background: rgba(6, 21, 41, 0.9) !important;
          color: #fff !important;
          border: 1px solid #2c5aa0 !important;

          &:hover {
            color: #41BEF5 !important;
            background: rgba(65, 190, 245, 0.2) !important;
          }

          &:disabled {
            color: #666 !important;
            background: rgba(6, 21, 41, 0.5) !important;
          }
        }

        .el-pagination__sizes {
          .el-select {
            .el-input__inner {
              background: rgba(6, 21, 41, 0.9) !important;
              color: #fff !important;
              border-color: #2c5aa0 !important;

              &:hover {
                border-color: #41BEF5 !important;
              }
            }
          }
        }

        .el-pagination__jump {
          color: #fff !important;

          .el-input__inner {
            background: rgba(6, 21, 41, 0.9) !important;
            color: #fff !important;
            border-color: #2c5aa0 !important;

            &:hover {
              border-color: #41BEF5 !important;
            }
          }
        }

        .el-pagination__total {
          color: #fff !important;
        }
      }
    }
  }
}
</style>
