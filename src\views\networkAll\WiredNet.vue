<template>
  <div class="body-content" v-loading="loading" element-loading-background="rgb(6, 21, 41)"
    element-loading-spinner="el-icon-loading loading iconfont icon-loading" element-loading-text="系统正在加载中......">
    <div class="body-top">
      <div class="body-top-left">
        <div class="box-title">
          <div class="title-text">份额</div>
        </div>
        <div class="chart-body-wrapper">
          <div class="chart-body">
            <div class="chart-pie">
              <DoughnutChart :data="pieData" :width="'100%'" :height="'100%'"
                :centerImage="'@/assets/images/network/centerImg.png'"
                :color="['#1E88E5', '#FFC107', '#4CAF50', '#9C27B0', '#FF5722']" :centerImageSize="0.3" />
            </div>
            <div class="chart-text">
              <div class="chart-text-box" v-for="(item, index) in pieData">
                <div class="rect" :style="{ background: gradientColors[index] }"></div>
                <div class="text">{{ item.name }}</div>
                <div class="value">{{ item.value + '%' }}</div>
              </div>
            </div>
          </div>
        </div>

      </div>
      <div class="body-top-right">
        <div class="box-title">
          <div class="title-text">趋势</div>
        </div>
        <SmoothLinechart :data="lineData" :width="'100%'" :height="'88%'" />
      </div>
    </div>
    <div class="body-bottom">
      <div class="body-bottom-left">
        <div class="box-title">
          <div class="title-text">TOP5高市占行政村</div>
        </div>
        <!-- <StackedBar :width="'100%'" :height="'100%'" :xData="top5HighXData" :yAxisName="'数量'" :yInterval="50"
          :yMax="250" /> -->
<!--        <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="top5HighData.xAxis"
          :seriesData="top5HighData.series" :yAxisConfig="top5HighData.yAxisConfig" :showLegend="true" barWidth="40%"
          stackName="total" /> -->
          <StackedBar :width="'100%'" :height="'100%'" :data="top5HighData" :xData="top5HighXData" :yAxisName="'数量'"
            :yInterval="50" :yMax="250" />

      </div>
      <div class="body-bottom-right">
        <div class="box-title">
          <div class="title-text">TOP5低市占行政村</div>
        </div>
        <!-- <StackedBar :width="'100%'" :height="'100%'" :xData="top5HighXData" :yAxisName="'数量'" :yInterval="50"
          :yMax="250" /> -->
     <!--   <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="top5LowData.xAxis"
          :seriesData="top5LowData.series" :yAxisConfig="top5LowData.yAxisConfig" :showLegend="true" barWidth="40%"
          stackName="total" /> -->
          <StackedBar :width="'100%'" :height="'100%'" :data="top5LowData" :xData="top5HighXData" :yAxisName="'数量'"
            :yInterval="50" :yMax="250" />
      </div>
    </div>
  </div>
</template>
<script>
import DoughnutChart from '@/components/charts/DoughnutChart.vue'
import SmoothLinechart from '@/components/charts/SmoothLinechart.vue'
import StackedBar from '@/components/charts/StackedBar.vue'
import StackedBarChart from '@/components/charts/StackedBarChart.vue'
import {
  getKdRate, getTrend,
  getHighTop5,
  getLowTop5,
} from '@/api/network'
export default {
  name: 'BodyContent',
  props: {
    filterData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    DoughnutChart,
    SmoothLinechart,
    StackedBar,
    StackedBarChart,
  },
  data() {
    return {
      lineData: [],
      pieData: [
        { name: '中国移动', value: 50 },
        { name: '中国电信', value: 20 },
        { name: '中国联通', value: 15 },
        { name: '京宽网络', value: 10 },
        { name: '未知其他', value: 5 }
      ],
      top5HighXData: [
        'xxx', 'xxx', 'xxx', 'xxx', 'xxx'
      ],
      gradientColors: [
        'linear-gradient(148.47deg, #BE760A 34.82%, #EDBE16 77.78%)',
        'linear-gradient(130.32deg, #3480B4 0%, #41BEF5 51.82%, #53BFEF 97.3%)',
        'linear-gradient(117.95deg, #2DBACE 23.17%, #85D9E4 87.83%)',
        'linear-gradient(130.32deg, #747474 0%, #9E9E9E 51.82%, #9E9E9E 97.3%)',
        'linear-gradient(117.59deg, #0A52BC 24.66%, #1166E4 58.66%, #428BF5 88.51%)',
        'linear-gradient(130.32deg, #6A1B9A 0%, #BA68C8 97.3%)' // 广电或备用色
      ],
      storeData: {},
      top5HighData: {},
      top5LowData: {},
      loading: false
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  watch: {
    filterData: {
      handler(val) {
        if (val && val.coverType) {
          this.handleSearch(val);
          this.storeData = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSearch(form) {
      this.fetchWiredData(form);
    },
    fetchWiredData(form) {
      this.loading = true
      let obj = {
        "statMon": form.statMon,
        "cityCode": form.cityCode,
        "countyCode": form.countyCode,
        "areaCode": form.gridCode
      }
      Promise.allSettled([
        this.fetchkdRate(obj),
        this.fetchTrend(obj),
        this.fetchHighTop5(obj),
        this.fetchLowTop5(obj)
      ]).finally(() => {
        this.loading = false
      })

    },
    fetchkdRate(obj) {
      return getKdRate(obj).then(res => {
        // console.log('res', res);
        this.pieData = this.transformPieData(res.data)
      })
    },
    fetchTrend(obj) {
      return getTrend(obj).then(res => {
        this.lineData = res.data.map(item => {
          return {
            ...item,
            statMon: item.statMon.replace('0', '') + '月'
          }
        })
      })
    },
    fetchHighTop5(obj) {
      return getHighTop5(obj).then(res => {
        // console.log('res', res);
        // this.top5HighData = this.getStackedChartData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)
        // console.log('selectedName', this.stackedChartData);
        this.top5HighData = res.data
      })
    },
    fetchLowTop5(obj) {
      return getLowTop5(obj).then(res => {
        // console.log('res', res);
        // this.top5LowData = this.getStackedChartData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)
        this.top5LowData = res.data
      })
    },
    transformPieData(rawData) {
      // console.log('rawData', rawData);
      if (!rawData || typeof rawData !== 'object') {
        return {};
      }
      const wifiRateMap = {
        ydWifiRate: '中国移动',
        jkWifiRate: '京宽网络',
        dxWifiRate: '中国电信',
        ltWifiRate: '中国联通',
        // gdWifiRate: '中国广电',
        qtWifiRate: '未知/其他'
      };
      const pieData = Object.entries(rawData)
        .filter(([key, value]) => wifiRateMap[key] !== undefined && value != null)
        .map(([key, value]) => {
          if (wifiRateMap[key] !== undefined) {
            return {
              name: wifiRateMap[key] || key,
              value: Math.round(value * 100)
            };
          }

        });


      return pieData;
    },
    getStackedChartData(dataList, cityCode, countyCode, areaCode) {
      // console.log('---', dataList, cityCode, countyCode, areaCode);
      const colorMap = {
        '移动': ['#ECBD15', '#E0A812', '#D4930F'],
        '电信': ['#7AC7D7', '#6AB5C5', '#5AA3B3'],
        '联通': ['#428AF4', '#3778E0', '#2C66CC'],
        '广电': ['#53BFEF', '#47ABDB', '#3B97C7'],
        '其他': ['#9E9E9E', '#8A8A8A', '#767676']
      };
      const series = [
      //   {
      //     name: '移动',
      //     data: dataList.map(d => d.ydWifiRate * 100 ?? 0),
      //     color: colorMap['移动']
      //   },
      //   {
      //     name: '电信',
      //     data: dataList.map(d => d.dxWifiRate * 100 ?? 0),
      //     color: colorMap['电信']
      //   },
      //   {
      //     name: '联通',
      //     data: dataList.map(d => d.ltWifiRate * 100 ?? 0),
      //     color: colorMap['联通']
      //   },
      //   {
      //     name: '广电',
      //     data: dataList.map(d => d.gdWifiRate * 100 ?? 0),
      //     color: colorMap['广电']
      //   },
      //   {
      //     name: '其他',
      //     data: dataList.map(d => d.qtWifiRate * 100 ?? 0),
      //     color: colorMap['其他']
      //   }]
      // let selectedName = []
      // selectedName = dataList.map(item => {
      //   return item.villageName
      // });
      {
          name: '移动',
          data: dataList.map(d => (d.ydWifiRate * 100).toFixed(2) ?? 0),
          color: colorMap['移动'],
          label: {
            show: true,          // 显示文本
            position: 'inside',  // 文本位置：柱状图内部
            formatter: params => `${params.value}%`,  // 加上百分号
            textStyle: {
              color: '#fff',    // 文字颜色设为白色
              fontSize: 16,
            },
          }
        },
        {
          name: '电信',
          data: dataList.map(d => (d.dxWifiRate * 100).toFixed(2) ?? 0),
          color: colorMap['电信']
        },
        {
          name: '联通',
          data: dataList.map(d => (d.ltWifiRate * 100).toFixed(2) ?? 0),
          color: colorMap['联通']
        },
        // {
        //   name: '广电',
        //   data: dataList.map(d => d.gdWifiRate * 100 ?? 0),
        //   color: colorMap['广电']
        // },
        {
          name: '其他',
          data: dataList.map(d => (d.qtWifiRate.toFixed(2)) * 100 ?? 0),
          color: colorMap['其他']
        }]
      let selectedName = []
      selectedName = dataList.map(item => {
        if (cityCode === "-1" && countyCode === "-1" && areaCode === "-1") {
          return item.cityName;
        } else if (cityCode !== "-1" && countyCode === "-1" && areaCode === "-1") {
          return item.countyName;
        } else if (cityCode !== "-1" && countyCode !== "-1" && areaCode === "-1") {
          return item.areaName;
        } else {
          return ""; // 如果有其它组合情况，可补充条件
        }
      });
      return {
        xAxis: selectedName,
        series,
        yAxisConfig: {
          name: '百分比',
          min: 0,
          max: 100
        }
      }
    },

  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "./icon/font.css";
$designWidth: 1920;
$designHeight: 1080;

@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

.body-content {
  width: 100%;
  flex: 1;
  display: flex;
  padding-left: vw(40);
  z-index: 99;
  flex-direction: column;

  .body-top {
    width: vw(1835);
    height: vh(404);
    margin-top: vh(29);
    display: flex;
    justify-content: center;

    .body-top-left {
      // border: 2px solid;
      // border-image-source: linear-gradient(180deg, #59B7FF 0%, rgba(41, 33, 98, 0.0001) 100%);
      border-radius: vw(24);
      border-image-slice: 1;
      /* 必须设置，否则不会显示 */
      width: vw(590);
      height: vh(404);

      // background: linear-gradient(180deg,
      //     rgba(15, 27, 52, 0.5) 0%,
      //     rgba(46, 97, 199, 0.5) 100%);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;

      display: flex;
      flex-direction: column;
      align-items: center;

      // justify-content: center;
      .chart-body-wrapper {
        width: vw(490);
        height: vh(540);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .chart-body {
          width: vw(490);
          height: vh(254);
          display: flex;
          align-items: center;
          justify-content: space-between;

          .chart-pie {
            width: vw(252);
            height: vh(252);
          }

          .chart-text {
            width: vw(182);
            height: vh(254);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            color: #fff;

            .chart-text-box {
              width: vw(175);
              height: vh(38);
              display: flex;
              padding: vw(10);
              justify-content: space-between;
              align-items: center;
              background: linear-gradient(90deg, rgba(86, 154, 255, 0.1) 0.11%, rgba(10, 82, 188, 0.05) 100.11%);
              border: 1px solid;
              border-image-source: linear-gradient(90deg, rgba(120, 175, 255, 0.256) -0.19%, rgba(78, 138, 226, 0.24) 99.81%);


              .rect {
                width: vw(10);
                height: vw(10);

              }

              .text {
                width: vw(72);
                height: vh(22);
                color: #F4F6FEBF;
                font-size: vh(16);
              }

              .value {
                width: vw(38);
                height: vh(22);
                font-weight: 900;
                color: #F7FAFD;
                font-size: vh(18);
              }
            }
          }
        }
      }




    }

    .body-top-right {
      border-radius: vw(24);
      width: vw(1214);
      height: vh(404);
      margin-left: vw(30);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .body-bottom {
    width: vw(1835);
    height: vh(404);
    margin-top: vh(29);
    display: flex;
    justify-content: space-between;

    .body-bottom-left {
      width: vw(900);
      height: vh(404);
      border-radius: vw(24);
      // background: linear-gradient(180deg,
      //     rgba(15, 27, 52, 0.5) 0%,
      //     rgba(46, 97, 199, 0.5) 100%);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
    }

    .body-bottom-right {
      width: vw(900);
      height: vh(404);
      border-radius: vw(24);
      // background: linear-gradient(180deg,
      //     rgba(15, 27, 52, 0.5) 0%,
      //     rgba(46, 97, 199, 0.5) 100%);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
    }

  }

  .box-title {
    display: flex;
    justify-content: center;
    position: relative;
    // padding-left: vw(51);
    padding-top: vh(9);
    top: vh(-1);
    width: vw(318);
    height: vh(49);
    margin: 0 auto;
    background: url('../../assets/images/dashboard/title.png') no-repeat;
    background-size: 100% 100%;

    .title-text {
      color: #fff;
      width: fit-content;
      height: vh(29);
      line-height: vh(29);
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vw(22);
      letter-spacing: vw(1.3);
    }
  }
}
</style>
