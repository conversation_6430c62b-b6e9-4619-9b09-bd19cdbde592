<template>
  <div class="body-content" v-loading="loading" element-loading-background="rgb(6, 21, 41)"
    element-loading-spinner="el-icon-loading loading iconfont icon-loading" element-loading-text="系统正在加载中......">
    <div class="body-top">
      <div class="body-top-left">
        <div class="box-title">
          <div class="title-text">优势弱占比</div>
        </div>
        <div class="chart-body-wrapper">
          <div class="chart-body">
            <div class="chart-pie">
              <DoughnutChart :data="pieData" :width="'100%'" :height="'100%'"
                :centerImage="'@/assets/images/network/centerImg.png'"
                :color="['#1E88E5', '#FFC107', '#4CAF50', '#9C27B0', '#FF5722']" :centerImageSize="0.3" />
            </div>
            <div class="chart-text">
              <div class="chart-text-box" v-for="(item, index) in pieData">
                <div class="rect" :style="{ background: gradientColors[index] }"></div>
                <div class="text">{{ item.name }}</div>
                <div class="value">{{ item.value + '%' }}</div>
              </div>

            </div>
          </div>
        </div>

      </div>
      <div class="body-top-right">
        <div class="box-title">
          <div class="title-text">占比层次</div>
        </div>
        <!-- <SmoothLinechart :width="'100%'" :height="'100%'" /> -->
        <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="shareTierData.xAxis"
          :seriesData="shareTierData.series" :yAxisConfig="shareTierData.yAxisConfig" :showLegend="true" barWidth="40%"
          stackName="total" :dataZoom="dataZoom" :flag="false" />
      </div>
    </div>
    <div class="body-bottom">
      <div class="body-bottom-block">
        <div class="box-title">
          <div class="title-text">网格分布</div>
        </div>
        <!-- <MixedChart :width="'100%'" :height="'100%'" :xAxisData="mixedChartData.xAxis" :barData="mixedChartData.bars"
          :lineData="mixedChartData.lines" :yAxisConfig="mixedChartData.yAxisConfig" :showLegend="true" barWidth="20%"
          :dataZoom="dataZoom" /> -->
        <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="mixedChartData.xAxis"
          :seriesData="mixedChartData.series" :yAxisConfig="mixedChartData.yAxisConfig" :showLegend="true"
          barWidth="40%" stackName="total" :dataZoom="dataZoom" />
      </div>
      <div class="body-bottom-block">
        <div class="box-title">
          <div class="title-text">TOP5移动强</div>
        </div>
        <!-- <StackedBar :width="'100%'" :height="'100%'" :xData="top5HighXData" :yAxisName="'数量'" :yInterval="50"
          :yMax="250" /> -->
        <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="top5HighData.xAxis"
          :seriesData="top5HighData.series" :yAxisConfig="top5HighData.yAxisConfig" :showLegend="true" barWidth="40%"
          stackName="total" :flag="false" :rawData="top5HighRawData" />
      </div>
      <div class="body-bottom-block">
        <div class="box-title">
          <div class="title-text">TOP5竞对强</div>
        </div>
        <StackedBarChart :width="'100%'" :height="'100%'" :xAxisData="top5LowData.xAxis"
          :seriesData="top5LowData.series" :yAxisConfig="top5LowData.yAxisConfig" :showLegend="true" barWidth="40%"
          stackName="total" :flag="false" :rawData="top5LowRawData" />
      </div>
    </div>
  </div>
</template>
<script>
import DoughnutChart from '@/components/charts/DoughnutChart.vue'
import SmoothLinechart from '@/components/charts/SmoothLinechart.vue'
import StackedBar from '@/components/charts/StackedBar.vue'
import MixedChart from '@/components/charts/MixedChart.vue'
import StackedBarChart from '@/components/charts/StackedBarChart.vue'
import {
  getCoverRate, getShareTierW, getHighTop5W, getLowTop5W, getLowGridW
} from '@/api/network'
export default {
  name: 'BodyContent',
  props: {
    filterData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    DoughnutChart,
    SmoothLinechart,
    StackedBar,
    MixedChart,
    StackedBarChart
  },
  data() {
    return {
      data: {
        value_001: 1024,
        value_002: 14,
        value_003: 124,
        value_004: 10240,
        value_005: 1024,
        value_006: 1034,
        value_007: 1024,
        value_008: 156,
        value_009: 1818,
        value_010: 12223,
        value_011: 2456,
        value_012: 24,
        value_013: 245,
        value_025: 2460,
        value_027: 24666,
        value_029: 122,
        value_026: 84,
        value_028: 849,
        value_030: 8,
        value_031: 84,
        value_032: 40,
        value_033: 12,
        value_034: 245,
        value_035: 245,
        value_036: 2477,
        value_037: 245,
        value_014: 1.867,
        value_015: 7458,
        value_016: 2,
        value_017: 25,
        value_018: 245,
        value_019: 2477,
        value_020: 2,
        value_021: 245,
        value_022: 2245,
        value_023: 24225,
        value_024: 25,
        value_038: 1.221,
        value_039: 745,
        value_040: 25.2,
        value_041: 24,
        value_042: 1.25,
        value_043: 24,
        value_044: 24.5,
        value_045: 2.45,
        value_046: 22,
      },
      pieData: [
        { name: '中国移动', value: 50 },
        { name: '中国电信', value: 20 },
        { name: '中国联通', value: 15 },
        { name: '京宽网络', value: 10 },
        { name: '未知其他', value: 5 }
      ],
      top5HighXData: [
        'xxx', 'xxx', 'xxx', 'xxx', 'xxx'
      ],
      gradientColors: [
        'linear-gradient(148.47deg, #BE760A 34.82%, #EDBE16 77.78%)',
        'linear-gradient(130.32deg, #3480B4 0%, #41BEF5 51.82%, #53BFEF 97.3%)',
        'linear-gradient(117.95deg, #2DBACE 23.17%, #85D9E4 87.83%)',
        'linear-gradient(130.32deg, #747474 0%, #9E9E9E 51.82%, #9E9E9E 97.3%)',
        'linear-gradient(117.59deg, #0A52BC 24.66%, #1166E4 58.66%, #428BF5 88.51%)',
        'linear-gradient(130.32deg, #6A1B9A 0%, #BA68C8 97.3%)' // 广电或备用色
      ],
      shareTierData: {},
      storeData: {},
      top5HighData: {},
      top5LowData: {},
      top5HighRawData: [],
      top5LowRawData: [],
      mixedChartData: {
        xAxis: ['一季度', '二季度', '三季度', '四季度', '年度', '总计'],
        bars: [
          {
            name: '高市占点比',
            data: [3, 5.5, 6, 5, 6, 7],
            color: ['#85D9E4', '#6AC0D0', '#4FA7BC']
          },
          {
            name: '低市占点比',
            data: [2, 4, 4.5, 3.5, 4, 5],
            color: ['#418AF4', '#3678D8', '#2B66BC']
          }
        ],
        lines: [
          // {
          //   name: '高市占数量',
          //   data: [40, 20, 45, 60, 65, 70],
          //   color: '#EDBE16'
          // },
          // {
          //   name: '低市占数量',
          //   data: [45, 15, 40, 55, 58, 75],
          //   color: '#E3925F'
          // }
        ],
        yAxisConfig: {
          left: {
            name: '单位：万',
            min: 0,
            max: 'dataMax'
          },
          right: {
            name: '',
            min: 0,
            max: 100,
            formatter: '{value}%'
          }
        }
      },
      dataZoom: [
        // {
        //   type: "slider", //隐藏或显示（true）组件
        //   show: true,
        //   backgroundColor: "rgb(19, 63, 100)", // 组件的背景颜色。
        //   fillerColor: "rgb(16, 171, 198)", // 选中范围的填充颜色。
        //   borderColor: "rgb(19, 63, 100)", // 边框颜色
        //   showDetail: false, //是否显示detail，即拖拽时候显示详细数值信息
        //   startValue: 0,
        //   endValue: 5,
        //   filterMode: "empty",
        //   width: "50%", //滚动条高度
        //   height: 8, //滚动条显示位置
        //   left: "center",
        //   zoomLoxk: true, // 是否锁定选择区域（或叫做数据窗口）的大小
        //   handleSize: 0, //控制手柄的尺寸
        //   bottom: 10, // dataZoom-slider组件离容器下侧的距离
        // },
        // {
        //   //没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
        //   type: "inside",
        //   zoomOnMouseWheel: false, //滚轮是否触发缩放
        //   moveOnMouseMove: true, //鼠标滚轮触发滚动
        //   moveOnMouseWheel: true,
        // },
      ],
      loading: false
    }
  },
  watch: {
    filterData: {
      handler(val) {
        if (val && val.coverType) {
          this.handleSearch(val);
          this.storeData = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },

  methods: {
    handleSearch(form) {
      this.fetchWiredData(form);
    },
    fetchWiredData(form) {
      console.log('loading无线');
      this.loading = true
      let obj = {
        "statMon": form.statMon,
        "cityCode": form.cityCode,
        "countyCode": form.countyCode,
        "gridType": form.dataType
      }

      Promise.allSettled([
        this.fetchkdRate(obj),
        this.fetchShareTierW(obj),
        this.fetchHighTop5(obj),
        this.fetchLowTop5(obj),
        this.fetchLowGridW(obj)
      ]).finally(() => {
        this.loading = false
      })

    },
    fetchkdRate(obj) {
      return getCoverRate(obj).then(res => {
        // console.log('res', res);
        this.pieData = this.transformPieData(res.data)
      })
    },
    fetchShareTierW(obj) {
      return getShareTierW(obj).then(res => {
        // console.log('res', res);
        this.shareTierData = this.getStachData(res.data, this.storeData.cityCode, this.storeData.countyCode)

      })
    },
    fetchHighTop5(obj) {
      return getHighTop5W(obj).then(res => {
        // console.log('res', res);
        this.top5HighRawData = res.data;
        this.top5HighData = this.getStackedChartData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)

      })
    },
    fetchLowTop5(obj) {
      return getLowTop5W(obj).then(res => {
        this.top5LowRawData = res.data;
        this.top5LowData = this.getStackedChartData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)
      })
    },
    fetchLowGridW(obj) {
      return getLowGridW(obj).then(res => {
        // console.log('res', res);
        this.mixedChartData = this.getMixedData(res.data, this.storeData.cityCode, this.storeData.countyCode, this.storeData.gridCode)
      })
    },
    transformPieData(rawData) {
      // console.log('rawData', rawData);
      if (!rawData || typeof rawData !== 'object') {
        return [];
      }
      const wifiRateMap = {
        advantageRate: '优势占比',
        disadvantageRate: '持平占比',

      };
      const pieData = Object.entries(rawData)
        .filter(([key, value]) => wifiRateMap[key] !== undefined && value != null)
        .map(([key, value]) => {
          if (wifiRateMap[key] !== undefined) {
            return {
              name: wifiRateMap[key] || key,
              value: Math.round(value * 100)
            };
          }

        });


      return pieData;
    },
    getStachData(dataList, cityCode, countyCode) {
      // console.log('---', dataList, cityCode, countyCode, areaCode);
      const colorMap = {
        '优势占比': ['#ECBD15', '#E0A812', '#D4930F'],
        '持平占比': ['#7AC7D7', '#6AB5C5', '#5AA3B3'],
        // '联通': ['#428AF4', '#3778E0', '#2C66CC'],
        // '广电': ['#53BFEF', '#47ABDB', '#3B97C7'],
        // '其他': ['#9E9E9E', '#8A8A8A', '#767676']
      };
      const series = [
        {
          name: '优势占比',
          data: dataList.map(d => (d.advantageRate * 100).toFixed(2) ?? 0),
          color: colorMap['优势占比']
        },
        {
          name: '持平占比',
          data: dataList.map(d => (d.disadvantageRate * 100).toFixed(2) ?? 0),
          color: colorMap['持平占比']
        },
      ]
      let selectedName = []
      selectedName = dataList.map(item => {
        if (cityCode === "-1" && countyCode === "-1") {
          return item.cityName;
        } else if (cityCode !== "-1" && countyCode === "-1") {
          return item.countyName;
        } else if (cityCode !== "-1" && countyCode !== "-1") {
          return item.areaName;
        } else {
          return ""; // 如果有其它组合情况，可补充条件
        }
      });


      return {
        xAxis: selectedName,
        series,
        yAxisConfig: {
          name: '百分比',
          min: 0,
          max: 100
        }
      }
    },
    getMixedData(dataList, cityCode, countyCode) {
      // console.log('---', dataList, cityCode, countyCode, areaCode);
      const colorMap = {
        '优势网格数量': ['#ECBD15', '#E0A812', '#D4930F'],
        '持平网格数量': ['#7AC7D7', '#6AB5C5', '#5AA3B3'],
        // '联通': ['#428AF4', '#3778E0', '#2C66CC'],
        // '广电': ['#53BFEF', '#47ABDB', '#3B97C7'],
        // '其他': ['#9E9E9E', '#8A8A8A', '#767676']
      };
      const series = [
        {
          name: '优势网格数量',
          data: dataList.map(d => d.advantageAreaCnt ?? 0),
          color: colorMap['优势网格数量']
        },
        {
          name: '持平网格数量',
          data: dataList.map(d => d.disadvantageAreaCnt ?? 0),
          color: colorMap['持平网格数量']
        },
      ]
      let selectedName = []
      selectedName = dataList.map(item => {
        if (cityCode === "-1" && countyCode === "-1") {
          return item.cityName;
        } else if (cityCode !== "-1") {
          return item.countyName;
        }
        // else if (cityCode !== "-1" && countyCode !== "-1") {
        //   return item.areaName;
        // }
        else {
          return ""; // 如果有其它组合情况，可补充条件
        }
      });
      console.log();


      return {
        xAxis: selectedName,
        series,
        yAxisConfig: {
          name: '数量',
          min: 0,
          max: 'dataMax'
        }
      }
    },
    getStackedChartData(dataList, cityCode, countyCode, areaCode) {
      // console.log('---', dataList, cityCode, countyCode, areaCode);
      const colorMap = {
        '优势占比': ['#ECBD15', '#E0A812', '#D4930F'],
        '持平占比': ['#7AC7D7', '#6AB5C5', '#5AA3B3'],
        // '联通': ['#428AF4', '#3778E0', '#2C66CC'],
        // '广电': ['#53BFEF', '#47ABDB', '#3B97C7'],
        // '其他': ['#9E9E9E', '#8A8A8A', '#767676']
      };
      const series = [
        {
          name: '优势占比',
          data: dataList.map(d => (d.advantageRate * 100).toFixed(2) ?? 0),
          color: colorMap['优势占比']
        },
        {
          name: '持平占比',
          data: dataList.map(d => (d.disadvantageRate * 100).toFixed(2) ?? 0),
          color: colorMap['持平占比']
        },
      ]
      let selectedName = []
      selectedName = dataList.map(item => {
        return item.villageName
      });
      return {
        xAxis: selectedName,
        series,
        yAxisConfig: {
          name: '百分比',
          min: 0,
          max: 100
        }
      }
    },

  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "./icon/font.css";
$designWidth: 1920;
$designHeight: 1080;

@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

.body-content {
  width: 100%;
  flex: 1;
  display: flex;
  padding-left: vw(40);
  z-index: 99;
  flex-direction: column;

  .body-top {

    width: vw(1835);
    height: vh(404);
    margin-top: vh(29);
    display: flex;
    justify-content: center;

    .body-top-left {
      // border: 2px solid;
      // border-image-source: linear-gradient(180deg, #59B7FF 0%, rgba(41, 33, 98, 0.0001) 100%);
      border-radius: vw(24);
      border-image-slice: 1;
      /* 必须设置，否则不会显示 */
      width: vw(590);
      height: vh(404);
      // background: linear-gradient(180deg,
      //     rgba(15, 27, 52, 0.5) 0%,
      //     rgba(46, 97, 199, 0.5) 100%);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .chart-body-wrapper {
        width: vw(490);
        height: vh(540);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .chart-body {

          width: vw(490);
          height: vh(254);
          display: flex;
          align-items: center;
          justify-content: space-between;

          .chart-pie {
            width: vw(252);
            height: vh(252);
          }

          .chart-text {
            //
            width: vw(182);
            height: vh(92);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            color: #fff;

            .chart-text-box {
              //
              width: vw(175);
              height: vh(38);
              display: flex;
              padding: vw(10);
              justify-content: space-between;
              align-items: center;
              background: linear-gradient(90deg, rgba(86, 154, 255, 0.1) 0.11%, rgba(10, 82, 188, 0.05) 100.11%);
              border: 1px solid;
              border-image-source: linear-gradient(90deg, rgba(120, 175, 255, 0.256) -0.19%, rgba(78, 138, 226, 0.24) 99.81%);


              .rect {
                //
                width: vw(10);
                height: vw(10);

              }

              .text {
                //
                width: vw(72);
                height: vh(22);
                color: #F4F6FEBF;
                font-size: vh(16);
              }

              .value {
                //
                width: vw(38);
                height: vh(22);
                font-weight: 900;
                color: #F7FAFD;
                font-size: vh(18);
              }
            }
          }
        }
      }
    }

    .body-top-right {
      border-radius: vw(24);
      width: vw(1214);
      height: vh(404);
      margin-left: vw(30);
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .body-bottom {

    width: vw(1835);
    height: vh(404);
    margin-top: vh(29);
    display: flex;
    justify-content: space-between;

    .body-bottom-block {

      width: vw(588);
      height: vh(404);
      border-radius: vw(24);
      // background: linear-gradient(180deg,
      //     rgba(15, 27, 52, 0.5) 0%,
      //     rgba(46, 97, 199, 0.5) 100%)
      background: url("../../assets/images/dashboard/left-infobox.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .box-title {
    display: flex;
    justify-content: center;
    position: relative;
    // padding-left: vw(51);
    padding-top: vh(9);
    top: vh(-1);
    width: vw(318);
    height: vh(49);
    margin: 0 auto;
    background: url('../../assets/images/dashboard/title.png') no-repeat;
    background-size: 100% 100%;

    .title-text {
      color: #fff;
      width: fit-content;
      height: vh(29);
      line-height: vh(29);
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: vw(22);
      letter-spacing: vw(1.3);
    }
  }
}
</style>
