<template>
    <div class="body-content">
      <div class="body-top">
        <div class="body-top-left">
          <PieChart :data="pieData" :width="'151px'" :height="'151px'" :color="['#1E88E5', '#FFC107', '#4CAF50', '#9C27B0', '#FF5722']"  :centerImageSize="0.3" />
        </div>
        <div class="body-top-right"></div>
      </div>
      <div class="body-middle">
      </div>
      <div class="body-bottom">
      </div>
    </div>
</template>
<script>
import PieChart from '@/components/charts/PieChart.vue'
export default {
  name: 'BodyContent',
  // props: {
  //   data: {
  //     type: Object,
  //     default: () => {
  //       return {}
  //     }
  //   }
  // },
  components: {
    PieChart
  },
  data() {
    return {
      data: {
        value_001: 1024,
        value_002: 14,
        value_003: 124,
        value_004: 10240,
        value_005: 1024,
        value_006: 1034,
        value_007: 1024,
        value_008: 156,
        value_009: 1818,
        value_010: 12223,
        value_011: 2456,
        value_012: 24,
        value_013: 245,
        value_025: 2460,
        value_027: 24666,
        value_029: 122,
        value_026: 84,
        value_028: 849,
        value_030: 8,
        value_031: 84,
        value_032: 40,
        value_033: 12,
        value_034: 245,
        value_035: 245,
        value_036: 2477,
        value_037: 245,
        value_014: 1.867,
        value_015: 7458,
        value_016: 2,
        value_017: 25,
        value_018: 245,
        value_019: 2477,
        value_020: 2,
        value_021: 245,
        value_022: 2245,
        value_023: 24225,
        value_024: 25,
        value_038: 1.221,
        value_039: 745,
        value_040: 25.2,
        value_041: 24,
        value_042: 1.25,
        value_043: 24,
        value_044: 24.5,
        value_045: 2.45,
        value_046: 22,
      },
      pieData: [
        { name: '中国移动', value: 50 },
        { name: '中国电信', value: 20 },
        { name: '中国联通', value: 15 },
        { name: '京宽网络', value: 10 },
        { name: '未知其他', value: 5 }
      ]
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
.body-content {
  width: 100%;
  flex: 1;
  display: flex;
  padding-left: vw(40);
  z-index: 99;
  flex-direction: column;
  .body-top{
    border:1px solid red; 
    width: vw(1835);
    height: vh(260);
    margin-top: vh(29);
    display: flex;
    .body-top-left{
      border:1px solid red; 
      width: vw(590);
      height: vh(260);
      border-radius: vw(24);
      background: linear-gradient(
        180deg,
        rgba(15, 27, 52, 0.5) 0%,
        rgba(46, 97, 199, 0.5) 100%
      )
 
    }
    .body-top-right{
      border:1px solid red; 
      width: vw(1211);
    height: vh(260);
    margin-left: vw(30);
    }
  }

  .body-middle{
    border:1px solid red; 
    width: vw(1835);
    height: vh(264);
    margin-top: vh(29);
  }

  .body-bottom{
    border:1px solid red; 
    width: vw(1835);
    height: vh(262);
    margin-top: vh(29);

  }
}

</style>
