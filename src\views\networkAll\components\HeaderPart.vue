<template>
  <div class="header-top">
    <div class="headerPart">
      <div class="option-btn">
        <!-- 返回上一级 -->
          <!-- <div :class="['btn','unactive']" @click="tabSwitch(1)">首页</div> -->
            <!-- 只有一个网络覆盖，直接高亮即可 -->
           <!-- <template v-for="(menu, index) in menus">
            <div v-if="!menu.child" :key="menu.menuId" :class="['btn', isActive === index + 1 ? 'active' : 'unactive']" @click="tabSwitch(index + 1)">{{ menu.menuName }}</div>
            <div v-else :key="menu.menuId" :class="['btn','unactive']">
              <el-select v-model="checkMarket" :placeholder="menu.menuName" popper-class="modul-select" @change="tabSwitch(index + 1)">
                <el-option
                  v-for="item in menu.child"
                  :key="item.meunUrl"
                  :label="item.menuName"
                  :value="item.meunUrl">
                </el-option>
              </el-select>
            </div>
        </template> -->
          <div :class="['btn','active']" @click="tabSwitch(2)">网络覆盖</div>
          <div :class="['btn',isActive === 3? 'active' : 'unactive']" >
            <el-select v-model="checkMarket" placeholder="市场洞察" popper-class="modul-select" @change="tabSwitch(3)">
              <el-option
                v-for="item in market"
                :key="item.path"
                :label="item.name"
                :value="item.path">
              </el-option>
            </el-select>
          </div>
          <div :class="['btn',isActive === 4? 'active' : 'unactive']">
            <el-select v-model="checkOperation" placeholder="政企洞察" popper-class="modul-select" @change="tabSwitch(4)">
              <el-option
                v-for="item in operation"
                :key="item.path"
                :label="item.name"
                :value="item.path">
              </el-option>
            </el-select>
          </div>
      </div>
      <div class="heder-change">
        <!-- <div class="back" @click="back">
          <div class="icon"></div>
          <div>返回上级</div>
        </div> -->
        <!-- <div class="time-part">
          <div class="day">{{ dateDay }}</div>
          <div class="time">{{ dateTime }}</div>
        </div> -->
      </div>
    </div>
    <div class="header-bottom">
      <div class="select">
        <div class="select-choice">
          <span>月份</span>
          <el-date-picker v-model="selectForm.statMon" @change="monthChange" :editable="false" placeholder="选择"
            popper-class="datePopperClass" type="month" value-format="yyyy-MM" :clearable="false" />
        </div>
        <div class="select-choice">
          <span>地市</span>
          <el-select v-model="selectForm.cityCode" @change="cityChange" :clearable="false" placeholder="请选择"
            popper-class="custom-select" size="mini">
            <el-option v-if="selectgridList.cityList.length !== 1" :key="'-1'" label="全部" :value="'-1'" />
            <el-option :key="item.areaId" :label="item.areaName" :value="item.areaId"
              v-for="item in selectgridList.cityList" />
          </el-select>
        </div>
        <div class="select-choice">
          <span>区县</span>
          <el-select v-model="selectForm.countyCode" @change="countyChange" :clearable="false" placeholder="请选择"
            popper-class="custom-select" size="mini">
            <el-option v-if="selectgridList.countyList.length !== 1"  :key="'-1'" label="全部" :value="'-1'" />
            <el-option :key="item.areaId" :label="item.areaName" :value="item.areaId"
              v-for="item in selectgridList.countyList" />
          </el-select>
        </div>
        <div class="select-choice" v-show="selectForm.coverType === '1'">
          <span>网格</span>
          <el-select v-model="selectForm.gridCode" @change="gridChange" :clearable="false" placeholder="请选择"
            popper-class="custom-select" size="mini">
            <el-option v-if="selectgridList.gridList.length !== 1"  :key="'-1'" label="全部" :value="'-1'" />
            <el-option :key="item.areaId" :label="item.areaName" :value="item.areaId"
              v-for="item in selectgridList.gridList" />
          </el-select>
        </div>
        <div class="select-choice">
          <span>覆盖类型</span>
          <el-select v-model="selectForm.coverType" @change="onFilterChange" :clearable="false" placeholder="请选择"
            popper-class="custom-select" size="mini">
            <el-option :key="item.value" :label="item.label" :value="item.value"
              v-for="item in selectgridList.coverList" />
          </el-select>
        </div>
        <div class="select-choice" v-show="selectForm.coverType === '2'">
          <span>数据类型</span>
          <el-select v-model="selectForm.dataType" @change="dataTypeChange" :clearable="false" placeholder="请选择"
            popper-class="custom-select" size="mini">
            <el-option :key="item.value" :label="item.label" :value="item.value"
              v-for="item in selectgridList.dataTypeList" />
          </el-select>
        </div>
      </div>
      <div v-drag class="right-part">
        <div class="task-distributed" @click="goBattle">
          <span>去作战></span>
        </div>
      </div>
    </div>
  </div>

</template>
<script>
import service from '../../../api/dashbord'
import { getDefaultStatMon, getDefaultStatMonW } from '@/api/network'
import common from '@/api/common'
export default {
  name: 'HeaderPart',
  filters: {
  },
  data() {
    return {
      isActive: 2,
      dateDay: null,
      dateTime: null,
      checkMarket: '',
      checkOperation: '',
      selectForm: {
        statMon: new Date().toISOString().slice(0, 7),
        cityCode: '-1',
        countyCode: '-1',
        gridCode: '-1',
        coverType: '1',
        dataType: 'OTT',
      },
      todayMonth: new Date().toISOString().slice(0, 7), // 当前月份
      market: [
        {
          name: '指标洞察',
          path: '/market/indicatorInsights'
        },
        {
          name: '任务调度',
          path: '/market/taskScheduling'
        },
         {
          name: '执行监控',
           path: '/market/executionMonitoring'
        }
      ],
      operation: [
        {
          name: '指标洞察',
          path: '/operation/indexInsight'
        },
        {
          name: '任务调度',
          path: '/operation/operationMap'
        },
        {
          name: '任务管理',
          path: '/operation/taskManagement'
        },
        {
          name: '执行监控',
          path: '/operation/taskAssessment'
        }
      ],
      selectgridList: {
        cityList: [
          {
            name: '昆明',
            value: '0'
          },
          {
            name: '大理',
            value: '1'
          },
          {
            name: '丽江',
            value: '2'
          }
        ],
        countyList: [
          {
            name: '五华区',
            value: '0'
          },
          {
            name: '官渡区',
            value: '1'
          },
          {
            name: '西山区',
            value: '2'
          }
        ],
        gridList: [
          {
            name: '爱琴海网格',
            value: '0'
          },
          {
            name: '街头网格',
            value: '1'
          },
          {
            name: '官渡网格',
            value: '2'
          }
        ],
        coverList: [
          {
            label: '宽带WIFI',
            value: '1'
          },
          {
            label: '无线',
            value: '2'
          }
        ],
        dataTypeList: [{
          label: 'OTT',
          value: 'OTT'
        },
        {
          label: 'MDT',
          value: 'MDT'
        }],
      },
      dashbordData: {},
    }
  },
  computed: {
    // menus() {
    //   return this.$store.state.menu.menus
    // }
  },
  watch: {
    'selectForm.coverType'(newVal, oldVal) {
      // 有线、无线页面切换
      // if (newVal !== oldVal) {
      //   console.log('selectForm.coverType切换');
      //   sessionStorage.setItem('coverType', newVal);
      //   this.checkCurrentMonthData();
      //   this.selectForm.cityCode = '-1'
      //   this.selectForm.countyCode = '-1'
      //   this.selectForm.gridCode = '-1'
      // }
    },
    $route(to, from) {
      console.log('watch--net', to, from)
      if (from.path === '/dashboard' && to.path === '/network') {
        this.selectForm.coverType = '1'
        this.selectForm.cityCode = '-1'
        this.selectForm.countyCode = '-1'
        this.selectForm.gridCode = '-1'
        // 选择无线，跳转首页，再进入网络覆盖页重置为有线
        this.$emit('updateData', { ...this.selectForm });
        this.$emit('changeTab', {
          coverType: this.selectForm.coverType,
          gridCode: this.selectForm.gridCode
        });
      }
    }

  },
  created() {
    const savedType = sessionStorage.getItem('coverType');

    if (savedType) {
      this.selectForm.coverType = savedType;
    }

    this.checkCurrentMonthData();
    // this.getUserInfo()
  },
  activated() {
    this.getUserCode()
    this.checkMarket = ''
    this.checkOperation = ''
  },
  async mounted() {
    // console.log('this.$route.query.from',this.$route);
    // if(this.$route.path==='/network'){
    //   this.selectForm.coverType='1'
    // }

    this.timeFn()
    await this.getAreaByPid(999, 'cityList')
    this.getUserCode()
  },
  methods: {
    goBattle() {
      console.log(this.selectForm)
      sessionStorage.setItem('selectgridList', JSON.stringify(this.selectgridList));
      if (this.selectForm.coverType === '1') {
        //跳转有线地图
        this.$router.push({path: '/networkCoverage/wired', query: { ...this.selectForm, from: 'network' }})
      } else {
        this.$router.push({path:'/networkCoverage/wireless', query: { ...this.selectForm, from: 'network' }})
      }
    },
    tabSwitch(type) {
      // this.isActive = type
      if (type === 1) {
        this.$router.push('/dashboard')
      } else if (type === 3) {
        this.$router.push(this.checkMarket)
      } else if (type === 4) {
        this.$router.push(this.checkOperation)
      }
      this.$emit('changeTab', {
        coverType: this.selectForm.coverType,
        gridCode: this.selectForm.gridCode
      })
    },
    timeFn() {
      this.timing = setInterval(() => {
        this.dateTime = new Date().Format('hh:mm:ss')
        this.dateDay = new Date().Format('yyyy/MM/dd')
      }, 1000)
    },
    async checkCurrentMonthData() {
      const isWired = this.selectForm.coverType === '1';
      const commonParams = {
        statMon: this.todayMonth,
        cityCode: "-1",
        countyCode: "-1"
      };

      const params = isWired
        ? { ...commonParams, gridCode: "-1" }
        : { ...commonParams, gridType: this.dataType };

      const fetchFn = isWired ? getDefaultStatMon : getDefaultStatMonW;
      try {
        const res = await fetchFn(params);

        if (res.code === 200) {
          this.selectForm.statMon = res.data;

          this.$nextTick(() => {
            this.$emit('updateData', { ...this.selectForm });
          });
        } else {
          this.$message.info('当前月份暂无数据，请选择其他月份');
        }
      } catch (err) {
        console.error('查询月份数据失败:', err);
      }

    },
    // 获取下拉框选择列表
    async getAreaByPid(code, listName) {
      await common.getAreaByPid({
        areaPid: code
      }).then(res => {
        if (res.code === 200) {
          this.selectgridList[listName] = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async onFilterChange() {
      sessionStorage.setItem('coverType', this.selectForm.coverType);
      await this.checkCurrentMonthData();

      if (this.selectgridList.cityList.length !== 1) {
        this.selectForm.cityCode = '-1'
      }
      if (this.selectgridList.countyList.length !== 1) {
        this.selectForm.countyCode = '-1'
      }
      if (this.selectgridList.gridList.length !== 1) {
        this.selectForm.gridCode = '-1'
      }
      this.selectForm.dataType = 'OTT'
      // 通知父组件页面切换
      this.$emit('changeTab', {
        coverType: this.selectForm.coverType,
        gridCode: this.selectForm.gridCode
      });

      this.$emit('updateData', { ...this.selectForm });
    },
    dataTypeChange() {
      this.$emit('updateData', { ...this.selectForm });
    },
    //TODO: 地市、区县、网格编码信息尚未获取
    monthChange(value) {
      console.log(value)
      this.$emit('updateData', { ...this.selectForm });
    },
    // getUserInfo() {
    //   const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    //   this.areaLevel = userInfo.roleType || 1
    // },
    async getUserCode() {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      switch (userInfo.roleType) {
        case 1:
          break
        case 2:
          this.selectForm.cityCode = userInfo.cityId
          await this.getAreaByPid(userInfo.cityId, 'countyList')
          // 过滤调其它市区
          this.selectgridList['cityList'] = this.selectgridList['cityList'].filter(item => item.areaId === userInfo.cityId)
          this.$emit('updateData', { ...this.selectForm })
          // this.cityChange()
          break
        case 3:
          if (userInfo.cityId) {
            this.selectForm.cityCode = userInfo.cityId
            await this.getAreaByPid(userInfo.cityId, 'countyList')
            // 过滤调其它市区
            this.selectgridList['cityList'] = this.selectgridList['cityList'].filter(item => item.areaId === userInfo.cityId)
          }
          if (userInfo.countyId) {
            this.selectForm.countyCode = userInfo.countyId
            await this.getAreaByPid(this.selectForm.countyCode, 'gridList')
            // 过滤调其它市区
            this.selectgridList['countyList'] = this.selectgridList['countyList'].filter(item => item.areaId === userInfo.countyId)
          }
          this.$emit('updateData', { ...this.selectForm })
          break
        case 4:
          if (userInfo.cityId) {
            this.selectForm.cityCode = userInfo.cityId
            await this.getAreaByPid(userInfo.cityId, 'countyList')
            // 过滤调其它市区
            this.selectgridList['cityList'] = this.selectgridList['cityList'].filter(item => item.areaId === userInfo.cityId)
          }
          if (userInfo.countyId) {
            this.selectForm.countyCode = userInfo.countyId
            await this.getAreaByPid(this.selectForm.countyCode, 'gridList')
            // 过滤调其它市区
            this.selectgridList['countyList'] = this.selectgridList['countyList'].filter(item => item.areaId === userInfo.countyId)
          }
          this.selectForm.gridCode = userInfo.gridId
          // 过滤调其它街道
          this.selectgridList['gridList'] = this.selectgridList['gridList'].filter(item => item.areaId === userInfo.gridId)
          this.$emit('updateData', { ...this.selectForm })
          this.$emit('changeTab', {
            coverType: this.selectForm.coverType,
            gridCode: userInfo.gridId
          });
      }
    },
    cityChange(value) {
      console.log(value)
      this.selectForm.cityName = this.selectgridList.cityList.find(item => item.areaId === value)?.areaName || '全部'
      this.selectForm.cityCode = value
      this.selectForm.countyCode = '-1'
      this.selectForm.gridCode = '-1'
      this.getAreaByPid(value, 'countyList')
      this.$emit('updateData', { ...this.selectForm });
      // if (this.selectForm.cityCode === '-1') {
        // 通知父组件页面切换
        this.$emit('changeTab', {
          coverType: this.selectForm.coverType,
          gridCode: this.selectForm.gridCode
        });
      // }

    },
    countyChange(value) {
      console.log(value)
      this.selectForm.countyCode = value
      this.selectForm.countyName = this.selectgridList.countyList.find(item => item.areaId === value)?.areaName || '全部'
      //网格编码清空
      this.selectForm.gridCode = '-1'
      this.getAreaByPid(value, 'gridList')
      this.$emit('updateData', { ...this.selectForm });
      this.$emit('changeTab', {
          coverType: this.selectForm.coverType,
          gridCode: this.selectForm.gridCode
        });
    },
    gridChange(value) {
      console.log(value)
      this.selectForm.gridCode = value
      this.selectForm.gridName = this.selectgridList.gridList.find(item => item.areaId === value)?.areaName || '全部'
      this.$emit('updateData', { ...this.selectForm });
      // 通知父组件页面切换
      this.$emit('changeTab', {
        coverType: this.selectForm.coverType,
        gridCode: this.selectForm.gridCode
      });
    },

    //地区改变获得新的展示数据
    getHomeIndex() {
      const statDate = this.dateDay.Format('yyyy-MM-dd')
      service.getHomeIndex({
        cityCode: this.selectForm.cityCode,
        countyCode: this.selectForm.countyCode,
        gridCode: this.selectForm.gridCode,
        statDate: statDate
      }).then(res => {
        if (res.code === 200) {
          this.dashbordData = res.data
        } else {
          this.$message.error('数据获取失败')
        }
      })
    },
    back() {
      this.$router.push('/')
    },
  },

}

</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;

@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

.header-top {
  width: 100%;
  height: vh(138);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.header-bottom {
  width: calc(vw(1835) - vw(46));
  height: vh(32);
  margin-left: vw(46);
  position: relative;

  .datePopperClass {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;

    .el-date-picker__header-label {
      color: #ffffff !important;
    }

    .el-month-table td .cell {
      color: #ffffff !important;
    }

    .el-month-table td.current:not(.disabled) .cell {
      color: #409eff !important;
    }

    .el-date-picker__header--bordered {
      border-color: #677aa5;
    }
  }

  .el-date-editor.el-input {
    width: vw(120);
  }

  // ::v-deep .el-input__inner {
  //   width: vw(120) !important;
  // }

  .right-part {
    position: absolute;
    bottom: vh(0);
    right: vw(0);

    .task-distributed {
      width: vw(162);
      height: vh(49);
      line-height: vh(49);
      margin-left: vw(53);
      background: url("~@/assets/images/networkCoverage/distributed.png") no-repeat;
      background-size: 100% 100%;
      text-align: center;
      cursor: pointer;

      span {
        background-image: linear-gradient(180deg, #ffffff 50.17%, #94e5ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vw(24);
      }
    }

    .checkbox-group {
      margin: vh(24) 0;
      border: vw(2) solid transparent;
      padding: vh(15) 0 vh(17) vw(22);
      border-radius: 10px;
      width: vw(215);
      background: linear-gradient(180deg, #254177 0%, #1f4a9c 100%) padding-box,
        radial-gradient(62.59% 72.5% at 3.63% 87.5%,
          rgba(93, 147, 255, 0.2) 0%,
          rgba(0, 0, 0, 0.2) 100%) padding-box,
        radial-gradient(50% 50% at 50% 50%,
          rgba(83, 140, 253, 0) 0%,
          rgba(83, 140, 253, 0.2) 100%) padding-box,
        linear-gradient(90deg,
          rgba(178, 209, 255, 0.5) 0%,
          rgba(123, 176, 255, 0.25) 50%,
          rgba(178, 209, 255, 0.5) 100%) border-box;

      .el-checkbox-group {
        display: flex;
        flex-direction: column;

        >label {
          &:first-child {
            margin-bottom: vh(10);
          }
        }
      }

      ::v-deep .el-checkbox {
        display: inline-flex;
        align-items: center;
      }

      ::v-deep .el-checkbox__label {
        color: #e4eeff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
      }

      ::v-deep .el-checkbox__inner {
        border: 2px solid #74a2ff;
        background-color: transparent;
        width: vh(16);
        height: vh(16);
      }

      ::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #e4eeff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
      }

      ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
        background-color: #345596;
      }

      ::v-deep .el-checkbox__inner::after {
        border-color: #ffffff;
        top: -2px;
        height: 10px;
      }

      ::v-deep .el-checkbox__inner::before {
        top: 4px;
      }

      ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #253a65;
      }
    }

    .compare-icon {
      ::v-deep .el-button {
        float: right;
        padding: vh(12) vw(7);
        color: #e4eeff;
        background: linear-gradient(181.96deg,
            #6498ff -38.96%,
            #1d4ba7 39.59%,
            #142d60 98.35%) padding-box,
          linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
        border: 1.5px solid transparent;
        box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: vw(14);
      }

      ::v-deep .el-icon-my-zdy {
        background: url("~@/assets/images/networkCoverage/union.png") center no-repeat;
        font-size: 12px;
        background-size: cover;
      }

      ::v-deep .el-icon-my-zdy:before {
        content: "替";
        font-size: vw(14);
        visibility: hidden;
      }

      ::v-deep .el-icon-my-zdy {
        font-size: vw(14);
      }

      ::v-deep .el-icon-my-zdy:before {
        content: "\e611";
      }
    }
  }

  .select {
    width: vw(1200);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .select-choice {
      display: flex;
      font-size: vw(16);
      color: #ffffff;
      align-items: center;
      width: vw(240);
      height: vh(22);
      line-height: vh(36);

      span {
        margin-right: vw(20);
        line-height: vh(36);
      }

      ::v-deep el-date-editor {
        display: flex;
        align-items: center;
      }

      ::v-deep .el-picker-panel {
        background-color: #060f22b2;
      }

      ::v-deep .el-input__prefix,
      .el-input__suffix {

        // top: vw(-8);
        .el-input__icon {
          line-height: 0;
        }
      }

      ::v-deep .el-input__inner {
        width: vw(150) !important;
        height: vh(32);
        line-height: vh(32);
        background-color: #141e32;
        border-color: #677aa5;
        border-radius: vw(4);
        font-size: vw(14);
        color: #ffffff !important;
      }

      ::v-deep .el-input__suffix-inner .el-icon-arrow-up:before {
        content: "\e78f";
      }

      ::v-deep .el-select {
        font-size: vw(14);
      }
    }
  }

  .goBattle {
    width: vw(140);
    height: vh(49);
    position: absolute;
    bottom: 0;
  }
}

.headerPart {
  width: 100%;
  height: vh(86);
  line-height: vh(86);
  display: flex;
  justify-content: space-between;
  background: url("../../../assets/images/dashboard/header-background.png") no-repeat;
  background-size: 100% 100%;
  padding-left: vw(547);
}

.option-btn {
  width: vw(560);
  height: vh(32);
  // background-color: aqua;
  margin-top: vh(11);
  display: flex;

  .btn {
    width: vw(144);
    height: vh(34);
    font-family: PingFang SC;
    font-weight: 600;
    font-size: vw(16);
    line-height: vh(34);
    text-align: center;
    cursor: pointer;
    ::v-deep .el-select {
      .el-input__suffix {
        opacity: 0;
      }
      .el-input__inner {
        background-color: transparent;
        border: none;
        height: vh(34);
        font-size: vw(16);
        color: rgba(242, 248, 255, 1);
        padding: 0;
        text-align: center;
      }
    }
  }

  .active {
    color: rgba(242, 248, 255, 1);
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background: url("../../../assets/images/dashboard/active.png") no-repeat;
    background-size: 100% 100%;
  }

  .unactive {
    color: rgba(190, 217, 255, 1);
    background: url("../../../assets/images/dashboard/unactive.png") no-repeat;
    background-size: 100% 100%;
  }
}

.heder-change {
  margin-top: vh(14);
  margin-right: vw(30);
  height: vh(25);
  color: #b0d0ff;
  display: flex;
  align-items: flex-start;
  align-items: center;
  cursor: pointer;
  line-height: vh(25);

  .back {
    width: vw(123);
    height: vh(30);
    background: url("../../../assets/images/operation/rectangle.png") no-repeat;
    background-size: 100% 100%;
    padding: vh(3.62) 0 0 vw(16.5);
    display: flex;
    cursor: pointer;

    div {
      width: vw(84);
      height: vh(21);
      font-size: vw(14);
      line-height: 150%;
    }

    .icon {
      width: vw(22);
      height: vh(20);
      background: url("../../../assets/images/market/icon.png") no-repeat;
      background-size: 100% 100%;
      margin-right: vw(6);
    }
  }

  .time-part {
    width: vw(175);
    height: vh(25);
    display: flex;
    justify-content: space-between;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(18);
    line-height: 100%;

  }
}
</style>
<style lang="scss">
@use "sass:math";
$designWidth: 1920;
$designHeight: 1080;

@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}

@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

.custom-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;

  .popper__arrow {
    border-bottom-color: #677aa5 !important;

    &::after {
      border-bottom-color: #141e32 !important;
    }
  }

  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    // height: vh(34);
    // line-height: vh(34);
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}

.datePopperClass {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;

  .el-date-picker__header-label {
    color: #ffffff !important;
  }

  .el-month-table td .cell {
    color: #ffffff !important;
  }

  .el-month-table td.current:not(.disabled) .cell {
    color: #409eff !important;
  }

  .el-date-picker__header--bordered {
    border-color: #677aa5;
  }
}
.modul-select {
  background-color: #1d4876 !important;
  border: none !important;
  // color: #ccd3d9;
  .el-select-dropdown__item {
    font-size: vw(16);
    color: white !important;
    height: vh(30) !important;
    line-height: vh(30);
  }
  .hover {
    background-color: #0f8bff !important;
  }
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #1d4876 !important;
    }
  }
}
</style>
