<template>
  <div
    v-loading="loading"
    class="screen-adapter"
    element-loading-background="rgba(0, 0, 0, 0.5)"
    element-loading-spinner="el-icon-loading loading iconfont icon-loading"
    element-loading-text="系统正在加载中......"
  >
    <div :style="style" class="content-wrap">
      <slot />
    </div>
  </div>
</template>

<script>
import { EventBus } from '../eventBus'
export default {
  name: 'ScreenAdapter',
  props: {
    w: {
      type: Number,
      default: 1920
    },
    h: {
      type: Number,
      default: 1080
    }
  },
  data() {
    return {
      loading: false,
      style: {
        width: `${this.w}px`,
        height: `${this.h}px`,
        transform: 'scale(1) translate(-50%, -50%)' // 默认不缩放，垂直水平居中
      }
    }
  },
  watch: {
    w: 'updateStyle',
    h: 'updateStyle'
  },
  mounted() {
    this.setScale()
    this.onresize = this.debounce(() => {
      this.setScale()
    }, 100)
    window.addEventListener('resize', this.onresize)
    EventBus.$on('start-loading', () => (this.loading = true))
    EventBus.$on('stop-loading', () => (this.loading = false))
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onresize)
    EventBus.$on('start-loading', () => (this.loading = true))
    EventBus.$on('stop-loading', () => (this.loading = false))
  },
  methods: {
    debounce(fn, t) {
      const delay = t || 500
      let timer
      return function() {
        const args = arguments
        if (timer) {
          clearTimeout(timer)
        }
        const context = this
        timer = setTimeout(() => {
          timer = null
          fn.apply(context, args)
        }, delay)
      }
    },
    getScale() {
      const w = window.innerWidth / this.w
      const h = window.innerHeight / this.h
      return w < h ? w : h
    },
    setScale() {
      const scale = this.getScale()
      this.style = {
        width: `${this.w}px`,
        height: `${this.h}px`,
        transform: `scale(${scale}) translate(-50%, -50%)`
      }
    },
    updateStyle() {
      // 当 props 变化时重新计算缩放
      this.setScale()
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-adapter {
  width: 100vw;
  min-height: 100%;
  max-height: 100vh;
  overflow: hidden;
  background: url("../../../assets/images/dashboard/background.png")
    no-repeat;
  background-size: 100% 100%;

  .content-wrap {
    display: flex;
    flex-direction: column;
    transform-origin: 0 0;
    transition: transform 0.3s;
    position: absolute;
    top: 50%;
    left: 50%;
  }
}
</style>
