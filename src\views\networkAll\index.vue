<template>
  <ScreenAdapter :h="screenHeight" :w="screenWidth">
    <HeaderPart @changeTab="changeTab" @updateData="updateData" />
    <div class="body-content">
     <component :is="activeComponent" :filter-data="filterData" ></component>
      <!-- <keep-alive> 
        <router-view v-if="$route.meta.keepAlive"  :key="key":id="key" :filter-data="filterData"></router-view>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive"  :key="key" :id="key+'222'"  :filter-data="filterData"></router-view> -->
    </div>
    <BottomPart />
  </ScreenAdapter>
</template>
<script>
import BottomPart from './components/BottomPart.vue'
import HeaderPart from './components/HeaderPart'
import ScreenAdapter from './components/ScreenAdapter.vue'
import BodyContent from './components/BodyContent.vue'
import NetworkWired from './NetworkWired.vue'
import WiredlessNet from './WiredlessNet.vue'
import WiredNet from './WiredNet.vue'
export default {
  name: 'network',
  components: {
    ScreenAdapter,
    HeaderPart,
    BottomPart,
    BodyContent,
    NetworkWired,
    WiredlessNet,
    WiredNet
  },
  watch: {
    $route(to, from) {
     console.log('watch', to, from)
    }
  },
  data() {
    return {
      activeComponent: 'NetworkWired', // 默认显示组件
      screenWidth: 1920, // 默认值
      screenHeight: 1080, // 默认值
      dashbordData: {},
      filterData: {}
    }
  },
  computed: {
    key() {
      return this.$route.path
    }
  },
  created() {
    const savedType = sessionStorage.getItem('coverType');
    console.log('savedType',savedType);
    
    if(savedType){
      savedType==='1'?this.activeComponent = 'NetworkWired':this.activeComponent = 'WiredlessNet'
    }

  },
  mounted() {
    this.updateScreenSize()
    this.onresize = this.debounce(() => this.updateScreenSize(), 100)
    window.addEventListener('resize', this.onresize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onresize)
    
  },
  // 页面被缓存时退出执行
  deactivated() {
    sessionStorage.removeItem('coverType')
  },
  methods: {
    debounce(fn, delay = 500) {
      let timer
      return function (...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          timer = null
          fn.apply(this, args)
        }, delay)
      }
    },
    updateScreenSize() {
      this.screenWidth = window.innerWidth
      this.screenHeight = window.innerHeight
    },
    changeTab({ coverType, gridCode }) {
      console.log('changeTab',coverType, gridCode)
       let targetRoute = '';

      if (coverType === '2') {
        this.activeComponent = 'WiredlessNet';
        //targetRoute = '/network/wireless';
      } else{
        if (gridCode === '-1') {
        this.activeComponent = 'NetworkWired';
      } else {
        this.activeComponent = 'WiredNet';
      }
      }

      // if (this.$route.path !== targetRoute) {
      //   this.$router.push(targetRoute);
      //   this.currentRouteKey = targetRoute; // 强制 router-view 刷新
      // }
    },
    updateData(data) {
      console.log('updateData')
      this.filterData = data;
    },
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.body-content {
  position: relative;
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  z-index: 99;
}
</style>
