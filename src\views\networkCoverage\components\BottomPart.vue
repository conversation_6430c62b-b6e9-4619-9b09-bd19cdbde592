<template>
  <div class="bottomPart" />
</template>
<script>
export default {
  name: 'BottomPart',
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.bottomPart {
  width: 100%;
  height: vh(185);
  line-height: vh(185);
  background: url("../../../assets/images/dashboard/bottom-background.png")
    no-repeat;
  background-size: 100% 100%;
  position: absolute;
  bottom: 0;
}
</style>
