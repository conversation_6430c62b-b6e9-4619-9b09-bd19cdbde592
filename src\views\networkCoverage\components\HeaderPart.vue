<template>
  <div class="headerPart">
    <div class="header-content">
      <div class="header-tabs">
        <div
          v-for="item in menu"
          :key="item.name"
          :class="{'active': item.name === menuActive}"
          class="header-tabs-item"
          @click="navClick(item)"
        >{{ item.menuName }}</div>
      </div>
      <div class="heder-change">
        <div class="back-icon" @click="backNetworkHome()">
          <div class="back-icon-img" />
          <div class="back-text">返回上级</div>
        </div>
        <!-- <div class="time-part">
          <div class="day">{{ dateDay }}</div>
          <div class="time">{{ dateTime }}</div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'HeaderPart',
  props: {
    menuActive: {
      type: String,
      default: ''
    },
    menu: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dateDay: null,
      dateTime: null
    }
  },
  mounted() {
    // this.timeFn()
  },
  methods: {
    backNetworkHome() {
      this.$router.push('/')
    },
    timeFn() {
      this.timing = setInterval(() => {
        this.dateTime = new Date().Format('hh:mm:ss')
        this.dateDay = new Date().Format('yyyy/MM/dd')
      }, 1000)
    },
    navClick(menuItem) {
      if (menuItem === this.menuActive) return
      this.$emit('navclick', menuItem)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.headerPart {
  width: 100%;
  height: vh(86);
  line-height: vh(86);
  display: flex;
  background: url("../../../assets/images/dashboard/header-background.png")
    no-repeat;
  background-size: 100% 100%;
  flex: 0 0 auto;
  z-index: 999;
}
.header-content {
  width: 100%;
  margin-left: vw(547);
  display: flex;
  justify-content: space-between;
  .header-tabs {
    display: flex;
    margin-top: vh(11);
    .header-tabs-item {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      line-height: vh(32);
      background: url("../../../assets/images/operation/rectangle.png")
        no-repeat;
      color: #bed9ff;
      width: vw(140);
      height: vh(32);
      text-align: center;
      background-size: 100% 100%;
      cursor: pointer;
    }

    .active {
      @extend.header-tabs-item;
      background: url("../../../assets/images/operation/rectangle-active.png")
        no-repeat;
      color: #f2f8ff;
      background-size: 100% 100%;
    }
  }

  .heder-change {
    margin-top: vh(10);
    margin-right: vw(10);
    display: flex;
    align-items: flex-start;
    line-height: vh(29.12);

    .back-icon {
      display: flex;
      align-items: center;
      width: vw(122.08);
      height: vh(29.12);
      line-height: vh(29.12);
      cursor: pointer;
      background: url("../../../assets/images/networkCoverage/rectangle.png")
        no-repeat;
      background-size: 100% 100%;

      .back-text{
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        color: #b0d0ff;
        background-image: linear-gradient(180deg, #FFFFFF 31.54%, #A2C2F1 73.33%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }

      .back-icon-img {
        margin-left: vw(17.4);
        width: vw(22);
        height: vh(20);
        background: url("~@/assets/images/networkCoverage/back.png") no-repeat;
        background-size: 100% 100%;
        margin-right: vw(6);
      }
    }

    .time-part {
      width: vw(175);
      margin-left: vw(7);
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      color: #b0d0ff;
      display: flex;
      >div{
        &:first-child{
          margin-right: vw(5);
        }
      }
    }
  }
}
</style>
