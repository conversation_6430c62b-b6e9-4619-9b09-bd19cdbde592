<template>
  <div
    v-loading="loading"
    class="map-part"
    element-loading-background="rgba(0, 0, 0, 0.5)"
    element-loading-spinner="el-icon-loading loading iconfont icon-loading"
    element-loading-text="系统正在加载中......"
  >
    <div class="form-part">
      <el-form :inline="true" :model="selectForm" class="form-inline">
        <el-form-item class="dateClass" label="时间">
          <el-date-picker
            v-model="selectForm.statMon"
            :editable="false"
            placeholder="选择月"
            popper-class="datePopperClass"
            type="month"
            value-format="yyyy-MM"
            @change="updateMapInfo"
          />
        </el-form-item>
        <el-form-item label="地市">
          <el-select v-model="selectForm.cityCode" :clearable="false" placeholder="请选择" popper-class="custom-select" size="mini" @change="cityChange">
            <el-option v-for="item in selectAreaList.cityList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="区县">
          <el-select
            v-model="selectForm.countyCode"
            :clearable="false"
            placeholder="请选择"
            popper-class="custom-select"
            size="mini"
            @change="countyChange"
          >
            <el-option v-for="item in selectAreaList.countyList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="网格">
          <el-select v-model="selectForm.areaCode" :clearable="false" placeholder="请选择" popper-class="custom-select" size="mini" @change="areaChange">
            <el-option v-for="item in selectAreaList.areaList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="行政村">
          <el-select
            v-model="selectForm.villageCode"
            :clearable="false"
            class="long-inputer"
            placeholder="请选择"
            popper-class="custom-select"
            size="mini"
            @change="villageChange"
          >
            <el-option v-for="item in selectAreaList.villageList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="小区">
          <el-select
            v-model="selectForm.cellId"
            class="long-inputer"
            :clearable="false"
            placeholder="请选择"
            popper-class="custom-select"
            size="mini"
            @change="cellChange"
          >
            <el-option v-for="item in selectAreaList.cellList" :key="item.areaId" :label="item.areaName" :value="item.areaId" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="keys==='wirelessMap'" label="数据类型">
          <el-select
            v-model="selectForm.gridType"
            :clearable="false"
            placeholder="请选择"
            popper-class="custom-select"
            size="mini"
            @change="updateMapInfo"
          >
            <el-option v-for="item in selectAreaList.dataTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="currentArea.areaLevel <3" :id="`${keys}echart-map-wrap`" :key="`echarts-${keys}`" class="echartsMapWrap" />
    <div v-else :id="`${keys}baidu-map-wrap`" :key="`baidu-map-${keys}`" class="map-wrap" />
    <MapTypeSelector v-if="currentArea.areaLevel>=3" @change="onMapTypeChange" />
    <EchartsLegend :arealevel="currentArea.areaLevel" :legend-list="legendList" />
  </div>
</template>
<script>
import MapTypeSelector from './MapTypeSelector.vue'
import * as echarts from 'echarts'
import { fitChartSize } from '@/utils/dataUtil.js'
import { getMapAreaInfo, getFqxData } from '@/api/networkCoverage'
import EchartsLegend from '@/components/common/EchartsLegend.vue'
import { formatNumberWithUnit } from '@/utils/dataUtil.js'
import iconG from '@/assets/images/networkCoverage/icon-g.png'
import iconD from '@/assets/images/networkCoverage/icon-d.png'
import iconL from '@/assets/images/networkCoverage/icon-l.png'
import iconY from '@/assets/images/networkCoverage/icon-y.png'
import iconP from '@/assets/images/networkCoverage/icon-point.png'
import purpleStyle from '../style'
import { mapActions, mapState } from 'vuex'
export default {
  name: 'MapPart',
  components: { MapTypeSelector, EchartsLegend },
  props: {
    keys: {
      type: String,
      default: ''
    },
    // 宽带占比-1 分纤箱分布-2
    category: {
      type: Number,
      default: 1
    },
    // 行政村-1 小区-2
    areaType: {
      type: Number,
      default: 1
    },
    opticalDataType: {
      type: Number,
      default: 1
    },
    showSecond: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      selectForm: {
        statMon: new Date('2025-07').Format('yyyy-MM'),
        cityCode: '',
        countyCode: '',
        areaCode: '',
        villageCode: '',
        cellId: '',
        gridType: 'OTT'
      },
      selectAreaList: {
        cityList: [],
        countyList: [],
        areaList: [],
        villageList: [],
        cellList: [],
        dataTypeList: [
          {
            label: 'OTT',
            value: 'OTT'
          },
          {
            label: 'MDT',
            value: 'MDT'
          }
        ]
      },
      mapChart: null,
      map: null,
      mapvLayer: null,
      villageMapvLayer: null,
      labelMapvLayer: null,
      mapTypes: null,
      mapJson: {}, // 地图边界值
      mapBorderJson: {}, // 地图白色边框以及地图底层边界值
      mapData: {}, // 地图悬浮数据
      geoCoordMap: {}, // 悬浮数据中心点
      currentArea: {
        areaId: '999',
        areaLevel: '1'
      },
      labelName: [],
      subLabelName: [],
      PolygonCenter: [],
      polygonPaths: [],
      center: {},
      pointCenter: {},
      level_zoom: {
        1: 8,
        2: 10,
        3: 12,
        4: 14,
        5: 16,
        6: 18
      },
      polygonOverlays: [],
      labelOverlays: [],
      subLabelOverlays: [],
      markers: [],
      bubble: true,
      centerAndZoom: {}
    }
  },
  computed: {
    ...mapState('networkCoverage', ['commonCurrentArea', 'commonSelectForm', 'commonSelectAreaList', 'updateKey']),
    zoom() {
      return this.level_zoom[Number(this.currentArea.areaLevel)]
    },
    legendList() {
      if (this.keys === 'wirelessMap') {
        // 无线的部分
        if (this.currentArea.areaLevel < 5) {
          return [
            { color: '#FF393980', borderColor: '#FF8787', text: '优势栅格比例＜25%' },
            { color: '#FF838380', borderColor: '#FFBCB9', text: '25%≤优势栅格比例＜50%' },
            { color: '#37D89280', borderColor: '#A9FFDA', text: '50%≤优势栅格比例＜75%' },
            { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '75%≤优势栅格比例≤100%' }
          ]
        }
        return [
          { color: '#35FF4C99', borderColor: '#FFFFFF', text: '优势' },
          { color: '#FFE01699', borderColor: '#FFFFFF', text: '持平' }
        ]
      } else {
        // 有线的部分
        if (this.currentArea.areaLevel < 5) {
          return [
            { color: '#FF393980', borderColor: '#FF8787', text: '宽带占比＜25%' },
            { color: '#FF838380', borderColor: '#FFBCB9', text: '25%≤宽带占比＜50%' },
            { color: '#37D89280', borderColor: '#A9FFDA', text: '50%≤宽带占比＜75%' },
            { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '75%≤宽带占比≤100%' }
          ]
        } else if (this.category === 1) {
          return [
            { color: '#FF161899', borderColor: '#FFFFFF', text: '0≤宽带占比≤20' },
            { color: '#FFE01699', borderColor: '#FFFFFF', text: '20＜宽带占比≤40' },
            { color: '#D52CFF99', borderColor: '#FFFFFF', text: '40＜宽带占比＜60' },
            { color: '#2CA3FF99', borderColor: '#FFFFFF', text: '60＜宽带占比＜80' },
            { color: '#35FF4C99', borderColor: '#FFFFFF', text: '80＜宽带占比≤100' }
          ]
        }
        return [
          { color: '#FF393980', borderColor: '#FF8787', text: '移动光交箱', icon: iconP },
          { color: '#FF838380', borderColor: '#FFBCB9', text: '移动分纤箱', icon: iconY },
          { color: '#37D89280', borderColor: '#A9FFDA', text: '电信分纤箱', icon: iconD },
          { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '联通分纤箱', icon: iconL },
          { color: '#FF838380', borderColor: '#FFBCB9', text: '广电分纤箱', icon: iconG }
        ]
      }
    }
  },
  watch: {
    updateKey() {
      if (this.showSecond) {
        this.currentArea = this.commonCurrentArea
        this.selectForm = { ...this.commonSelectForm, statMon: this.selectForm.statMon }
        this.selectAreaList = this.commonSelectAreaList
        this.getMapInfo({ ...this.currentArea })
      }
    }
  },

  mounted() {
    this.getUserAreaParam()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    ...mapActions({
      setArea: 'networkCoverage/setArea',
      setForm: 'networkCoverage/setForm',
      setSelectAreaList: 'networkCoverage/setSelectAreaList',
      setUpdateKey: 'networkCoverage/setUpdateKey'
    }),
    /**
     * @description 获取用户权限,如果是对比图就不需要获取用户信息
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    getUserAreaParam() {
      if (this.showSecond) {
        this.currentArea = this.commonCurrentArea
        this.selectForm = { ...this.commonSelectForm, statMon: this.selectForm.statMon }
        this.selectAreaList = this.commonSelectAreaList
      } else if (this.$route.query.from && this.$route.query.from === 'network') {
        if (this.$route.query.gridCode !== '-1') {
          this.currentArea.areaLevel = '4'
          this.currentArea.areaId = this.$route.query.gridCode
        } else if (this.$route.query.countyCode !== '-1') {
          this.currentArea.areaLevel = '3'
          this.currentArea.areaId = this.$route.query.countyCode
        } else if (this.$route.query.cityCode !== '-1') {
          this.currentArea.areaLevel = '2'
          this.currentArea.areaId = this.$route.query.cityCode
        } else {
          this.currentArea.areaLevel = '1'
          this.currentArea.areaId = '999'
        }
        this.selectForm.cityCode = this.$route.query.cityCode !== '-1' ? this.$route.query.cityCode : ''
        this.selectForm.countyCode = this.$route.query.countyCode !== '-1' ? this.$route.query.countyCode : ''
        this.selectForm.areaCode = this.$route.query.gridCode !== '-1' ? this.$route.query.gridCode : ''
        const data  = sessionStorage.getItem('selectgridList') ? JSON.parse(sessionStorage.getItem('selectgridList')) : {}
        this.selectAreaList.cityList = data.cityList ? data.cityList : []
        this.selectAreaList.countyList = data.countyList ? data.countyList : []
        this.selectAreaList.areaList = data.gridList ? data.gridList : []
        sessionStorage.removeItem('selectgridList')
      } else {
        const user = JSON.parse(window.sessionStorage.getItem('userInfo'))
        if (user) {
          if (user.roleType) {
            this.currentArea.areaLevel = '1'
            this.currentArea.areaId = '999'
            if (user.roleType === 1) {
              this.currentArea.areaId = user.provId
            } else if (user.roleType === 2) {
              this.currentArea.areaLevel = '2'
              this.currentArea.areaId = user.cityId
              this.selectAreaList.cityList = [{ areaName: user.cityName, areaId: user.cityId }]
            } else if (user.roleType === 3) {
              this.currentArea.areaLevel = '3'
              this.currentArea.areaId = user.countyId
              this.selectAreaList.cityList = [{ areaName: user.cityName, areaId: user.cityId }]
              this.selectAreaList.countyList = [{ areaName: user.countyName, areaId: user.countyId }]
            } else if (user.roleType === 4) {
              this.currentArea.areaLevel = '4'
              this.currentArea.areaId = user.gridId
              this.selectAreaList.cityList = [{ areaName: user.cityName, areaId: user.cityId }]
              this.selectAreaList.countyList = [{ areaName: user.countyName, areaId: user.countyId }]
              this.selectAreaList.areaList = [{ areaName: user.gridName, areaId: user.gridId }]
            } else if (user.roleType === 5) {
              // 暂时没有roleType是5的用户
              this.currentArea.areaLevel = '5'
            }
          }
          this.selectForm.cityCode = user.cityId
          this.selectForm.countyCode = user.countyId
          this.selectForm.areaCode = user.gridId
        }
      }
      this.setArea(this.currentArea)
      this.setForm(this.selectForm)
      this.setSelectAreaList(this.selectAreaList)
      this.getMapInfo({ ...this.currentArea })
    },
    /**
     * @description 地图类型切换
     * @param newType
     * <AUTHOR>
     * @date 2025-08-09
     */
    onMapTypeChange(newType) {
      console.log('地图类型已更改为:', newType)
      if (newType === 'BMAP_HYBRID_MAP') {
        this.mapTypes = BMAP_HYBRID_MAP
        this.map.setMapType(BMAP_HYBRID_MAP)
      } else {
        this.mapTypes = BMAP_NORMAL_MAP
        this.map.setMapType(BMAP_NORMAL_MAP)
      }
    },
    backProvince() {
      this.selectForm.cityCode = ''
      this.selectForm.countyCode = ''
      this.selectForm.areaCode = ''
      this.selectForm.villageCode = ''
      this.selectForm.cellId = ''
      if (this.showSecond) {
        this.setArea({ areaId: '999', areaLevel: '1' })
        this.setForm(this.selectForm)
        this.setSelectAreaList(this.selectAreaList)
        this.setUpdateKey()
      } else {
        this.getMapInfo({ areaId: '999', areaLevel: '1' })
      }
    },
    cityChange(areaId) {
      console.log(areaId)
      this.selectForm.countyCode = ''
      this.selectForm.areaCode = ''
      this.selectForm.villageCode = ''
      this.selectForm.cellId = ''
      const areaName = this.selectAreaList.cityList.find(item => item.areaId === areaId).areaName
      const areaLevel = areaName === '全部' ? '1' : '2'
      if (this.showSecond) {
        this.setArea({ areaId, areaLevel })
        this.setForm(this.selectForm)
        this.setSelectAreaList(this.selectAreaList)
        this.setUpdateKey()
      } else {
        this.getMapInfo({ areaId, areaLevel })
      }
    },
    countyChange(areaId) {
      console.log(areaId)
      this.selectForm.areaCode = ''
      this.selectForm.villageCode = ''
      this.selectForm.cellId = ''
      const areaName = this.selectAreaList.countyList.find(item => item.areaId === areaId).areaName
      const areaLevel = areaName === '全部' ? '2' : '3'
      if (this.showSecond) {
        this.setArea({ areaId, areaLevel })
        this.setForm(this.selectForm)
        this.setSelectAreaList(this.selectAreaList)
        this.setUpdateKey()
      } else {
        this.getMapInfo({ areaId, areaLevel })
      }
    },
    areaChange(areaId) {
      console.log(areaId)
      this.selectForm.villageCode = ''
      this.selectForm.cellId = ''
      const areaName = this.selectAreaList.areaList.find(item => item.areaId === areaId).areaName
      const areaLevel = areaName === '全部' ? '3' : '4'
      if (this.showSecond) {
        this.setArea({ areaId, areaLevel })
        this.setForm(this.selectForm)
        this.setSelectAreaList(this.selectAreaList)
        this.setUpdateKey()
      } else {
        this.getMapInfo({ areaId, areaLevel })
      }
    },

    villageChange(areaId) {
      console.log(areaId)
      this.selectForm.cellId = ''
      const areaName = this.selectAreaList.villageList.find(item => item.areaId === areaId).areaName
      const areaLevel = areaName === '全部' ? '4' : '5'
      if (this.showSecond) {
        this.setArea({ areaId, areaLevel })
        this.setForm(this.selectForm)
        this.setSelectAreaList(this.selectAreaList)
        this.setUpdateKey()
      } else {
        this.getMapInfo({ areaId, areaLevel })
      }
    },
    cellChange(areaId) {
      console.log(areaId)
      const areaName = this.selectAreaList.cellList.find(item => item.areaId === areaId).areaName
      const areaLevel = areaName === '全部' ? '5' : '6'
      if (this.showSecond) {
        this.setArea({ areaId, areaLevel })
        this.setForm(this.selectForm)
        this.setSelectAreaList(this.selectAreaList)
        this.setUpdateKey()
      } else {
        this.getMapInfo({ areaId, areaLevel })
      }
    },
    /**
     * @description 更新地图重新加载
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    updateMapInfo() {
      this.getMapInfo({ ...this.currentArea })
    },
    /**
     * @description 省、地市展示Echarts地图，其余层级展示百度地图
     * @param currentArea 当前选中的区域
     * <AUTHOR>
     * @date 2025-08-09
     */
    async getMapInfo(value, centerAndZoom) {
      console.log(value)
      this.centerAndZoom = {}
      if (centerAndZoom && (centerAndZoom.center || centerAndZoom.zoom)) {
        this.centerAndZoom = centerAndZoom
      }
      const { areaId, areaLevel } = value
      this.currentArea = value
      this.setArea(this.currentArea)
      this.setForm(this.selectForm)
      this.setSelectAreaList(this.selectAreaList)
      this.loading = true
      const param = {
        areaId,
        areaLevel,
        areaType: this.areaType,
        date: this.selectForm.statMon,
        // wirelessMap代表无线覆盖，其他都是有线覆盖
        dataType: this.keys !== 'wirelessMap' ? 1 : this.selectForm.gridType === 'OTT' ? 2 : 3
      }
      const params = { ...this.selectForm, dataType: this.opticalDataType }
      if (value.areaLevel === '5') {
        params.villageCode = value.areaId
        params.cellId = ''
      } else if (value.areaLevel === '6') {
        params.cellId = value.areaId
      }
      console.log(params)
      const request = this.currentArea.areaLevel > 4 && this.category === 2 ? getFqxData(params) : getMapAreaInfo(param)
      await request.then(async({ code, data }) => {
        if (code === 200) {
          // 将边界数据转为echarts的json格式
          this.mapBorderJson = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {
                  name: data.areaName
                },
                geometry: {
                  type: 'MultiPolygon',
                  coordinates: this.polygonPath(data.boundary)
                }
              }
            ]
          }
          this.mapJson = {
            type: 'FeatureCollection',
            features: this.polygonFullPath(data.children || [])
          }
          this.geoCoordMap = this.getGeoCoordMap(data.children || [])
          const echartValue = this.keys === 'wirelessMap' ? 'advantageRate' : 'ydWifiRate'
          this.mapData = (data.children || []).map(item => {
            const rawValue = item.data?.[echartValue]
            return {
              name: item.areaName,
              value: rawValue != null ? formatNumberWithUnit(rawValue, true).value : 0,
              ...item
            }
          })
          const obj = { areaId: data.areaId, areaName: '全部' }
          if (areaLevel === '5') {
            data.cellChildren.unshift(obj)
          } else if (areaLevel === '6') {
            console.log(data)
          } else {
            data.children.unshift(obj)
          }
          console.log(data)
          // 地图下钻的时候回填选中区域
          this.selectForm[
            this.currentArea.areaLevel === '2' ? 'cityCode'
              : this.currentArea.areaLevel === '3' ? 'countyCode'
                : this.currentArea.areaLevel === '4' ? 'areaCode'
                  : this.currentArea.areaLevel === '5' ? 'villageCode'
                    : this.currentArea.areaLevel === '6' ? 'cellId'
                      : 'provinceCode'
          ] = data.areaId
          // 获取下级区域信息
          this.getAreaListInfo(data)
          this.$nextTick(() => {
            if (areaLevel < 3) {
              this.initMapEcharts(data)
            } else {
              this.initBaiduMap(data)
            }
          })
        }
      }).catch(e => {
        console.log(e)
        this.loading = false
      })
      this.loading = false
    },
    /**
     * @description 获取下级区域信息
     * @param currentArea 当前选中的区域
     * <AUTHOR>
     * @date 2025-08-09
     */
    getAreaListInfo(data) {
      const { areaLevel, children, cellChildren } = data
      if (areaLevel === '1') {
        this.selectAreaList.cityList = children || []
        this.selectAreaList.countyList = []
        this.selectAreaList.areaList = []
        this.selectAreaList.villageList = []
        this.selectAreaList.cellList = []
      } else if (areaLevel === '2') {
        this.selectAreaList.countyList = children || []
        this.selectAreaList.areaList = []
        this.selectAreaList.villageList = []
        this.selectAreaList.cellList = []
      } else if (areaLevel === '3') {
        this.selectAreaList.areaList = children || []
        this.selectAreaList.villageList = []
        this.selectAreaList.cellList = []
      } else if (areaLevel === '4') {
        this.selectAreaList.villageList = children || []
        this.selectAreaList.cellList = []
      } else if (areaLevel === '5') {
        this.selectAreaList.cellList = cellChildren || []
      }
    },

    /**
     * @description 格式化白色边框边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonPath(data) {
      const polygons = data.split('|')
      const result = polygons.map(polyStr => {
        const points = polyStr.split(';')
        const polygon = points.map(item => {
          const [lon, lat] = item.split(',')
          return [parseFloat(lon.trim()), parseFloat(lat.trim())]
        })
        return polygon
      })
      return [result]
    },

    /**
     * @description 格式化边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonFullPath(data) {
      return data.map(item => {
        return {
          type: 'Feature',
          properties: {
            name: item.areaName
          },
          geometry: {
            type: 'MultiPolygon',
            coordinates: this.polygonPath(item.boundary)
          }
        }
      })
    },
    /**
     * @description 获取悬浮中心点
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    getGeoCoordMap(arr) {
      const result = {}
      arr.forEach(item => {
        const [longitude, latitude] = item.centralPoint.split(',').map(parseFloat)
        result[`${item.areaName}`] = [longitude, latitude]
      })
      return result
    },
    /**
     * @description 初始化Echarts地图
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    initMapEcharts() {
      const datas = [...this.mapData]
      if (this.mapChart) {
        this.mapChart.dispose()
      }
      this.mapChart = echarts.init(document.getElementById(`${this.keys}echart-map-wrap`))
      echarts.registerMap('map', this.mapJson)
      echarts.registerMap('mapBorder', this.mapBorderJson)
      this.mapChart.off('click')
      const option = this.getOption(datas)
      this.mapChart.setOption(option)
      this.mapChart.on('click', (params) => {
        console.log(params.data)
        // 如果是对比状态，点击展示弹窗，否则就是下钻
        if (this.showSecond) {
          this.$emit('messageWindow', params.data, this.keys)
        } else {
          if (params.data.value <= 0) return
          this.currentArea = params.data
          this.getMapInfo(params.data)
        }
      })
    },
    /**
     * @description Echats地图配置项
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    getOption(datas) {
      var that = this
      function scatterData2() {
        return datas.map((item) => ({
          name: item.name,
          areaId: item.areaId,
          value: that.geoCoordMap[item.name]
        }))
      }
      return {
        geo: [
          // 第一层地图
          {
            map: 'map',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              borderColor: '#D8E6FF4D',
              borderWidth: 1,
              shadowColor: 'rgba(0, 0, 0, 0.25)'
            },
            emphasis: {
              itemStyle: {
                areaColor: 'transparent'
              },
              label: {
                show: 0,
                color: '#fff'
              }
            },
            zlevel: 3
          },
          // // 第二层地图和第一层重叠用作边框
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#F7F8FF',
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.25)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: fitChartSize(3.72)
            },
            zlevel: 2,
            silent: true
          }
          // // 第三层地图底层
          // {
          //   map: 'mapBorder',
          //   aspectScale: 1,
          //   layoutSize: '96%',
          //   layoutCenter: ['50%', '52%'],
          //   itemStyle: {
          //     areaColor: 'transparent',
          //     borderColor: '#B6C1FB99',
          //     borderWidth: 1
          //   },
          //   zlevel: 1,
          //   silent: true
          // }
        ],
        visualMap: {
          show: false,
          type: 'piecewise',
          bottom: fitChartSize(40),
          right: fitChartSize(40),
          itemGap: fitChartSize(10),
          align: 'left',
          itemWidth: fitChartSize(16),
          itemHeight: fitChartSize(12),
          textStyle: {
            fontSize: fitChartSize(14),
            color: '#EDEDED'
          },
          seriesIndex: 0,
          pieces: [
            {
              gte: 75,
              lte: 100,
              color: '#A9FFDA80'
            },
            {
              gte: 50,
              lt: 75,
              color: '#37D89280'
            },
            {
              gte: 25,
              lt: 50,
              color: '#FF838380'
            },
            {
              gte: 0,
              lt: 25,
              color: '#FF393980'
            }
          ]
        },
        series: [
          {
            type: 'map',
            selectedMode: false,
            map: 'map',
            geoIndex: 0,
            data: datas
          },
          // 地市弹框撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: '{b}',
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#fff',
              fontSize: fitChartSize(14),
              fontWeight: '500',
              fontFamily: '',
              show: true
            },
            symbol: 'rect',
            // symbolSize: function(data, params) {
            //   const name = params.data.name
            //   const nameLength = name.length || 0
            //   const width = fitChartSize(nameLength * 20)
            //   const height = fitChartSize(30)
            //   return [width, height]
            // },
            symbolOffset: [0, fitChartSize(-30)],
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              borderWidth: fitChartSize(0.5),
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
          // 地市对应值撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: function(name) {
                const item = datas.find(item => item.name === name.name)
                return item ? `${item.value}%` : ''
              },
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#ffffff',
              fontSize: fitChartSize(14),
              fontFamily: 'Milibus',
              show: true
            },
            symbol: 'rect',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          }
        ]
      }
    },
    handleResize() {
      if (this.mapChart) {
        this.mapChart.resize()
        this.$nextTick(() => {
          const updatedOption = this.getOption(this.mapData)
          this.mapChart.setOption(updatedOption)
        })
      }
    },
    /**
     * @description 初始化百度地图
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    initBaiduMap(data) {
      console.log(data)
      const [lng, lat] = data.centralPoint.split(',')
      this.map = new BMap.Map(`${this.keys}baidu-map-wrap`, { enableMapClick: false })
      const center = new BMap.Point(lng, lat)
      // 当是行政村或者小区的时候，不使用特殊样式的地图，且一开始默认是卫星图
      if (this.centerAndZoom && (this.centerAndZoom.center || this.centerAndZoom.zoom) && this.currentArea.areaLevel > 4) {
        this.mapTypes = this.centerAndZoom.mapTypes
        this.map.setMapType(this.mapTypes)
      } else {
        if (this.currentArea.areaLevel >= '5') {
          this.mapTypes = BMAP_HYBRID_MAP
          this.map.setMapType(this.mapTypes)
        } else {
          this.map.setMapStyle(purpleStyle)
        }
      }
      this.map.centerAndZoom(center, this.zoom)
      this.map.enableScrollWheelZoom(true)
      this.$emit('mapReady', this.map)
      this.getBoundary(data)
    },
    /**
     * @description 获取边界数据并绘制Label和Polygon
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    getBoundary(data) {
      console.log(data)
      if (this.map != null) {
        this.map.clearOverlays()
      }
      console.log(this.currentArea)
      this.polygonPaths = []
      this.PolygonCenter = []
      this.labelName = []
      this.subLabelName = []
      const [lng, lat] = data.centralPoint.split(',')
      this.center = { lng, lat }
      // 区县和网格使用百度地图原生方法绘制多边形和标签
      console.log(data && data.children && this.currentArea.areaLevel < 5)
      if (data && data.children && this.currentArea.areaLevel < 5) {
        data.children.filter(item => item.areaName !== '全部').forEach(item => {
          if (item && item.boundary) {
            const boundaryParts = item.boundary.split('|')
            boundaryParts.forEach(boundaryStr => {
              const tr = boundaryStr.split(';').map(val => {
                const [lng, lat] = val.split(',')
                return {
                  lng: (lng || '').trim(),
                  lat: (lat || '').trim()
                }
              })
              this.polygonPaths.push({ tr, item })
            })
            this.labelName.push(item.areaName)
            const value = formatNumberWithUnit(item.data?.advantageRate, true).value || 0
            this.subLabelName.push(value)
            if (item.centralPoint) {
              const [lng, lat] = item.centralPoint.split(',')
              this.PolygonCenter.push({ lng, lat })
            }
          }
        })
        this.drawLabels()
        this.drawPolygons()
      } else if (this.currentArea.areaLevel > 4) {
        if (this.category === 1) {
          this.drawAreaPolygons(data)
        } else {
          this.drawPoints(data)
        }
      }
      if (this.centerAndZoom && (this.centerAndZoom.center || this.centerAndZoom.zoom)) {
        this.map.centerAndZoom(this.centerAndZoom.center, this.centerAndZoom.zoom)
      } else {
        const center = new BMap.Point(this.center.lng, this.center.lat)
        this.map.centerAndZoom(center, this.zoom)
      }
      this.loading = false
    },

    /**
     * @description 绘制标签
     * @param
     * <AUTHOR>
     * @date 2025-07-30
     */
    drawLabels() {
      this.clearLabels()
      this.PolygonCenter.forEach((position, index) => {
        const html = `
        <div style="text-align: center; line-height: 1.2;">
            <div>${this.labelName[index]}</div>
            ${this.keys === 'wirelessMap' && (this.currentArea.areaLevel === '3' || this.currentArea.areaLevel === '4') ? `<div>${this.subLabelName[index]}%</div>` : ''}
        </div>`
        const label = new BMap.Label(html, { position })
        label.setStyle({
          fontSize: '13px',
          fontWeight: '400',
          fontFamily: 'PingFang SC',
          border: 'none',
          background: 'none',
          color: '#ffffff',
          pointerEvents: 'none'
        })
        this.map.addOverlay(label)
        this.labelOverlays.push(label)
      })
    },
    /**
     * @description 清除标签图层
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    clearLabels() {
      this.labelOverlays.forEach((label) => {
        this.map.removeOverlay(label)
      })
      this.labelOverlays = []
    },
    /**
     * @description 绘制多边形
     * @param
     * <AUTHOR>
     * @date 2025-07-30
     */
    drawPolygons() {
      this.clearPolygons()
      this.polygonPaths.forEach(({ item, tr }) => {
        let color = 'rgba(213, 152, 54)'
        if (item) {
          const points = tr.map(coord => new BMap.Point(coord.lng, coord.lat))
          const { ydWifiRate, situation, advantageRate } = item.data || {}
          const value = formatNumberWithUnit(ydWifiRate, true).value || 0
          const advantageRateValue = formatNumberWithUnit(advantageRate, true).value || 0
          if (this.keys === 'wirelessMap') {
            if (this.currentArea.areaLevel < 5) {
              if (advantageRateValue !== undefined && advantageRateValue !== null) {
                if (advantageRateValue < 25) {
                  color = 'rgba(255, 57, 57)'
                } else if (advantageRateValue >= 25 && advantageRateValue < 50) {
                  color = 'rgba(255, 131, 131)'
                } else if (advantageRateValue >= 50 && advantageRateValue < 75) {
                  color = 'rgba(55, 216, 146)'
                } else if (advantageRateValue >= 75 && advantageRateValue <= 100) {
                  color = 'rgba(169, 255, 218)'
                }
              }
            } else {
              if (situation === '0') {
                color = 'rgba(255, 224, 22, 0.5)'
              } else {
                color = 'rgba(53, 255, 76, 0.5)'
              }
            }
          } else {
            if (this.currentArea.areaLevel < 5) {
              if (value !== undefined && value !== null) {
                if (value < 25) {
                  color = 'rgba(255, 57, 57)'
                } else if (value >= 25 && value < 50) {
                  color = 'rgba(255, 131, 131)'
                } else if (value >= 50 && value < 75) {
                  color = 'rgba(55, 216, 146)'
                } else if (value >= 75 && value <= 100) {
                  color = 'rgba(169, 255, 218)'
                }
              }
            } else {
              if (value !== undefined && value !== null) {
                if (value >= 0 && value <= 20) {
                  color = 'rgba(255, 22, 24)'
                } else if (value > 20 && value <= 40) {
                  color = 'rgba(255, 224, 22)'
                } else if (value > 40 && value <= 60) {
                  color = 'rgba(213, 44, 255)'
                } else if (value > 60 && value <= 80) {
                  color = 'rgba(44, 163, 255)'
                } else if (value > 80 && value <= 100) {
                  color = 'rgba(53, 255, 76)'
                }
              }
            }
          }
          // 行政村、小区的（areaLevel=5）用单独的颜色
          const polygon = new BMap.Polygon(points, {
            fillColor: this.currentArea.areaLevel === '5' || this.currentArea.areaLevel === '6' ? 'rgba(255, 255, 255)' : color,
            fillOpacity: this.currentArea.areaLevel < 4 ? 0.5 : this.currentArea.areaLevel === '5' || this.currentArea.areaLevel === '6' ? 0.05 : 0.6,
            strokeOpacity: this.currentArea.areaLevel === '5' || this.currentArea.areaLevel === '6' ? 2 : 1,
            strokeWeight: this.currentArea.areaLevel === '5' ? 4 : 1,
            strokeColor: this.currentArea.areaLevel === '5' || this.currentArea.areaLevel === '6' ? 'rgb(255, 0, 221)' : color,
            strokeStyle: 'none'
          })
          polygon.addEventListener('click', () => {
            console.log(item)
            if (this.showSecond) {
              this.$emit('messageWindow', item, this.keys)
            } else {
              this.getMapInfo(item)
            }
          })
          this.map.addOverlay(polygon)
          this.polygonOverlays.push(polygon)
        }
      })
    },
    /**
     * @description 清除多边形图层
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    clearPolygons() {
      this.polygonOverlays.forEach(polygon => {
        this.map.removeOverlay(polygon)
      })
      this.polygonOverlays = []
    },
    /**
     * @description 行政村、小区绘制图层
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    drawAreaPolygons(data) {
      console.log(data)
      var that = this
      // 绘制行政村边界
      if (data && data.boundary) {
        this.polygonPaths = []
        this.PolygonCenter = []
        this.labelName = []
        const boundaryParts = data.boundary.split('|')
        boundaryParts.forEach(boundaryStr => {
          const tr = boundaryStr.split(';').map(val => {
            const [lng, lat] = val.split(',')
            return {
              lng: (lng || '').trim(),
              lat: (lat || '').trim()
            }
          })
          this.polygonPaths.push({ tr, item: data })
        })
        this.labelName.push(data.areaName)
        if (data.centralPoint) {
          const [lng, lat] = data.centralPoint.split(',')
          this.PolygonCenter.push({ lng, lat })
        }
        this.drawPolygons()
      }
      // 因为栅格数据太多，所以采用mapv方法绘制canvas图层不会导致卡顿
      if (data && data.children) {
        const polygons = []
        data.children.filter(item => item.areaName !== '全部').forEach(item => {
          if (item && item.boundary && item.data) {
            const tr = item.boundary.split(';').map(str => {
              const [lng, lat] = str.split(',')
              return [parseFloat(lng), parseFloat(lat)]
            })
            const { situation, ydWifiRate } = item.data
            const value = formatNumberWithUnit(ydWifiRate, true).value || 0
            let color = 'rgba(213, 152, 54)'
            // 无线的行政村、小区图例按照situation来判断，0持平，1优势
            if (this.keys === 'wirelessMap') {
              if (situation === '0') {
                color = 'rgba(255, 224, 22, 0.5)'
              } else {
                color = 'rgba(53, 255, 76, 0.5)'
              }
            } else {
              // 有线的行政村、小区图例按照value来判断
              if (value >= 0 && value <= 20) {
                color = 'rgba(255, 22, 24,0.5)'
              } else if (value > 20 && value <= 40) {
                color = 'rgba(255, 224, 22,0.5)'
              } else if (value > 40 && value <= 60) {
                color = 'rgba(213, 44, 255,0.5)'
              } else if (value > 60 && value <= 80) {
                color = 'rgba(44, 163, 255,0.5)'
              } else if (value > 80 && value <= 100) {
                color = 'rgba(53, 255, 76,0.5)'
              }
            }
            polygons.push({
              geometry: {
                type: 'Polygon',
                coordinates: [tr]
              },
              fillStyle: color,
              obj: item.data,
              centralPoint: item.centralPoint
            })
          }
        })
        console.log(polygons)
        const options = {
          draw: 'simple',
          strokeStyle: 'rgba(255, 255, 255, 1)',
          lineWidth: 1,
          zIndex: 200,
          methods: {
            click: function(e) {
              console.log(e, 1, that.bubble)
              if (e && e.obj) {
                that.bubble = false
                const [lng, lat] = e.centralPoint.split(',')
                that.pointCenter = { lng, lat }
                that.addPoint()
                // // 将标注添加到地图
                that.$emit('windowInfo', e, that.keys)
              }
            },
            mousemove: function(e) {
              that.map.setDefaultCursor(e ? 'pointer' : 'default')
            }
          }
        }
        const dataSet = new mapv.DataSet(polygons)
        this.mapvLayer = new mapv.baiduMapLayer(this.map, dataSet, options)
      }
      // 行政村层级的时候需要绘制小区边界
      if (this.currentArea.areaLevel === '5') {
        // cellChildren
        if (data && data.cellChildren) {
          // 绘制多边形边界，小区很多，所以采用mapv绘制
          const polygons = []
          const label = []
          data.cellChildren.filter(item => item.areaName !== '全部').forEach(item => {
            if (item && item.boundary) {
              const boundaryParts = item.boundary.split('|')
              boundaryParts.forEach(boundaryStr => {
                const tr = boundaryStr.split(';').map(str => {
                  const [lng, lat] = str.split(',')
                  return [parseFloat(lng), parseFloat(lat)]
                })
                polygons.push({
                  geometry: {
                    type: 'Polygon',
                    coordinates: [tr]
                  },
                  fillStyle: 'rgba(255, 255, 255,0.05)',
                  obj: item
                })
              })
            }
            const [lng, lat] = item.centralPoint.split(',')
            if (item && item.centralPoint) {
              label.push({
                geometry: { type: 'Point', coordinates: [parseFloat(lng), parseFloat(lat)] },
                text: item.areaName
              })
            }
          })
          const options = {
            draw: 'simple',
            strokeStyle: 'rgba(255,0,0)',
            lineWidth: 1,
            zIndex: 99,
            methods: {
              click: function(e) {
                console.log(e, 2, that.bubble)
                if (e && e.obj) {
                  if (!that.bubble) {
                    console.log('111')
                  } else if (that.showSecond) {
                    console.log('222')
                  } else {
                    that.getMapInfo(e.obj)
                  }
                  that.$nextTick(() => {
                    that.bubble = true
                  })
                }
              },
              mousemove: function(e) {
                that.map.setDefaultCursor(e ? 'pointer' : 'default')
              }
            }
          }

          const labelOptions = {
            draw: 'text',
            fillStyle: '#000000',
            textAlign: 'center',
            textBaseline: 'middle',
            avoid: true, // 防遮挡
            size: 12,
            zIndex: 90
          }
          const dataSet = new mapv.DataSet(polygons)
          const labelDataSet = new mapv.DataSet(label)
          this.villageMapvLayer = new mapv.baiduMapLayer(this.map, dataSet, options)
          this.labelMapvLayer = new mapv.baiduMapLayer(this.map, labelDataSet, labelOptions)
        }
      }
    },
    /**
     * @description 添加图标点
     * @param
     * <AUTHOR>
     * @date 2025-08-14
     */
    addPoint() {
      this.markers.forEach(marker => {
        this.map.removeOverlay(marker)
      })
      this.markers = []
      const point = new BMap.Point(this.pointCenter.lng, this.pointCenter.lat)
      const myIcon = new BMap.Icon(
        require('@/assets/images/networkCoverage/address-point.svg'),
        new BMap.Size(35, 35)
      )
      myIcon.setImageSize(new BMap.Size(35, 35))
      myIcon.setAnchor(new BMap.Size(17, 32))
      const marker = new BMap.Marker(point, {
        icon: myIcon
      })
      this.map.addOverlay(marker)
      this.markers.push(marker)
    },
    /**
     * @description 绘制分纤箱点，不知道有多少，所以也先用mapv的覆盖点绘制
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    async drawPoints(data) {
      console.log(data)
      // this.loading = true
      if (this.map != null) {
        this.map.clearOverlays()
      }
      // await getFqxData(params).then(({ data }) => {
      this.drawAreaPolygons(data)
      const res = data.fqxList || []
      const iconMap = {
        'GD光分纤盒': iconG,
        'DX光分纤盒': iconD,
        'LT光分纤盒': iconL,
        'YD光分纤盒': iconY,
        'YD分光器': iconP
      }
      const icons = {}
      const iconTypes = Object.keys(iconMap)
      let loadedCount = 0
      iconTypes.forEach(type => {
        const img = new Image()
        img.src = iconMap[type]
        icons[type] = img
        img.onload = img.onerror = () => {
          loadedCount++
          if (loadedCount === iconTypes.length) {
            const points = res.map(item => {
              const iconType = iconMap[item.yysType + item.deviceType] ? item.yysType + item.deviceType : '1'
              return {
                geometry: {
                  type: 'Point',
                  coordinates: [parseFloat(item.longitude), parseFloat(item.latitude)]
                },
                icon: icons[iconType],
                item
              }
            })
            const dataSet = new mapv.DataSet(points)
            var that = this
            const options = {
              draw: 'icon',
              width: 35,
              height: 46,
              methods: {
                mousemove: function(e) {
                  that.map.setDefaultCursor(e ? 'pointer' : 'default')
                }
              }
            }
            new mapv.baiduMapLayer(this.map, dataSet, options)
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.map-part {
  width: 100%;
  height: 100%;
  position: relative;
  ::v-deep .el-loading-mask {
    top: vh(-86);
  }
  .form-part {
    padding: vh(20) vw(20) vh(10);
    position: absolute;
    top: vh(50);
    left: vw(46);
    background-color: #060f22b2;
    z-index: 99;
    display: inline-block;
    ::v-deep .el-input__inner {
      width: vw(114);
      height: vh(32);
      line-height: vh(32);
      background-color: transparent;
      border-color: #727687;
      border-radius: 4px;
      font-size: vw(14);
      color: #b4b9bf !important;
    }
    ::v-deep .el-form-item__label {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(16);
      color: #ffffff;
      padding-right: vw(10);
    }
    ::v-deep .el-form-item {
      margin-right: wv(10);
      margin-bottom: vh(10);
    }
    .dateClass {
      .el-date-editor.el-input {
        width: vw(140);
      }
      ::v-deep .el-input__inner {
        width: vw(140) !important;
      }
    }
    .long-inputer {
      ::v-deep .el-input__inner {
        width: vw(180) !important;
      }
    }
  }
  .echartsMapWrap {
    position: absolute;
    top: vw(120);
    left: 50%;
    transform: translateX(-50%);
    width: vw(775);
    height: vh(731);
  }
  .map-wrap {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="scss">
@import "~@/views/operation/index.scss";
.custom-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #141e32 !important;
    }
  }
  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    height: vh(34);
    line-height: vh(34);
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}
.datePopperClass {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  .el-date-picker__header-label {
    color: #ffffff !important;
  }
  .el-month-table td .cell {
    color: #ffffff !important;
  }
  .el-month-table td.current:not(.disabled) .cell {
    color: #409eff !important;
  }
  .el-date-picker__header--bordered {
    border-color: #677aa5;
  }
}
</style>
