<template>
  <div class="map-type" @mouseenter="hover = true" @mouseleave="hover = false">
    <div :class="{ active: activeType === 'BMAP_NORMAL_MAP' }" class="map-type-card vector-type" @click="setType('BMAP_NORMAL_MAP')">
      <span>地图</span>
    </div>
    <div :class="{ active: activeType === 'BMAP_HYBRID_MAP' }" class="map-type-card img-type" @click="setType('BMAP_HYBRID_MAP')">
      <span>卫星</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapTypeToggle',
  data() {
    return {
      activeType: 'BMAP_NORMAL_MAP', // 默认是普通地图
      hover: false
    }
  },
  methods: {
    setType(type) {
      this.activeType = type
      this.$emit('change', type) // 抛出事件，供父组件监听地图类型变化
    }
  }
}
</script>

<style lang="scss" scoped>
.map-type {
  width: 153px;
  height: 104px;
  position: absolute;
  bottom: 56px;
  right: 40px;
  z-index: 2;
  cursor: pointer;
  border-radius: 8px;
  transition: width 0.8s, height 0.8s;
  background: #414976;

  &:hover {
    width: 292px;
    height: 104px;
  }

  .map-type-card {
    position: absolute;
    top: 12px;
    width: 128px;
    height: 80px;
    border-radius: 8px;
    border: solid 1px black;

    span {
      width: 36px;
      height: 16px;
      position: absolute;
      bottom: 1px;
      right: 1px;
      display: inline-block;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      color: #fff;
      opacity: 0.8;
    }
  }

  .vector-type {
    background: url("~@/assets/images/networkCoverage/map-background.png")
      no-repeat;
    background-size: 100% 100%;
    right: 12px;
    z-index: 3;
    transition: right 0.8s;
  }

  .img-type {
    background: url("~@/assets/images/networkCoverage/satellite-background.png")
      no-repeat;
    background-size: 100% 100%;
    right: 12px;
    z-index: 2;
    transition: right 0.8s;
  }

  &:hover {
    .vector-type {
      right: 154px;
    }

    .img-type {
      right: 12px;
    }
  }

  .vector-type:hover,
  .img-type:hover,
  .active {
    border: solid 1px #55ADFF;
  }
}
</style>
