<template>
  <ScreenAdapter :h="screenHeight" :w="screenWidth">
    <HeaderPart :menu-active="menuActive" :menu="menuList" @navclick="navClick" />
    <div class="body-content">
      <router-view :key="key" />
    </div>
    <BottomPart />
  </ScreenAdapter>
</template>
<script>
import BottomPart from './components/BottomPart.vue'
import HeaderPart from './components/HeaderPart'
import ScreenAdapter from './components/ScreenAdapter.vue'

export default {
  name: 'NetworkCoverage',
  components: {
    ScreenAdapter,
    HeaderPart,
    BottomPart
  },
  data() {
    return {
      screenWidth: 1920, // 默认值
      screenHeight: 1080, // 默认值
      menuActive: '',
      menuList: [
        { name: 'wired', menuName: '宽带WIFI', path: '/networkCoverage/wired' },
        { name: 'wireless', menuName: '无线', path: '/networkCoverage/wireless' }
      ]
    }
  },
  computed: {
    key() {
      return this.$route.path
    }
  },
  mounted() {
    const { name } = this.$route
    if (name === 'networkCoverageWired') {
      this.menuActive = 'wired'
    } else if (name === 'networkCoverageWireless') {
      this.menuActive = 'wireless'
    }
    this.updateScreenSize()
    this.onresize = this.debounce(() => this.updateScreenSize(), 100)
    window.addEventListener('resize', this.onresize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onresize)
  },
  methods: {
    debounce(fn, delay = 500) {
      let timer
      return function(...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          timer = null
          fn.apply(this, args)
        }, delay)
      }
    },
    updateScreenSize() {
      this.screenWidth = window.innerWidth
      this.screenHeight = window.innerHeight
    },
    navClick(menuItem) {
      this.menuActive = menuItem.name
      this.$router.push({ path: menuItem.path })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.body-content {
  position: relative;
  top: vh(-30);
  width: 100%;
  height: 100%;
  margin-bottom: vh(-30);
  flex: 1;
  display: flex;
  z-index: 99;
}
</style>
