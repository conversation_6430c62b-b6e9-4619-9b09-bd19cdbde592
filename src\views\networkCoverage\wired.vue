<template>
  <div class="wired">
    <div class="wired-content">
      <div>
        <MapPart
          ref="firstMap"
          :area-type="areaType"
          :category="category"
          :optical-data-type="opticalDataType"
          :show-second="showSecond"
          keys="firstMap"
          @mapReady="onFirstMapReady"
          @messageWindow="messageWindow"
          @windowInfo="windowInfo"
        />
      </div>
      <div v-if="showSecond">
        <MapPart
          ref="secondMap"
          :area-type="areaType"
          :category="category"
          :optical-data-type="opticalDataType"
          :show-second="showSecond"
          keys="secondMap"
          @mapReady="onSecondMapReady"
          @messageWindow="messageWindow"
          @windowInfo="windowInfo"
        />
      </div>
    </div>
    <div v-drag class="right-part">
      <div class="task-distributed" @click="jumpover">
        <span>派发任务></span>
      </div>
      <template v-if="commonCurrentArea.areaLevel>4">
        <div class="checkbox-group">
          <el-checkbox-group v-model="category" :max="1" @change="changeCategory">
            <el-checkbox :true-label="1" false-label="null">宽带占比</el-checkbox>
            <el-checkbox :true-label="2" false-label="null">分纤箱分布</el-checkbox>
          </el-checkbox-group>
        </div>
        <!-- 小区归属到行政村下，取消互斥选择 -->
        <!-- <div v-if="category===1" class="checkbox-group">
          <el-checkbox-group v-model="areaType" @change="changeAreaType">
            <el-checkbox :true-label="1" false-label="null">行政村</el-checkbox>
            <el-checkbox style="margin-bottom:0" :true-label="2" :false-label="1">资管小区</el-checkbox>
          </el-checkbox-group>
        </div>-->
        <div v-if="category===2" class="checkbox-group">
          <el-checkbox-group v-model="opticalType" :min="1" @change="changeOpticalType">
            <el-checkbox :label="2">竞对分纤箱</el-checkbox>
            <el-checkbox :label="1">移动分纤箱光交箱</el-checkbox>
          </el-checkbox-group>
        </div>
      </template>
      <div class="task-distributed" @click="compare">
        <span v-if="!showSecond">对比</span>
        <span v-else>返回</span>
      </div>
      <template v-if="commonCurrentArea.areaLevel>user.roleType">
        <div class="task-distributed" @click="backMap">
          <span>返回上级地图</span>
        </div>
      </template>
    </div>
    <el-dialog :visible.sync="dialogVisible" append-to-body class="info-dialog">
      <div class="info-dialog-content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="汇总" name="first">
            <el-table
              :data="collectData"
              cell-class-name="cellClassName"
              class="info-dialog-table"
              header-cell-class-name="headerCellClassName"
              style="width: 100%"
            >
              <el-table-column label="月份" prop="statMon" />
              <el-table-column label="地市" prop="cityName" />
              <el-table-column label="栅格ID" prop="gridId" />
              <el-table-column label="WiFi量" prop="wifiAllCnt" />
              <el-table-column label="移动WiFi量" prop="ydWifiCnt" />
              <el-table-column label="竞对WiFi量" prop="jdWifiCnt" />
              <el-table-column :formatter="formatPercentage" label="移动WiFi占比" prop="ydWifiRate" />
              <el-table-column :formatter="formatPercentage" label="竞对WiFi占比" prop="jdWifiRate" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="竞对明细" name="third">
            <el-table
              v-loading="loading"
              :data="tableData"
              cell-class-name="cellClassName"
              class="info-dialog-table"
              header-cell-class-name="headerCellClassName"
              style="width: 100%"
            >
              <el-table-column label="月份" prop="statMon" />
              <el-table-column label="WiFi类型" prop="wifiType" />
              <el-table-column label="WiFi名称" prop="wifiName" />
              <el-table-column label="WiFi Mac" prop="wifiMac" />
              <el-table-column label="删格ID" prop="gridId" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="移动明细" name="second">
            <el-table
              v-loading="loading"
              :data="tableData"
              cell-class-name="cellClassName"
              class="info-dialog-table"
              header-cell-class-name="headerCellClassName"
              style="width: 100%"
            >
              <el-table-column label="月份" prop="statMon" />
              <el-table-column label="WiFi类型" prop="wifiType" />
              <el-table-column label="WiFi名称" prop="wifiName" />
              <el-table-column label="WiFi Mac" prop="wifiMac" />
              <el-table-column label="删格ID" prop="gridId" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogMsgVisible" append-to-body class="message-dialog">
      <div class="info-dialog-content">
        <div class="msg-dialog-title">信息</div>
        <div class="info-dialog-box">
          <!-- 月份 -->
          <div class="info-row">
            <div class="info-label">月份</div>
            <div v-for="(month, index) in infoData.months" :key="`month-${index}`" class="info-value">{{ month }}</div>
          </div>
          <!-- 地区名称 -->
          <div class="info-row">
            <div
              class="info-label"
            >{{ commonCurrentArea.areaLevel==='1'?'地市':commonCurrentArea.areaLevel==='2'?'区县':commonCurrentArea.areaLevel==='3'?'网格':commonCurrentArea.areaLevel=='4'?'行政村':commonCurrentArea.areaLevel=='5'?'小区':'' }}：</div>
            <div v-for="(name, index) in infoData.name" :key="`name-${index}`" class="info-value">{{ name }}</div>
          </div>
          <!-- 家宽总量 -->
          <div class="info-row">
            <div class="info-label">家宽总量：</div>
            <div v-for="(cnt, index) in infoData.wifiAllCnt" :key="`wifiAllCnt-${index}`" class="info-value">{{ cnt }}</div>
          </div>
          <!-- 竞对家宽总量 -->
          <div class="info-row">
            <div class="info-label">竞对家宽总量：</div>
            <div v-for="(cnt, index) in infoData.jdWifiCnt" :key="`jdWifiCnt-${index}`" class="info-value">{{ cnt }}</div>
          </div>
          <!-- 移动家宽总量 -->
          <div class="info-row">
            <div class="info-label">移动家宽总量：</div>
            <div v-for="(cnt, index) in infoData.ydWifiCnt" :key="`ydWifiCnt-${index}`" class="info-value">{{ cnt }}</div>
          </div>
          <!-- 移动家宽占比 -->
          <div class="info-row">
            <div class="info-label">移动家宽占比：</div>
            <div v-for="(rate, index) in infoData.ydWifiRate" :key="`ydWifiRate-${index}`" class="info-value">{{ formatRate(rate) }}</div>
          </div>
          <!-- 竞对家宽占比 -->
          <div class="info-row">
            <div class="info-label">竞对家宽占比：</div>
            <div v-for="(rate, index) in infoData.jdWifiRate" :key="`jdWifiRate-${index}`" class="info-value">{{ formatRate(rate) }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MapPart from './components/MapPart.vue'
import { getKdDetailData } from '@/api/networkCoverage'
import { mapState } from 'vuex'
export default {
  name: 'Wired',
  components: { MapPart },

  data() {
    return {
      loading: false,
      dialogVisible: false,
      user: {},
      category: 1,
      areaType: 1,
      opticalType: [2],
      showSecond: false,
      activeName: 'first',
      tableData: [],
      collectData: [],
      keys: '',
      dialogMsgVisible: false,
      infoData: {},
      map1: null,
      map2: null,
      isSyncing: false
    }
  },
  computed: {
    ...mapState('networkCoverage', ['commonCurrentArea', 'commonSelectForm']),
    opticalDataType() {
      if (this.opticalType.length === 1) {
        return this.opticalType[0]
      }
      if (this.opticalType.includes(1) && this.opticalType.includes(2)) {
        return 3
      }
      return null
    }
  },
  mounted() {
    this.user = JSON.parse(window.sessionStorage.getItem('userInfo')) || {}
  },
  methods: {
    /**
     * @description 格式化百分比
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    formatPercentage(row, column, cellValue) {
      if (cellValue !== null && cellValue !== undefined) {
        return (cellValue * 100).toFixed(2) + '%'
      }
      return ''
    },
    formatRate(value) {
      if (value === '-' || value == null) return '-'
      const num = Number(value)
      return isNaN(num) ? '-' : (num * 100).toFixed(2) + '%'
    },
    /**
     * @description 开始对比
     * @param
     * <AUTHOR>
     * @date 2025-08-09
     */
    compare() {
      this.showSecond = !this.showSecond
    },

    windowInfo(e, keys) {
      console.log(e)
      this.activeName = 'first'
      this.keys = keys
      if (this.showSecond) {
        const obj = this.$refs.firstMap.mapData.find(el => el.areaId === e.obj.gridId)
        const obj2 = this.$refs.secondMap.mapData.find(el => el.areaId === e.obj.gridId)
        if (obj || obj2) {
          this.collectData = [obj.data || {}, obj2.data || {}]
        }
      } else {
        this.collectData = [e.obj]
      }
      this.dialogVisible = true
    },

    // changeAreaType() {
    //   this.$refs.firstMap.getMapInfo({ ...this.commonCurrentArea })
    //   if (this.showSecond) {
    //     this.$refs.secondMap.getMapInfo({ ...this.commonCurrentArea })
    //   }
    // },

    changeCategory() {
      const { map, mapTypes } = this.$refs.firstMap
      const centerAndZoom = { center: map.getCenter(), zoom: map.getZoom(), mapTypes }
      this.$refs.firstMap.getMapInfo({ ...this.commonCurrentArea }, centerAndZoom)
      if (this.showSecond) {
        this.$refs.secondMap.getMapInfo({ ...this.commonCurrentArea }, centerAndZoom)
      }
    },

    changeOpticalType() {
      const { map, mapTypes } = this.$refs.firstMap
      const centerAndZoom = { center: map.getCenter(), zoom: map.getZoom(), mapTypes }
      this.$refs.firstMap.getMapInfo({ ...this.commonCurrentArea }, centerAndZoom)
      if (this.showSecond) {
        this.$refs.secondMap.getMapInfo({ ...this.commonCurrentArea }, centerAndZoom)
      }
    },

    backMap() {
      const area = { ...this.commonCurrentArea }
      area.areaLevel--
      if (area.areaLevel === 1) {
        this.$refs.firstMap.backProvince()
      } else if (area.areaLevel === 2) {
        this.$refs.firstMap.cityChange(this.commonSelectForm.cityCode)
      } else if (area.areaLevel === 3) {
        this.$refs.firstMap.countyChange(this.commonSelectForm.countyCode)
      } else if (area.areaLevel === 4) {
        this.$refs.firstMap.areaChange(this.commonSelectForm.areaCode)
      } else if (area.areaLevel === 5) {
        this.$refs.firstMap.villageChange(this.commonSelectForm.villageCode)
      }
    },

    async handleClick() {
      this.tableData = []
      if (this.activeName === 'second') {
        const params = { gridId: this.collectData[0].gridId, dataType: '1' }
        if (this.keys === 'firstMap') {
          params.statMon = this.$refs.firstMap.selectForm.statMon
        } else if (this.keys === 'secondMap') {
          params.statMon = this.$refs.secondMap.selectForm.statMon
        }
        this.loading = true
        await getKdDetailData(params).then(({ data }) => {
          this.tableData = data || []
        })
        this.loading = false
      } else if (this.activeName === 'third') {
        const params = { gridId: this.collectData[0].gridId, dataType: '2' }
        if (this.keys === 'firstMap') {
          params.statMon = this.$refs.firstMap.selectForm.statMon
        } else if (this.keys === 'secondMap') {
          params.statMon = this.$refs.secondMap.selectForm.statMon
        }
        this.loading = true
        await getKdDetailData(params).then(({ data }) => {
          this.tableData = data || []
        })
        this.loading = false
      }
    },

    messageWindow(e) {
      const { firstMap, secondMap } = this.$refs
      const getData = (map, areaId) => map.mapData.find(item => item.areaId === areaId) || {}
      const obj1 = getData(firstMap, e.areaId) || {}
      const obj2 = getData(secondMap, e.areaId) || {}
      const month1 = firstMap.selectForm.statMon || '-'
      const month2 = secondMap.selectForm.statMon || '-'

      this.infoData = {
        months: [month1, month2],
        name: [obj1?.name || '-', obj2?.name || '-'],
        wifiAllCnt: [obj1?.data?.wifiAllCnt || '-', obj2?.data?.wifiAllCnt || '-'],
        ydWifiCnt: [obj1?.data?.ydWifiCnt || '-', obj2?.data?.ydWifiCnt || '-'],
        jdWifiCnt: [obj1?.data?.jdWifiCnt || '-', obj2?.data?.jdWifiCnt || '-'],
        ydWifiRate: [obj1?.data?.ydWifiRate, obj2?.data?.ydWifiRate],
        jdWifiRate: [obj1?.data?.jdWifiRate, obj2?.data?.jdWifiRate]
      }
      this.dialogMsgVisible = true
    },
    /**
     * @description 同步地图
     * @param
     * <AUTHOR>
     * @date 2025-08-13
     */
    onFirstMapReady(map) {
      this.map1 = map
      this.tryBindEvents()
    },
    onSecondMapReady(map) {
      this.map2 = map
      this.tryBindEvents()
    },
    tryBindEvents() {
      // 两个地图都准备好才绑定
      if (this.map1 && this.map2) {
        this.bindSyncEvents(this.map1, this.map2)
        this.bindSyncEvents(this.map2, this.map1)
      }
    },
    bindSyncEvents(srcMap, targetMap) {
      // 拖拽
      srcMap.addEventListener('moving', () => {
        if (!this.isSyncing) this.isSyncing = true
        const center = srcMap.getCenter()
        targetMap.setCenter(center)
      })
      srcMap.addEventListener('moveend', () => {
        this.isSyncing = false
      })
      // 缩放
      srcMap.addEventListener('zoomstart', () => {
        this.isSyncing = true
      })
      srcMap.addEventListener('zoomend', () => {
        const zoom = srcMap.getZoom()
        const center = srcMap.getCenter()
        targetMap.centerAndZoom(center, zoom)
        this.isSyncing = false
      })
    },

    jumpover() {
      this.$router.push({ path: '/market/taskScheduling', query: { ...this.$refs.firstMap.selectForm, from: 'networkCoverage', level: this.commonCurrentArea.areaLevel }})
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.wired {
  width: 100%;
  position: relative;
  .wired-content {
    display: flex;
    color: #ffffff;
    height: 100%;
    > div {
      flex: 1;
      flex-shrink: 0;
    }
  }

  .right-part {
    position: absolute;
    top: vh(45);
    left: vw(1665);
    z-index: 99;
    .task-distributed {
      width: vw(215);
      height: vh(49);
      line-height: vh(49);
      background: url("~@/assets/images/networkCoverage/distributed.png")
        no-repeat;
      background-size: 100% 100%;
      text-align: center;
      margin-top: vh(16);
      cursor: pointer;
      span {
        background-image: linear-gradient(180deg, #ffffff 50.17%, #94e5ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vw(24);
      }
    }

    .checkbox-group {
      margin-top: vh(16);
      padding: vh(15) 0 vh(15) vw(22);
      width: vw(215);
      background: url("~@/assets/images/networkCoverage/distributed.png")
        no-repeat;
      background-size: 100% 100%;
      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        > label {
          &:first-child {
            margin-bottom: vh(10);
          }
        }
      }

      ::v-deep .el-checkbox {
        display: inline-flex;
        align-items: center;
      }
      ::v-deep .el-checkbox__label {
        color: #e4eeff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
      }
      ::v-deep .el-checkbox__inner {
        border: 2px solid #bed4ff;
        background-color: transparent;
        width: vh(16);
        height: vh(16);
      }
      ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #e4eeff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
      }

      ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
        background-color: #345596;
      }
      ::v-deep .el-checkbox__inner::after {
        border-color: #ffffff;
        top: -2px;
        height: 10px;
      }
      ::v-deep .el-checkbox__inner::before {
        top: 4px;
      }
      ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #253a65;
      }
    }

    .compare-icon {
      margin-top: vh(24);
      text-align: right;
      ::v-deep .el-button {
        padding: vh(12) vw(7);
        color: #e4eeff;
        background: linear-gradient(
              181.96deg,
              #6498ff -38.96%,
              #1d4ba7 39.59%,
              #142d60 98.35%
            )
            padding-box,
          linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%)
            border-box;
        border: 1.5px solid transparent;
        box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: vw(14);
      }

      ::v-deep .el-icon-my-zdy {
        background: url("~@/assets/images/networkCoverage/union.png") center
          no-repeat;
        font-size: 12px;
        background-size: cover;
      }
      ::v-deep .el-icon-my-zdy:before {
        content: "替";
        font-size: vw(14);
        visibility: hidden;
      }

      ::v-deep .el-icon-my-zdy {
        font-size: vw(14);
      }

      ::v-deep .el-icon-my-zdy:before {
        content: "\e611";
      }
    }
  }
}
.info-dialog {
  ::v-deep .el-dialog {
    width: vw(1142);
    background: linear-gradient(180deg, #113963 0%, #2b63a2 100%);
    border-radius: 4px;
    box-shadow: 0px 4px 20px 0px #456180;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(
        90deg,
        #def1fe 31.37%,
        rgba(222, 241, 254, 0) 100%
      );
      z-index: -1;
      border-radius: 4px;
    }
  }

  ::v-deep .el-dialog__header {
    padding: 0;
  }
  ::v-deep .el-dialog__body {
    padding: vh(20) vw(20);
  }
  ::v-deep .el-dialog__headerbtn {
    top: vh(21);
    right: vw(20);
    z-index: 99;
  }
}
.info-dialog-content {
  ::v-deep .el-tabs__nav-wrap::after {
    display: none;
  }
  ::v-deep .el-tabs__item {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: vw(16);
    color: #e4eeff;
    padding: 0 vw(13);
  }
  ::v-deep .el-tabs__item.is-active {
    color: #77f1ff;
  }
  ::v-deep .el-tabs__active-bar {
    background-color: #77f1ff;
  }
  ::v-deep .el-tabs .el-tabs__nav-wrap {
    padding: 0;
  }

  ::v-deep .el-table {
    background-color: transparent !important;
    &::before {
      display: none;
    }
    tr {
      background-color: unset;
    }
    .headerCellClassName {
      background: transparent !important;
      color: #e4eeff !important;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
    }
    .cellClassName {
      color: #e4eeff !important;
      border-color: #405d8b;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
    }
    .el-table__cell {
      padding: vh(5) 0;
    }
  }

  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: transparent !important;
  }

  ::v-deep .el-table__row {
    background-color: transparent;
  }

  ::v-deep .el-table td.el-table__cell,
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border: none;
  }
  .msg-dialog-title {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: vw(16);
    color: #e4eeff;
    line-height: vh(22);
    height: vh(22);
    margin-bottom: vh(16);
  }
  .info-dialog-box {
    > div {
      display: flex;
      &:not(:last-child) {
        margin-bottom: vh(10);
      }
      > div {
        &:first-child {
          flex: 2;
        }
        flex: 1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: vw(14);
        color: #e4eeff;
      }
    }
  }
}
::v-deep .el-loading-mask {
  background-color: transparent;
}

.message-dialog {
  ::v-deep .el-dialog {
    width: vw(323);
    background: linear-gradient(180deg, #113963 0%, #2b63a2 100%);
    border-radius: 4px;
    box-shadow: 0px 4px 20px 0px rgba(69, 97, 128, 1);
    position: relative;
    margin-top: vh(400) !important;

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(
        90deg,
        #def1fe 31.37%,
        rgba(222, 241, 254, 0) 100%
      );
      z-index: -1;
      border-radius: 4px;
    }
  }

  ::v-deep .el-dialog__header {
    padding: 0;
  }
  ::v-deep .el-dialog__body {
    padding: vh(20) vw(20);
  }
  ::v-deep .el-dialog__headerbtn {
    top: vh(21);
    right: vw(20);
    z-index: 99;
  }
}
</style>

