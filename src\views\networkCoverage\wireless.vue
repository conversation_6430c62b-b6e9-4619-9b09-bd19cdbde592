<template>
  <div class="wired">
    <div class="wired-content">
      <div>
        <MapPart ref="wirelessMap" :area-type="areaType" keys="wirelessMap" @windowInfo="windowInfo" />
      </div>
    </div>
    <div v-drag class="right-part">
      <div class="task-distributed" @click="jumpover">
        <span>派发任务></span>
      </div>
      <!-- 小区归属到行政村下，取消互斥选择 -->
      <!-- <div v-if="commonCurrentArea.areaLevel>3" class="checkbox-group">
        <el-checkbox-group v-model="areaType" :max="1" @change="changeAreaType">
          <el-checkbox :true-label="1" false-label="null">行政村</el-checkbox>
          <el-checkbox :true-label="2" false-label="null">资管小区</el-checkbox>
        </el-checkbox-group>
      </div> -->
      <template v-if="commonCurrentArea.areaLevel>user.roleType">
        <div class="task-distributed" @click="backMap">
          <span>返回上级地图</span>
        </div>
      </template>
    </div>
    <el-dialog :visible.sync="dialogVisible" append-to-body class="info-dialog">
      <div class="info-dialog-content">
        <div class="info-dialog-title">信息</div>
        <div class="info-dialog-box">
          <div>我方态势：</div>
          <div :style="{color:info.situation==='0'?'#FFE016':'#35ff4c'}">{{ info.situation==='0'?'持平':'优势' }}</div>
        </div>
        <div class="info-dialog-box">
          <div>移动平均电平：</div>
          <div>{{ info.rsrpMAvg||'' }}</div>
        </div>
        <div class="info-dialog-box">
          <div>电信联通平均电平：</div>
          <div>{{ info.rsrpTuAvg||'' }}</div>
        </div>
        <div class="info-dialog-box">
          <div>移动平均信号质量：</div>
          <div>{{ info.sinrMAvg ||'' }}dBm</div>
        </div>
        <div class="info-dialog-box">
          <div>电信联通平均信号质量：</div>
          <div>{{ info.sinrTuAvg ||'' }}dBm</div>
        </div>
        <div class="info-dialog-box">
          <div>移动有效采样点数：</div>
          <div>{{ info.itemNumM ||'' }}</div>
        </div>
        <div class="info-dialog-box">
          <div>电信联通有效采样点数：</div>
          <div>{{ info.itemNumTu ||'' }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import MapPart from './components/MapPart.vue'
import { mapState } from 'vuex'
export default {
  name: 'Wireless',
  components: { MapPart },

  data() {
    return {
      areaType: 1,
      dialogVisible: false,
      user: {},
      info: {
        situation: '',
        rsrpMAvg: '',
        rsrpTuAvg: '',
        sinrMAvg: '',
        sinrTuAvg: '',
        itemNumM: '',
        itemNumTu: ''
      }
    }
  },
  computed: {
    ...mapState('networkCoverage', ['commonCurrentArea', 'commonSelectForm'])
  },
  mounted() {
    this.user = JSON.parse(window.sessionStorage.getItem('userInfo')) || {}
  },
  methods: {

    windowInfo(e) {
      console.log(e)
      Object.assign(this.info, e.obj)
      this.dialogVisible = true
    },

    backMap() {
      const area = { ...this.commonCurrentArea }
      area.areaLevel--
      if (area.areaLevel === 1) {
        this.$refs.wirelessMap.backProvince()
      } else if (area.areaLevel === 2) {
        this.$refs.wirelessMap.cityChange(this.commonSelectForm.cityCode)
      } else if (area.areaLevel === 3) {
        this.$refs.wirelessMap.countyChange(this.commonSelectForm.countyCode)
      } else if (area.areaLevel === 4) {
        this.$refs.wirelessMap.areaChange(this.commonSelectForm.areaCode)
      } else if (area.areaLevel === 5) {
        this.$refs.wirelessMap.villageChange(this.commonSelectForm.villageCode)
      }
    },

    // changeAreaType() {
    //   this.$refs.wirelessMap.getMapInfo({ areaId: this.$refs.wirelessMap.selectForm.areaCode, areaLevel: 4 })
    // },

    jumpover() {
      this.$router.push({ path: '/market/taskScheduling', query: { ...this.$refs.wirelessMap.selectForm, from: 'networkCoverage', level: this.commonCurrentArea.areaLevel }})
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.wired {
  width: 100%;
  position: relative;
  .wired-content {
    display: flex;
    color: #ffffff;
    height: 100%;
    > div {
      flex: 1;
      flex-shrink: 0;
    }
  }

  .right-part {
    position: absolute;
    top: vh(45);
    left: vw(1665);
    z-index: 99;
    .task-distributed {
      width: vw(215);
      height: vh(49);
      line-height: vh(49);
      background: url("~@/assets/images/networkCoverage/distributed.png")
        no-repeat;
      background-size: 100% 100%;
      text-align: center;
      margin-top: vh(16);
      cursor: pointer;
      span {
        background-image: linear-gradient(180deg, #ffffff 50.17%, #94e5ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-family: YouSheBiaoTiHei;
        font-weight: 400;
        font-size: vw(24);
      }
    }

    .checkbox-group {
      margin-top: vh(16);
      padding: vh(15) 0 vh(15) vw(22);
      width: vw(215);
      background: url("~@/assets/images/networkCoverage/distributed.png")
        no-repeat;
      background-size: 100% 100%;
      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        > label {
          &:first-child {
            margin-bottom: vh(10);
          }
        }
      }

      ::v-deep .el-checkbox {
        display: inline-flex;
        align-items: center;
      }
      ::v-deep .el-checkbox__label {
        color: #e4eeff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
      }
      ::v-deep .el-checkbox__inner {
        border: 2px solid #BED4FF;
        background-color: transparent;
        width: vh(16);
        height: vh(16);
      }
      ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #e4eeff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
      }

      ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
        background-color: #345596;
      }
      ::v-deep .el-checkbox__inner::after {
        border-color: #ffffff;
        top: -2px;
        height: 10px;
      }
      ::v-deep .el-checkbox__inner::before {
        top: 4px;
      }
      ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #253a65;
      }
    }

    .compare-icon {
      margin-top: vh(24);
      text-align: right;
      ::v-deep .el-button {
        padding: vh(12) vw(7);
        color: #e4eeff;
        background: linear-gradient(
              181.96deg,
              #6498ff -38.96%,
              #1d4ba7 39.59%,
              #142d60 98.35%
            )
            padding-box,
          linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%)
            border-box;
        border: 1.5px solid transparent;
        box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: vw(14);
      }

      ::v-deep .el-icon-my-zdy {
        background: url("~@/assets/images/networkCoverage/union.png") center
          no-repeat;
        font-size: 12px;
        background-size: cover;
      }
      ::v-deep .el-icon-my-zdy:before {
        content: "替";
        font-size: vw(14);
        visibility: hidden;
      }

      ::v-deep .el-icon-my-zdy {
        font-size: vw(14);
      }

      ::v-deep .el-icon-my-zdy:before {
        content: "\e611";
      }
    }
  }
}
.info-dialog {
  ::v-deep .el-dialog {
    width: vw(280);
    background: linear-gradient(180deg, #113963 0%, #2b63a2 100%);
    border-radius: 4px;
    box-shadow: 0px 4px 20px 0px #456180;
    position: relative;
    margin-top: vh(273) !important;

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(
        90deg,
        #def1fe 31.37%,
        rgba(222, 241, 254, 0) 100%
      );
      z-index: -1;
      border-radius: 4px;
    }
  }

  ::v-deep .el-dialog__header {
    padding: 0;
  }
  ::v-deep .el-dialog__body {
    padding: vh(20) vw(20);
  }
  ::v-deep .el-dialog__headerbtn {
    top: vh(21);
    right: vw(20);
    z-index: 99;
  }
}
.info-dialog-content {
  ::v-deep .el-tabs__nav-wrap::after {
    display: none;
  }
  ::v-deep .el-tabs__item {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: vw(16);
    color: #e4eeff;
    padding: 0 vw(13);
  }
  ::v-deep .el-tabs__item.is-active {
    color: #77f1ff;
  }
  ::v-deep .el-tabs__active-bar {
    background-color: #77f1ff;
  }
  ::v-deep .el-tabs .el-tabs__nav-wrap {
    padding: 0;
  }

  ::v-deep .el-table {
    background-color: transparent !important;
    &::before {
      display: none;
    }
    tr {
      background-color: unset;
    }
    .headerCellClassName {
      background: transparent !important;
      color: #e4eeff !important;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
    }
    .cellClassName {
      color: #e4eeff !important;
      border-color: #405d8b;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
    }
    .el-table__cell {
      padding: vh(5) 0;
    }
  }

  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: transparent !important;
  }

  ::v-deep .el-table__row {
    background-color: transparent;
  }

  ::v-deep .el-table td.el-table__cell,
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border: none;
  }
  .info-dialog-title {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: vw(16);
    color: #e4eeff;
    line-height: vh(22);
    height: vh(22);
    margin-bottom: vh(16);
  }
  .info-dialog-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:not(:last-child){
      margin-bottom: vh(10);
    }

    > div {
      &:first-child {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: vw(14);
        color: #e4eeff;
      }
      &:last-child {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: vw(14);
        color: #e4eeff;
      }
    }
  }
}
</style>
