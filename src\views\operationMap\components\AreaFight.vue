<template>
  <div class="area-fight">
    <EchartsMap ref="echartsMap" :filter-list="filterList" @back-last-level="backLastLevel" @map-init="getMapInfo" @b-map-init="getBMapInfo" @start-fight="streetStartFight"/>
    <div class="select-part">
      <div class="area-select ">
        <div v-show="!showSelectSearch" class="search-icon">
          <i class="el-icon-search" @click="openSearchPanel"></i>
          <el-dropdown @command="handleCommand" placement="bottom">
            <span class="el-dropdown-link">
              {{ choseProvince.name }}<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in provinceOpts" :key="'province_' + item.areaId" :class="item.areaId === choseProvince.areaId ? 'active' : 'text'" :command="item">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown @command="handleCommand" placement="bottom">
            <span class="el-dropdown-link">
              {{ choseCounty.name }}<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in countyOpts" :key="'county_' + item.areaId" :class="item.areaId === choseCounty.areaId ? 'active' : ''" :command="item">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown @command="handleCommandStreet" placement="bottom">
            <span class="el-dropdown-link">
              {{ choseBlock.streetName }}<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in blockOpts" :key="'street_' + item.streetId" :class="item.streetId === choseBlock.streetId ? 'active' : ''" :command="item">{{ item.streetName }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="search-select-input">
          <el-autocomplete
            ref="searchRef"
            v-show="showSelectSearch"
            v-model="areaSelect"
            :fetch-suggestions="getSearchedArea"
            placeholder="请输入内容"
            value-key="areaName"
            @select="handleSelect"
            prefix-icon="el-icon-search"
            @blur="closeSearchPanel"
          >
            <template v-slot:default="{ item }">
              <div style="display: flex;align-items: center;justify-content: flex-start;">
                <div class="level-color" :class="getLevelColorClass(item.areaLevelName)">{{ item.areaLevelName }}</div>
                {{ item.areaName }}
              </div>
            </template>
          </el-autocomplete>
        </div>
      </div>
      <TitlePart :popover-content="popoverContent" :question="true" title="筛选条件" width="238" />
      <div class="select-group">
        <div v-for="item in filterList" :key="item.label" class="select-item">
          <div class="select-item-title">{{ item.label }}</div>
<!--          <div class="select-item-checkbox">-->
<!--            <el-checkbox v-model="item.checked" @change="checkBoxChange(item)">不限制</el-checkbox>-->
<!--          </div>-->
          <div class="select-item-input">
            <div class="line">介于</div>
            <el-input v-model="item.firstInput"/>  <!-- @blur="valueChange(item)" -->
            <div class="line">至</div>
            <el-input v-model="item.secondInput"/>  <!-- @blur="valueChange(item)" -->
            <div class="line">之间</div>
          </div>
        </div>
      </div>
      <div class="button-group">
        <div class="confirm-btn" @click="beginFight">发起攻坚</div>
        <div class="search-btn" @click="search">查询</div>
        <div :class="currentArea.areaLevel > 2 ? 'search-btn b-map-reset' : 'reset-btn'" @click="reset">
          <img src="@/assets/images/operation/Union.svg">
          <div>重置</div>
        </div>
      </div>
    </div>
    <DialogPage :visible.sync="dialogVisible" title="区域作战" @before-close="beforeCloseDialog">
      <div class="step-container">
        <div class="step">
          <template v-for="(item, index) in steps">
            <div style="display: flex;flex-direction: column;align-items: center;justify-content: space-between;" :key="'num_' + index" @click="currentStep = index">
              <div :class="{ active: currentStep === index }" class="step-circle">{{ index + 1 }}</div>
              <div :class="{active: currentStep === index}" class="step-label">{{ item }}</div>
            </div>
            <div v-if="index < steps.length - 1" class="step-line" :key="'line_' + index"/>
          </template>
        </div>
      </div>
      <div class="operation-content">
        <BattleList v-show="currentStep === 0" ref="fightListRef" :out-params="outParams" />
        <BaseInfo v-show="currentStep === 1" ref="baseInfoRef" :out-params="outParams" @getGoalListTempId="getGoalListTempId" />
        <WorkDistributed v-show="currentStep === 2" ref="dispatchRef" :out-params="outParams"/>
      </div>
      <template #footer>
        <div class="page-footer">
          <el-button v-if="currentStep > 0" @click="currentStep--">上一步</el-button>
          <el-button v-if="currentStep < steps.length - 1" @click="currentStep++">下一步</el-button>
          <el-button v-if="currentStep === steps.length - 1" @click="dispatchAndCreateTask" :disabled="inDispatch">派发</el-button>
        </div>
      </template>
    </DialogPage>
    <DialogPage :visible.sync="showDispatchSuccessDialog" :width="503" @before-close="closeDispatchSuccessDialog">
      <div class="img-container">
        <div class="success-img"></div>
      </div>
      <template #footer>
        <div class="page-footer">
          <el-button @click="showDispatchSuccessDialog = false">确定</el-button>
        </div>
      </template>
    </DialogPage>
    <EchartsLegend v-show="currentArea.areaLevel < 3" title="宽带占比 [街区数量]" :legend-list="legendList" />
  </div>
</template>
<script>
// import mapAreaInfoJson  from '../getMapAreaInfo.json'
import TitlePart from '@/views/operation/components/TitlePart.vue'
import EchartsMap from './EchartsMap.vue'
import DialogPage from './DialogPage.vue'
import BattleList from './BattleList.vue'
import BaseInfo from './BaseInfo.vue'
import WorkDistributed from './WorkDistributed.vue'
import { getMapInfo, getSearchAllAreaList, getStreetInfoByCounty, makeDispatchTask } from '@/api/operation'
import EchartsLegend from '@/components/common/EchartsLegend.vue'
export default {
  name: 'AreaFight',
  components: { EchartsMap, TitlePart, DialogPage, BattleList, BaseInfo, WorkDistributed, EchartsLegend },
  data() {
    return {
      dialogVisible: false,
      filterList: [
        {
          checked: false,
          label: '端口数(个)',
          firstInput: '',
          secondInput: ''
        },
        {
          checked: false,
          label: '区域渗透率(%)',
          firstInput: '',
          secondInput: ''
        },
        // {
        //   checked: false,
        //   label: '区域商客渗透率(%)',
        //   firstInput: '',
        //   secondInput: ''
        // },
        {
          checked: false,
          label: '异网客户数(个)',
          firstInput: '',
          secondInput: ''
        },
        {
          checked: false,
          label: '图商市占率(%)',
          firstInput: '',
          secondInput: ''
        }
      ],
      popoverContent: `
      <div class="popoverContent">
        <div style="margin-bottom: 25px">1.区域渗透率=端口占用/全量端口；</div>
        <div style="margin-bottom: 25px">2.区域商客渗透率=政企场景端口数占用/全量政企场景端口；</div>
        <div>3.图商市占率=1-异网客户/区域全量客户。</div>
      </div>`,
      currentStep: 0,
      steps: ['作战清单', '基础信息', '工单派发'],
      legendList: [
        { color: '#FF393980', borderColor: '#FF8787', text: '宽带占比＜25%' },
        { color: '#FF838380', borderColor: '#FFBCB9', text: '25%≤宽带占比＜50%' },
        { color: '#37D89280', borderColor: '#A9FFDA', text: '50%≤宽带占比＜75%' },
        { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '75%≤宽带占比≤100%' }
      ],
      showSelectSearch: false, // 是否显示可搜索下拉框
      areaSelect: null, // 搜索下拉框选择的数据
      areaSelectOpts: [], // 全文区域搜索项
      provinceOpts: [], // 省下的选择项 市州
      countyOpts: [], // 县下的选择项 县
      blockOpts: [], // 街区下选择项 街区
      choseProvince: { name: '全省', areaId: '999' },
      choseCounty: { name: '县', areaId: '' },
      choseBlock: { streetName: '街区', streetId: '' },
      currentArea: {}, // 地图图层当前显示的整体区域信息
      outParams: null, // 传给作战清单的请求信息
      inDispatch: false, // 派发按钮禁用
      showDispatchSuccessDialog: false,
      // 存下当前省市区查询条件
      currentCity: '', // 当前省id
      currentCounty: '', // 当前市id
      currentCountyName: '', // 当前市Name
      goalListTempId: ''
    }
  },
  computed: {
  },
  watch: {
    currentStep(val) {
      if (val === 2) {
        const [exclusionList, stepOneNotValidate] = this.$refs.fightListRef.getReturnParams()
        this.$refs.dispatchRef.getTableData(exclusionList)
      }
    }
  },
  created() {
  },
  async mounted() {
    await this.getMapInfo({
      areaId: '999',
      areaLevel: 1
    })
  },
  methods: {
    checkBoxChange(item) {
      // console.log(item)
      if (item.checked) {
        item.firstInput = ''
        item.secondInput = ''
      }
    },
    valueChange(item) {
      // console.log(item)
      if (item.firstInput || item.secondInput) {
        item.checked = false
      }
    },
    // 开始作战 直接界面表单发起 准备参数 弹出窗口
    beginFight() {
      // console.log(this.getSearchParams())
      this.currentStep = 0
      this.outParams = this.getSearchParams()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.fightListRef.init({ type: 'params' }) // 参数类型方式打开作战信息准备
        // this.$refs.dispatchRef.getTableData() // 改到进入第三步 在请求 然后还需要带上第一步中取消的数据
      })
    },
    // 开始作战 下钻到街区后选择了街区信息后 发起的 准备参数 弹出窗口
    streetStartFight(ids) {
      this.currentStep = 0
      this.outParams = { ...this.currentArea, chooseStreetIds: ids }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.fightListRef.init({ type: 'ids' }) // 街区ids类型方式打开作战信息准备
        // this.$refs.dispatchRef.getTableData()
      })
    },
    getSearchParams() {
      const params = { areaId: this.currentArea.areaId, areaLevel: this.currentArea.areaLevel }
      this.filterList.forEach(item => {
        switch (item.label) {
          case '端口数(个)':
            params.minPort = item.firstInput
            params.maxPort = item.secondInput
            params.isAllPort = item.checked ? '1' : '0'
            break
          case '区域渗透率(%)':
            params.minAreaRate = item.firstInput
            params.maxAreaRate = item.secondInput
            params.isAllAreaRate = item.checked ? '1' : '0'
            break
          case '区域商客渗透率(%)':
            params.minBusiRate = item.firstInput
            params.maxBusiRate = item.secondInput
            params.isAllBusiRate = item.checked ? '1' : '0'
            break
          case '异网客户数(个)':
            params.minDiffNet = item.firstInput
            params.maxDiffNet = item.secondInput
            params.isAllDiffNet = item.checked ? '1' : '0'
            break
          case '图商市占率(%)':
            params.minMapBusiRate = item.firstInput
            params.maxMapBusiRate = item.secondInput
            params.isAllMapBusiRate = item.checked ? '1' : '0'
            break
        }
      })
      return params
    },
    search() {
      if (this.currentArea.areaLevel > 2) {
        // this.$refs.echartsMap.getBMapInfo()
        this.getBMapInfo({ ...this.currentArea, name: this.choseCounty.name })
      } else {
        this.$refs.echartsMap.getStreetNum()
        this.changeLegendData()
      }
    },
    reset() {
      this.filterList = [
        {
          checked: false,
          label: '端口数(个)',
          firstInput: '',
          secondInput: ''
        },
        {
          checked: false,
          label: '区域渗透率(%)',
          firstInput: '',
          secondInput: ''
        },
        {
          checked: false,
          label: '区域商客渗透率(%)',
          firstInput: '',
          secondInput: ''
        },
        {
          checked: false,
          label: '异网客户数(个)',
          firstInput: '',
          secondInput: ''
        },
        {
          checked: false,
          label: '图商市占率(%)',
          firstInput: '',
          secondInput: ''
        }
      ]
      this.$refs.echartsMap.getStreetNum(params)
    },
    // 搜索调用接口
    getSearchedArea(queryString, cb) {
      if (queryString === null || queryString === '') {
        cb([])
      } else {
        getSearchAllAreaList({ keyWord: queryString || '', pageNum: 1, pageSize: 100 }).then(res => {
          cb([...res.data.records])
        })
      }
    },
    // 打开搜索检索框 并且获取焦点
    openSearchPanel() {
      this.showSelectSearch = true
      this.$nextTick(() => {
        this.$refs.searchRef.focus()
      })
    },
    closeSearchPanel() { // 设置延时关闭 避免选择时先关闭了 还没选中
      setTimeout(() => {
        this.showSelectSearch = false
      }, 500)
    },
    // 最终选择事件 1-2会自动调用下拉的赋值初始化 从三起需要判断 显示归属值
    async handleSelect(value) {
      if (parseInt(value.areaLevel) > 2) {
        this.getBMapInfo(value.areaLevel === 3 ? value : { areaId: value.countyCode, areaLevel: 3 })
        // 首先获取市州下拉数据 判断出市州的选项
        if (this.provinceOpts) { // 存在值 不在请求 一定会存在
          this.choseProvince = this.provinceOpts.find(item => { return item.areaId === value.cityCode })
          // 根据市州初始化到当前区县的下拉
          const res = await getMapInfo({ ...this.choseProvince })
          this.countyOpts = [{ areaId: 'all', areaLevel: '3', name: '全部' }, ...res.data.children.map(item => {
            return {
              name: item.areaName,
              areaId: item.areaId,
              areaLevel: item.areaLevel,
              centralPoint: item.centralPoint
            }
          })]
        }
        if (parseInt(value.areaLevel) === 3) { // 选择的是区县
          this.choseCounty = { name: value.areaName, areaId: value.areaId }
          this.currentArea = { areaId: value.areaId, areaLevel: value.areaLevel }
        } else { // 只能是街区级别了
          this.currentArea = { areaId: value.countyCode, areaLevel: 3 }
          this.choseCounty = this.countyOpts.find(item => { return item.areaId === value.countyCode })
          this.choseBlock = { streetName: value.areaName, streetId: value.areaId }
        }
        // 还需要根据区县 获取街区的下拉数据 看逻辑 无论如何这里都需要获取街区下拉
        // 获取街道 图层数据
        const res = await getStreetInfoByCounty(this.getSearchParams())
        // 街区的下拉信息填充
        this.blockOpts = [{ streetId: 'all', areaLevel: '4', streetName: '全部' }, ...res.data.streetList]
        if (parseInt(value.areaLevel) > 3) { // 选择了街道的 尝试地图渲染后 定位到对应地方
          const area = this.blockOpts.find(item => { return item.streetId === this.choseBlock.streetId })
          setTimeout(() => {
            const [lng, lat] = area.centerPoint.split(',')
            this.$refs.echartsMap.toBMapCenter({ lng, lat })
          }, 1000)
        }
      } else {
        this.getMapInfo(value)
      }
    },
    handleCommand(value) {
      // 选择全部
      if (value.areaId === 'all' && value.areaLevel === '3') {
        this.getMapInfo({
          areaId: this.currentCity,
          areaLevel: '2'
        })
        return
      }
      if (value.areaLevel === '3') {
        this.getBMapInfo(value)
        this.currentCounty = value.areaId
        this.currentCountyName = value.name
      } else {
        this.getMapInfo(value)
        this.currentCity = value.areaId
      }
    },
    getLevelColorClass(val) {
      if (val === '省') {
        return 'blue'
      } else if (val === '区县') {
        return 'green'
      } else if (val === '街区') {
        return 'orange'
      } else {
        return 'red'
      }
    },
    // 街道定位到对应中心区域
    handleCommandStreet(value) {
      // 街道选择全部
      if (value.streetId === 'all') {
        this.getBMapInfo({
          areaId: this.currentCounty,
          name: this.currentCountyName,
          areaLevel: '3'
        })
        return
      }
      this.choseBlock = { ...value }
      const [lng, lat] = value.centerPoint.split(',')
      this.$refs.echartsMap.toBMapCenter({ lng, lat }, value.streetId)
    },
    // 区县点击后 显示百度地图后 初始化区县下拉数据
    getBMapInfo(parentData) {
      this.currentArea = { areaId: parentData.areaId, areaLevel: parentData.areaLevel }
      const params = this.getSearchParams()
      // 获取街道 图层数据
      getStreetInfoByCounty(params).then(({ code, data, msg }) => {
        if (code === 200) {
          // 内部填充百度地图信息
          this.$refs.echartsMap.initBMapData(data)
          // 街区的下拉信息填充
          this.blockOpts = [{ streetId: 'all', areaLevel: '4', streetName: '全部' }, ...data.streetList]
          this.choseCounty = { name: parentData.name, areaId: parentData.areaId }
          this.choseBlock = { streetName: '街区', streetId: '' }
        } else {
          this.$message.error(msg)
        }
      })
    },
    changeLegendData() {
      if (parseInt(this.currentArea.areaLevel) < 3) {
        this.legendList = [
          { color: '#FF393980', borderColor: '#FF8787', text: '宽带占比＜25%' },
          { color: '#FF838380', borderColor: '#FFBCB9', text: '25%≤宽带占比＜50%' },
          { color: '#37D89280', borderColor: '#A9FFDA', text: '50%≤宽带占比＜75%' },
          { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '75%≤宽带占比≤100%' }
        ]
      } else {
        this.legendList = [
          { color: '#FF393980', borderColor: '#FF8787', text: '≤5' },
          { color: '#FF838380', borderColor: '#FFBCB9', text: '5-10' },
          { color: '#37D89280', borderColor: '#A9FFDA', text: '10-20' },
          { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '≥20' }
        ]
      }
    },
    // 根据父级id下钻数据echarts图 只有省-市 街道点了是初始化百度地图
    async getMapInfo(value) {
      const { areaId, areaLevel } = value
      this.currentArea = value
      this.changeLegendData()
      getMapInfo({
        areaId,
        areaLevel
      }).then(async({ code, data }) => {
        if (code === 200) {
          await this.$refs.echartsMap.initMapData(data)
          const arr = data.children.map(item => {
            return {
              name: item.areaName,
              areaId: item.areaId,
              areaLevel: item.areaLevel,
              centralPoint: item.centralPoint
            }
          })
          if (data.areaLevel === '1') { // 省
            this.provinceOpts = [{ areaId: '999', areaLevel: 1, name: '全省' }, ...arr] // 下两级直接清空
            this.countyOpts = []
            this.blockOpts = []
            this.choseProvince = { name: '全省', areaId: '999' }
            this.choseCounty = { name: '县', areaId: '' }
            this.choseBlock = { streetName: '街区', streetId: '' }
          } else if (data.areaLevel === '2') { // 县
            this.countyOpts = [{ areaId: 'all', areaLevel: '3', name: '全部' }, ...arr]
            this.blockOpts = []
            this.choseProvince = { name: data.areaName, areaId: data.areaId }
            this.choseCounty = { name: '县', areaId: '' }
            this.choseBlock = { streetName: '街区', streetId: '' }
          } else if (data.areaLevel === '3') {
            // this.blockOpts = [...arr]
          } else {
            // this.choseBlock = { streetName: data.areaName, streetId: data.areaId }
          }
        }
      })
    },
    // 对话框关闭前 执行的操作
    beforeCloseDialog(done) {
      console.log('对话框关闭了')
      // 需要去清空三个组件的数据
      this.$refs.fightListRef.clearData()
      this.$refs.baseInfoRef.clearData()
      this.$refs.dispatchRef.clearData()
      done()
    },
    closeDispatchSuccessDialog(done) {
      done()
    },
    // 派发任务 并且创建任务
    dispatchAndCreateTask() {
      this.inDispatch = true
      // 先组装参数 创建参数
      let params = { ...this.outParams, goalListTempId: this.goalListTempId }
      // 获取第一步 作战清单参数
      const [exclusionList, stepOneNotValidate] = this.$refs.fightListRef.getReturnParams()
      if (stepOneNotValidate) {
        this.$message.warning('作战清单参数异常，请重新选择！')
        this.currentStep = 0
        return false
      }
      // 将第一步的数据放入 params中
      params.exclusionList = exclusionList
      // 对第二步数据做校验
      const [baseInfo, stepTwoNotValidate] = this.$refs.baseInfoRef.getReturnParams()
      if (stepTwoNotValidate) {
        this.$message.warning('基础信息不完整，请重新填写！')
        this.currentStep = 1
        return false
      }
      // 对第三步数据做校验
      const [dispatchInfo, stepThreeValidate] = this.$refs.dispatchRef.getReturnParams()
      if (stepThreeValidate) {
        this.currentStep = 2
        return false
      }
      params = { ...params, ...baseInfo, ...dispatchInfo }
      console.log(params)
      makeDispatchTask(params).then(res => {
        this.inDispatch = false
        this.dialogVisible = false
        this.showDispatchSuccessDialog = true
      }).catch(err => {
        this.inDispatch = false
      })
    },
    backLastLevel(level) {
      if (level === 2) { // 市州级 返回到省级
        this.getMapInfo({ areaId: '999', areaLevel: 1 })
      } else if (level === 3) {
        this.getMapInfo({ areaId: this.choseProvince.areaId, areaLevel: 2 })
      }
    },

    getGoalListTempId(val) {
      this.goalListTempId = val
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.area-fight {
  .select-part {
    position: absolute;
    top: 0;
    right: vw(30);
  }
  // 地域选择部分
  .area-select {
    width: vw(441);
    height: vh(50);
    //line-height: vh(50);
    //padding: 0 vw(20);
    position: relative;
    background: linear-gradient(#254177) padding-box,
      linear-gradient(
          90deg,
          rgba(178, 209, 255, 0.5) 0%,
          rgba(123, 176, 255, 0.25) 50%,
          rgba(178, 209, 255, 0.5) 100%
        )
        border-box;
    border: vw(2) solid transparent;
    border-radius: 10px;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: vw(14);
    color: #ffffff;
    .search-icon{
      font-size: vw(16);
      display: flex;
      margin: 0 vw(20);
      align-items: center;
      justify-content: space-between;
      height: 100%;
      .el-icon-search{
        font-size: 14px;
        cursor: pointer;
      }
      ::v-deep .el-dropdown{
        color: white;
        //margin-left: 10px;
        font-size: vw(16);
      }
    }
    .search-select-input{
      font-size: vw(16);
      display: flex;
      align-items: center;
      ::v-deep .el-input__inner {
        width: vw(441);
        height: vh(50);
        line-height: vh(50);
        //padding: 0 vw(20);
        position: relative;
        background: linear-gradient(#254177) padding-box,
        linear-gradient(
            90deg,
            rgba(178, 209, 255, 0.5) 0%,
            rgba(123, 176, 255, 0.25) 50%,
            rgba(178, 209, 255, 0.5) 100%
        )
        border-box;
        border: vw(2) solid transparent;
        border-radius: 10px;
        font-size: vw(16);
        padding-left: 28px;
        color: white !important;
      }
    }
  }

  .select-group {
    .select-item {
      display: flex;
      align-items: center;
      padding: vh(20) vw(20);
      width: vw(441);
      height: vh(80);
      background: url("../../../assets/images/operation/select-item.png")
        no-repeat;
      background-size: 100% 100%;
      margin-bottom: vh(16);
      &:last-child {
        margin-bottom: 0;
      }

      .select-item-title {
        color: #ffffff;
        width: vw(80);
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
        margin-right: vw(14);
      }
      .select-item-checkbox {
        margin-right: vw(10);
        ::v-deep .el-checkbox__label {
          color: #ffffff;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: vw(16);
        }
        ::v-deep .el-checkbox__inner {
          border: vw(2) solid #74a2ff;
          background-color: transparent;
          width: 16px;
          height: 16px;
        }

        ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
          background-color: #253a65;
        }
        ::v-deep .el-checkbox__inner::after {
          top: -2px;
          height: 10px;
        }
      }

      .select-item-input {
        display: flex;
        align-items: center;
        color: white;
        font-size: vw(16);
        ::v-deep .el-input{
          width: vw(90);
        }
        ::v-deep .el-input__inner {
          width: vw(90);
          height: vh(40);
          border-radius: 6px;
          background: #142a4e;
          border: 1px solid #95b8ff80;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: vw(16);
          color: #ffffff;
          text-align: center;
        }
      }
      .line {
        color: #b3baca;
        margin: 0 vw(6);
      }
    }
  }
  // 按钮部分
  .button-group {
    display: flex;
    align-items: center;
    margin-top: vh(23);
    .confirm-btn {
      width: vw(200);
      border-radius: 4px;
      padding: vh(10) vw(20);
      position: relative;
      background: linear-gradient(
            181.96deg,
            #6498ff -38.96%,
            #1d4ba7 39.59%,
            #142d60 98.35%
          )
          padding-box,
        linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%)
          border-box;
      border: vw(2) solid transparent;
      color: #ffffff;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      text-align: center;
      cursor: pointer;
    }
    .search-btn {
      width: vw(93.12);
      border-radius: 4px;
      padding: vh(10) vw(20);
      margin: 0 vw(20);
      position: relative;
      background: linear-gradient(
            181.96deg,
            #6498ff -38.96%,
            #1d4ba7 39.59%,
            #142d60 98.35%
          )
          padding-box,
        linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%)
          border-box;
      border: vw(2) solid transparent;
      color: #ffffff;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      text-align: center;
      cursor: pointer;
      &.b-map-reset{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: vw(100);
        img {
          width: vw(20);
          margin-right: vw(2.88);
        }
      }
    }

    .reset-btn {
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(18);
      color: #e4eeff;
      cursor: pointer;
      img {
        width: vw(20);
        margin-right: vw(2.88);
      }
    }
  }
}
.page-footer {
  ::v-deep .el-button {
    padding: vh(8) vw(16);
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(16);
    color: #e4eeff;
    background: linear-gradient(
          181.96deg,
          #6498ff -38.96%,
          #1d4ba7 39.59%,
          #142d60 98.35%
        )
        padding-box,
      linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
    border: vw(2) solid transparent;
    box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
  }
}
.step-container {
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
  .step {
    display: flex;
    align-items: center;
    position: relative;
    justify-content: space-between;
    width: 100%;
  }

  .step-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #8cb2ffcc;
    color: #212832;
    text-align: center;
    line-height: 20px;
    font-size: vw(16);
    cursor: pointer;
    z-index: 2;
    //margin: 0 vw(22);
  }

  .step-circle.active {
    background: linear-gradient(201.75deg, #ffffff 14.26%, #68f7ff 85.74%);
  }

  .step-label {
    margin-top: vh(5);
    font-size: vw(16);
    font-family: PingFang SC;
    font-weight: 400;
    color: #a2b6f3;
    text-align: center;
    width: vw(80);
  }
  .step-label.active {
    font-family: PingFang SC;
    font-weight: 500;
    color: #68f7ff;
  }

  .step-line {
    width: vw(420);
    height: vw(2);
    background: #a2b6f3;
    z-index: 1;
    margin-bottom: vw(21);
  }
}
.operation-content {
  margin-top: vh(48);
}
.img-container{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  .success-img{
    width: vw(106);
    height: vh(148);
    background-image: url("../../../assets/images/operation/dispatch-success.png");
    background-size: 100% 100%;
  }
}
.echarts-legend {
  position: fixed;
  left: vw(77);
  bottom: vh(80);
  .legend-title {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: vw(16);
    color: #ffffff;
  }
  .legend-icon {
    color: #ffffff;
    display: flex;
    align-items: center;
    margin-top: 10px;
    > div {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      &:first-child {
        margin-right: vw(12);
        width: vw(13);
        height: vw(13);
        border: vw(2) solid;
      }
    }
  }
}
::v-deep .el-dropdown-menu el-popper{
  background: linear-gradient(
      181.96deg,
      #6498ff -38.96%,
      #1d4ba7 39.59%,
      #142d60 98.35%
  )
  padding-box,
  linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
}
.el-dropdown-menu{
  background: linear-gradient(
      181.96deg,
      #6498ff -38.96%,
      #1d4ba7 39.59%,
      #142d60 98.35%
  )
  padding-box,
  linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
  border: none;
  color: white;
}
.el-dropdown-menu__item{
  line-height: vh(30);
  color: white;
}
.el-dropdown-menu__item.active{
  color: #6498ff;
  background-color: #e3e3e3;
}
.level-color{
  display: inline-block;
  padding: 3px 10px;
  margin-right: 10px;
  line-height: 16px;
  border-radius: 5px;
  color: white;
  &.blue{
    background-color: #2F71CB;
  }
  &.green{
    background-color: #1F8C92;
  }
  &.orange{
    background-color: #B98359;
  }
  &.red{
    background-color: #B95C59;
  }
}
</style>
<style lang="scss">
.el-autocomplete-suggestion{
  background: #1D325C;
  border: none !important;
  &.is-loading{
    li:hover{
      background: none;
    }
  }
  li{
    color: white;
    &:hover{
      color: #1D4BA7;
      background-color: #6498ff;
    }
  }
}
</style>
<style>
.prop-class .popper__arrow::after {
  top: 0 !important;
  border-bottom-color: #1f4a9c !important;
}
</style>
