<template>
  <div class="base-info">
    <el-row justify="center" type="flex">
      <el-form :model="formData" class="formData" label-position="top">
        <el-form-item label="作战名称" prop="taskName">
          <el-input v-model="formData.taskName" placeholder="请输入作战名称" size="mini" />
        </el-form-item>
        <el-form-item label="作战描述" prop="taskDesc">
          <el-input v-model="formData.taskDesc" :rows="6" placeholder="请输入作战描述" size="mini" type="textarea" />
        </el-form-item>
        <el-form-item label="作战周期">
          <el-date-picker
            v-model="formData.dateTimeRange"
            end-placeholder="结束日期"
            popper-class="datePopperClass"
            range-separator="-"
            start-placeholder="开始日期"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="预期目标">
          <div class="target-handle-type">
            <el-radio v-model="targetHandleType" :label="1">设置统一目标</el-radio>
            <el-radio v-model="targetHandleType" :label="2">上传街区明细目标</el-radio>
          </div>
          <div v-show="targetHandleType === 1">
            <div v-for="(select, i) in targetSelects" class="expected-select-box">
              <div class="expected-select">
                <el-select v-model="select.targetType" placeholder="请选择目标项" popper-class="custom-select" size="mini" @change="targetSelectChange(i)">
                  <el-option v-for="(opt, index) in targetOpts" :key="'target_' + i + '_option_' + index" :label="opt.label" :value="opt.value" />
                </el-select>
              </div>
              <div class="expected-input">
                <el-input v-model="select.targetValue" placeholder="请输入目标值" size="mini" />
              </div>
              <div v-show="i === targetSelects.length - 1" class="add-icon">
                <el-button @click="addNewSelectTarget">+</el-button>
              </div>
            </div>
          </div>
          <div v-show="targetHandleType === 2" class="file-download-upload">
            <el-button size="small" type="text" @click="downloadTemplateFile">下载模板</el-button>
            <el-upload
              ref="upload"
              class="upload-demo"
              action=""
              :on-remove="handleRemove"
              :on-exceed="handleFileExceed"
              :on-change="handleFileChange"
              :http-request="uploadTemplate"
              :file-list="fileList"
              :auto-upload="true"
              accept=".csv,.xls,.xlsx"
              :limit="1"
            >
              <el-button size="small" type="text">上传文件</el-button>
              <div slot="tip" class="el-upload__tip">只能上传xls\xlsx文件</div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </el-row>
  </div>
</template>
<script>
import { downloadTemplate, getStreetTargetOpts, uploadTemplate } from '@/api/operation'

export default {
  name: 'BaseInfo',
  props: {
    outParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {
        taskName: '',
        taskDesc: '',
        dateTimeRange: []
      },
      targetOpts: [],
      targetMap: {},
      targetSelects: [
        { targetType: '', targetValue: '' }
      ],
      targetHandleType: 1,
      fileList: []
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
    this.getStreetTargetOptsList()
  },
  methods: {
    getStreetTargetOptsList() {
      this.targetOpts = []
      getStreetTargetOpts({ tableName: 'street_goal_kpi', fieldName: 'kpi' }).then(res => {
        res.data?.forEach(item => {
          this.targetOpts.push({ label: item.val, value: item.code })
          this.targetMap[item.code] = item.val
        })
      })
    },
    addNewSelectTarget() {
      this.targetSelects.push({ targetType: '', targetValue: '' })
    },
    // 目标类型选择项 change事件 需要判断最后（当前）选择的这个是否 与其他选择好的冲突了（一样） 冲突了就把当前这个置空
    targetSelectChange(i) {
      console.log(this.targetSelects[i].targetType)
      this.targetSelects.forEach((item, index) => {
        if (item.targetType !== '' && i !== index && item.targetType === this.targetSelects[i].targetType) { // 找到不为空且不是自己且值相同的 存在
          this.$message.warning('选择的目标已存在，请重新选择！')
          this.targetSelects[i].targetType = ''
        }
      })
      console.log(i)
    },
    clearData() {
      this.formData = {
        taskName: '',
        taskDesc: '',
        dateTimeRange: []
      }
      this.targetSelects = [{ targetType: '', targetValue: '' }]
    },
    getReturnParams() {
      // 先对参数做校验
      let validate = false
      if (this.formData.taskName === '' || this.formData.taskDesc === '' || this.formData.dateTimeRange.length === 0) validate = true
      const targets = []
      this.targetSelects.forEach(item => {
        if (item.targetType && item.targetValue) { // 存在的值才放入返回
          targets.push({
            goalTypeId: item.targetType,
            goalTypeName: this.targetMap[item.targetType],
            goalValue: item.targetValue
          })
        }
      })
      return [
        { goalType: '1', streetGoals: targets, taskName: this.formData.taskName, taskDesc: this.formData.taskDesc, taskStartDatetime: this.formData.dateTimeRange[0], taskEndDatetime: this.formData.dateTimeRange[1] },
        validate
      ]
    },
    // 下载明细目标模板文件
    downloadTemplateFile() {
      downloadTemplate({ ...this.outParams }).then(res => {
        const headers = res.headers
        const contentType = headers['Content-Type']
        const blob = new Blob([res.data], { type: contentType })
        const fileName = '街区明细目标模板' + (new Date()).getTime() + '.xls'
        if (window.navigator.msSaveBlob) {
          window.navigator.msSaveBlob(blob, fileName)
        } else {
          const downloadElement = document.createElement('a')
          downloadElement.href = window.URL.createObjectURL(blob)
          downloadElement.download = fileName
          document.body.appendChild(downloadElement)
          downloadElement.click()
          document.body.removeChild(downloadElement)
        }
      })
    },
    handleRemove(file, fileList) {
      console.log(file, fileList, this.fileList)
    },
    handleFileExceed(files, fileList) {
      this.$message.warning(`只能上传一个文件，请先移除文件【${fileList[0].name}】`)
      console.log(1, files, fileList, this.fileList)
    },
    handleFileChange(file, fileList) {
      console.log(2, file, fileList)
      this.fileList = [...fileList]
    },
    // 上传文件
    async uploadTemplate(fileInfo) {
      console.log(3, fileInfo)
      const { file } = fileInfo
      const fd = new FormData()
      fd.append('file', file)
      const res = await uploadTemplate(fd)
      // 如果是 Blob 数据，可以转换为文本或对象
      if (res.data instanceof Blob) {
        const text = await new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result)
          reader.readAsText(res.data)
        })
        console.log(JSON.parse(text)) // 假设是 JSON 数据
        const { code, data, msg } = JSON.parse(text)
        if (code === 200) {
          this.$emit('getGoalListTempId', data || '')
          this.$message.success('文件上传成功')
        } else {
          this.$message.error(msg)
        }
      } else {
        console.log(res.data)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.base-info {
  .formData {
    width: vw(567);
    ::v-deep .el-form-item {
      margin-bottom: vh(20);
    }
    ::v-deep .el-form-item__label {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(16);
      color: #e4eeff;
      padding-bottom: vh(6);
      line-height: vh(22);
    }
    ::v-deep .el-form-item__content {
      line-height: vh(32);
      width: vw(567);
    }
    ::v-deep .el-input__inner {
      line-height: vh(32);
      height: vh(32);
      background-color: #1d334b;
      border-color: #445369;
      color: #e4eeff;
      padding: vh(4) vw(8);
      font-size: vw(14);
    }
    ::v-deep .el-textarea__inner {
      background-color: #1d334b;
      border-color: #445369;
      color: #e4eeff;
      font-size: vw(14);
    }
    ::v-deep .el-date-editor {
      width: vw(567);
      .el-range-separator {
        color: #d9d9d9;
        line-height: vh(24);
      }
      .el-range__icon {
        line-height: vh(24);
      }
      .el-range__close-icon {
        line-height: vh(24);
      }
      .el-range-input {
        background: #1d334b;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        color: #e4eeff;
      }
    }
  }
  .target-handle-type{
    margin-bottom: vh(5);
    ::v-deep .el-radio{
      color: white;
    }
    ::v-deep .el-radio__label{
      font-size: vw(14);
    }
    ::v-deep .el-radio__inner{
      width: vw(14);
      height: vw(14);
    }
  }
  .file-download-upload{
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-top: vh(12);
    ::v-deep .el-button--text{
      margin-right: vw(14);
    }
    ::v-deep .el-upload__tip{
      color: white;
      font-size: vw(14);
      line-height: vw(15);
    }
    ::v-deep .el-upload-list__item{
      font-size: vw(14);
      color: #e3e3e3;
      &:hover{
        color: white;
        background-color: #1D4BA7;
      }
      &:first-child{
        line-height: vw(15);
      }
    }
    ::v-deep .el-upload-list__item-name{
      padding: vw(1) vw(1) vw(1) 0;
      color: #e3e3e3;
    }
    ::v-deep .el-icon-close{
      font-size: vw(14);
      color: white;
      right: vw(1.5);
      top: vw(1);
    }
  }
  .expected-select-box {
    margin-bottom: vh(6);
    display: flex;
    .expected-select {
      width: vw(421);
      font-size: vw(14);
      ::v-deep .el-input--mini .el-input__icon{
        font-size: vw(14);
        line-height: vh(32);
      }
    }
    .expected-input {
      width: vw(100);
      margin: 0 vw(8);
      font-size: vw(14);
    }
  }
  .add-icon {
    ::v-deep .el-button {
      padding: 0;
      width: vw(35);
      height: vh(30);
      color: #e4eeff;
      background: linear-gradient(
            181.96deg,
            #6498ff -38.96%,
            #1d4ba7 39.59%,
            #142d60 98.35%
          )
          padding-box,
        linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%)
          border-box;
      border: 1.5px solid transparent;
      box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
    }
  }
}
</style>
<style lang="scss">
.datePopperClass {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  color: #ffffff;
  th {
    color: #ffffff !important;
    border-color: #677aa5;
  }
  .in-range div {
    background-color: #677aa5 !important;
  }
  .is-left {
    border-color: #677aa5;
  }
}
.el-input__inner {
  line-height: vh(32);
  height: vh(32);
  background-color: #1d334b;
  border-color: #445369;
  color: #e4eeff;
  padding: vh(4) vw(8);
}
.el-time-panel{
  background-color: #1d334b;
  border-color: #3571bb;
}
.el-time-panel__content::after, .el-time-panel__content::before{
  border-bottom: 1px solid #3571bb;
  border-top: 1px solid #3571bb;
}
.el-picker-panel{
  border: 1px solid #3571bb;
}
.el-time-spinner__item.active:not(.disabled){
  color: white;
}
.el-time-spinner__item:hover:not(.disabled):not(.active){
  background-color: #3571bb;
  color: white;
}
.el-input.is-disabled .el-input__inner{
  background-color: #1d334b;
  border-color: #445369;
  color: #e4eeff;
}
.el-picker-panel__footer{
  background: linear-gradient(148.39deg, #113963 38.34%, #1d3052 98.51%);
}
.el-button--default {
  padding: vh(8) vw(16);
  font-family: PingFang SC;
  font-weight: 400;
  font-size: vw(16);
  color: #e4eeff;
  background: linear-gradient(
      181.96deg,
      #6498ff -38.96%,
      #1d4ba7 39.59%,
      #142d60 98.35%
  )
  padding-box,
  linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
  border: vw(2) solid transparent;
  box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
}
</style>
