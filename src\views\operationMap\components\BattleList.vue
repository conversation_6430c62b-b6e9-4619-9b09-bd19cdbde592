<template>
  <div class="battle-list">
    <el-form v-show="false" :inline="true" :model="formInline" class="form-inline">
      <div>
        <el-form-item label="地市">
          <el-select v-model="formInline.user" placeholder="活动区域" popper-class="custom-select" size="mini">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="区县">
          <el-select v-model="formInline.region" placeholder="活动区域" popper-class="custom-select" size="mini">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="网格">
          <el-select v-model="formInline.region" placeholder="活动区域" popper-class="custom-select" size="mini">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>
        </el-form-item>
      </div>
      <div class="search-btn">
        <el-form-item>
          <el-button>查询</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div class="tab-select">
      <el-radio-group v-model="tabSelect" @change="tableTypeChange">
        <el-radio-button label="1">街区</el-radio-button>
        <el-radio-button label="2">区县</el-radio-button>
        <el-radio-button label="3">地市</el-radio-button>
      </el-radio-group>
      <el-radio-group v-show="false" v-model="handleStatus">
        <el-radio border label="1">全部取消</el-radio>
        <el-radio border label="2">全部选中</el-radio>
        <el-radio border label="3">反选</el-radio>
      </el-radio-group>
    </div>
    <div class="battle-table">
      <el-table
        v-if="tabSelect === '1'"
        ref="selectionTableRef"
        :cell-style="cellStyle"
        :data="tableDataForStreet"
        border
        cell-class-name="cellClassName"
        header-cell-class-name="headerCellClassName"
        stripe
        style="width: 100%"
        row-key="streetId"
        @select="handleSelected"
      >
        <el-table-column :key="'selection_' + tabSelect" type="selection" />
        <el-table-column label="城市" prop="cityName" show-overflow-tooltip />
        <el-table-column :key="'countyName_' + tabSelect" label="区县" prop="countyName" show-overflow-tooltip />
        <el-table-column :key="'streetName_' + tabSelect" label="街区名称" prop="streetName" show-overflow-tooltip />
        <el-table-column label="端口数" prop="portNum" show-overflow-tooltip />
        <el-table-column label="区域渗透率" prop="areaPenetrationRatio" show-overflow-tooltip />
        <el-table-column label="异网客户数" prop="ywCustomerNum" show-overflow-tooltip />
        <el-table-column label="图商市占率" prop="bdMarketRatio" show-overflow-tooltip />
      </el-table>
      <el-table
        v-if="tabSelect === '2'"
        :cell-style="cellStyle"
        :data="tableDataForCount"
        border
        cell-class-name="cellClassName"
        header-cell-class-name="headerCellClassName"
        stripe
        style="width: 100%"
        row-key="streetId"
      >
        <el-table-column label="城市" prop="cityName" show-overflow-tooltip />
        <el-table-column :key="'countyName_' + tabSelect" label="区县" prop="countyName" show-overflow-tooltip />
        <el-table-column :key="'streetNum_' + tabSelect" label="街区个数" prop="streetNum" show-overflow-tooltip />
        <el-table-column label="端口数" prop="portNum" show-overflow-tooltip />
        <el-table-column label="区域渗透率" prop="areaPenetrationRatio" show-overflow-tooltip />
        <el-table-column label="异网客户数" prop="ywCustomerNum" show-overflow-tooltip />
        <el-table-column label="图商市占率" prop="bdMarketRatio" show-overflow-tooltip />
      </el-table>
      <el-table
        v-if="tabSelect === '3'"
        :cell-style="cellStyle"
        :data="tableDataForProvince"
        border
        cell-class-name="cellClassName"
        header-cell-class-name="headerCellClassName"
        stripe
        style="width: 100%"
        row-key="streetId"
      >
        <el-table-column label="城市" prop="cityName" show-overflow-tooltip />
        <el-table-column :key="'streetNum_' + tabSelect" label="街区个数" prop="streetNum" show-overflow-tooltip />
        <el-table-column label="端口数" prop="portNum" show-overflow-tooltip />
        <el-table-column label="区域渗透率" prop="areaPenetrationRatio" show-overflow-tooltip />
        <el-table-column label="异网客户数" prop="ywCustomerNum" show-overflow-tooltip />
        <el-table-column label="图商市占率" prop="bdMarketRatio" show-overflow-tooltip />
      </el-table>
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          popper-class="select-dropdown"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { getFightList } from '@/api/operation'
export default {
  name: 'BattleList',
  props: {
    outParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      notSelectedList: [],
      formInline: {
        user: '',
        region: ''
      },
      tabSelect: '1',
      handleStatus: '',
      tableDataForStreet: [],
      tableDataForCount: [],
      tableDataForProvince: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      originType: 'params' // 查询街区表格信息的参数来源 params是界面上直接参数查询进入 ids是下钻到街区地图选择街区id后进入 两种方式 后续保存用于判断发起参数组装
    }
  },
  computed: {

  },
  created() {
  },
  mounted() {
  },
  methods: {
    cellStyle({ row, rowIndex }) {
      return {
        backgroundColor: rowIndex % 2 !== 0 ? 'rgba(36, 63, 93, 1)' : 'rgba(32, 66, 105, 1)'
      }
    },
    tableTypeChange(val) {
      this.pagination.currentPage = 1
      this.tabSelect = val
      this.getTableDataByParams()
      console.log(this.tabSelect)
    },
    getTableDataByParams() {
      const params = { ...this.outParams, queryType: this.tabSelect, pageNum: this.pagination.currentPage, pageSize: this.pagination.pageSize }
      getFightList(params).then(res => {
        if (this.tabSelect === '1') {
          this.tableDataForStreet = [...res.data.records]
        } else if (this.tabSelect === '2') {
          this.tableDataForCount = [...res.data.records]
        } else {
          this.tableDataForProvince = [...res.data.records]
        }
        this.pagination.total = res.data.total
        // 当前页数据渲染后 先默认全部勾选数据 只有街区的列表需要执行
        if (this.tabSelect !== '1') {
          return false
        }
        this.$nextTick(() => {
          this.tableDataForStreet.forEach(row => {
            if (this.notSelectedList.includes(row.streetId)) {
              this.$refs.selectionTableRef.toggleRowSelection(row, false)
            } else {
              this.$refs.selectionTableRef.toggleRowSelection(row, true)
            }
          })
        })
      })
    },
    handleSelected(selection, row) {
      // console.log(selection, row)
      if (this.notSelectedList.includes(row.streetId)) { // 不选列表中存在 当前街道id 表示之前没选 现在选上 需要将其移除
        // this.notSelectedList.remove(row.streetId)
        this.notSelectedList = this.notSelectedList.filter(item => item !== row.streetId)
      } else { // 不存在 表示新加入的不选街道
        this.notSelectedList.push(row.streetId)
      }
    },
    // 处理每页数量变化
    handleSizeChange(pageSize) {
      this.pagination.currentPage = 1
      this.pagination.pageSize = pageSize
      this.getTableDataByParams()
    },
    // 处理页码变化
    handleCurrentChange(currentPage) {
      this.pagination.currentPage = currentPage
      this.getTableDataByParams()
    },
    init(params) {
      this.originType = params.type || 'params'
      this.tabSelect = '1'
      this.getTableDataByParams()
    },
    // 返回子组件操作后的参数信息
    getReturnParams() {
      return [this.notSelectedList, false]
    },
    // 清空关键数据 防止下次打开的污染
    clearData() {
      this.notSelectedList = []
      this.tableDataForStreet = []
      this.tableDataForCount = []
      this.tableDataForProvince = []
      this.tabSelect = '1'
      this.pagination.total = 0
      this.pagination.currentPage = 1
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.battle-list {
  color: #ffffff;
  .form-inline {
    display: flex;
    justify-content: space-between;
    ::v-deep .el-input__inner {
      width: vw(120);
      height: vh(32);
      line-height: vh(32);
      background-color: #141e32;
      border-color: #677aa5;
      border-radius: vw(4);
      font-size: vw(14);
      color: #ffffff !important;
    }
    ::v-deep .el-form-item__label {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(16);
      color: #e4eeff;
      padding-right: vw(6);
    }
    ::v-deep .el-form-item {
      margin-right: vw(20);
    }
    ::v-deep .el-select {
      font-size: vw(14);
    }
  }
  .search-btn {
    ::v-deep .el-button {
      padding: vh(8) vw(35);
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      color: #e4eeff;
      background: linear-gradient(
            181.96deg,
            #6498ff -38.96%,
            #1d4ba7 39.59%,
            #142d60 98.35%
          )
          padding-box,
        linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%)
          border-box;
      border: 1.5px solid transparent;
      box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
    }
  }

  .tab-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ::v-deep .el-radio-button__inner {
      padding: vh(8) vw(20);
      font-family: PingFang SC;
      font-weight: 500;
      font-size: vw(14);
      background: #1d334b;
      border-color: #445369;
      color: #e4eeff;
    }
    ::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: linear-gradient(
        201.75deg,
        #8cb2ff 14.26%,
        rgba(159, 191, 255, 0.6) 85.74%
      );
      color: #212832;
      box-shadow: none;
    }
    ::v-deep .el-radio.is-bordered {
      border: 1px solid #445369;
      padding: 0 vw(20);
      height: vh(32);
      line-height: vh(32);
      font-family: PingFang SC;
      font-weight: 400;
      color: #e4eeff;
      margin-right: vw(16);
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
    ::v-deep .el-radio.is-bordered + .el-radio.is-bordered {
      margin-left: unset;
    }
    ::v-deep .el-radio.is-bordered.is-checked {
      border-color: #8cb2ff;
    }

    ::v-deep .el-radio {
      .el-radio__inner {
        background-color: #445369;
        width: vh(16);
        height: vh(16);
      }
      .el-radio__input {
        width: vh(16);
        height: vh(16);
      }
      .el-radio__label {
        font-size: vw(14);
        font-family: PingFang SC;
        font-weight: 400;
        padding-left: vw(8);
      }
      .el-radio__input.is-checked .el-radio__inner {
        border-color: #8cb2ff;
      }
      .el-radio__inner::after {
        background-color: #8cb2ff;
        width: vh(8);
        height: vh(8);
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #e4eeff;
      }
    }
  }

  .battle-table {
    margin-top: vh(10);

    ::v-deep .el-table--border,
    .el-table--group {
      border-radius: 4px;
      border-color: #405d8b;
    }
    ::v-deep .el-table--border::after {
      background-color: #405d8b;
    }
    ::v-deep .el-table::before {
      background-color: #405d8b;
    }
    ::v-deep .el-table {
      .headerCellClassName {
        background-color: #265282 !important;
        color: rgba(228, 238, 255, 1) !important;
        border-color: #405d8b;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        padding: vh(12) 0 !important;
      }
      .cellClassName {
        color: rgba(228, 238, 255, 1) !important;
        border-color: #405d8b;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
      }

      .cell {
        padding-left: vw(16);
      }
      .el-table__cell {
        padding: vh(12) 0;
      }
    }

    ::v-deep .el-checkbox__label {
      color: #ffffff;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(16);
    }
    ::v-deep .el-checkbox__inner {
      border: 1.5px solid #8cb2ff;
      background-color: transparent;
      width: 12px;
      height: 12px;
    }

    ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
      background-color: transparent;
    }
    ::v-deep .el-checkbox__inner::after {
      border-color: #28436a;
      top: -2px;
      height: 10px;
    }
    ::v-deep .el-checkbox__inner::before {
      top: 4px;
    }
    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #8cb2ff;
    }

    .pagination-container {
      margin-top: vh(16);
      text-align: right;

      ::v-deep .el-input__inner {
        background: transparent;
        border-color: #adb0bd;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        color: #e4eeff;
      }

      ::v-deep .el-pagination__total {
        float: left;
        color: #8cb2ff;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
      }
      ::v-deep .el-pagination__jump {
        color: #adb0bd;
      }
      ::v-deep .el-pagination__editor.el-input {
        width: vw(48);
      }

      ::v-deep .btn-prev {
        background-color: transparent;
        color: #adb0bd;
      }
      ::v-deep .btn-next {
        background-color: transparent;
        color: #adb0bd;
      }
      ::v-deep .el-pager li {
        background-color: transparent;
        border: 1px solid #adb0bd;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        text-align: center;
        color: #e4eeff;

        &.active {
          background-color: #8cb2ff;
          border-color: #8cb2ff;
          color: #ffffff;
        }
      }
      ::v-deep .el-pagination__jump {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(14);
        line-height: vh(22);
        color: rgba(173, 176, 189, 1);
      }
    }
  }
}
</style>
<style lang="scss">
.custom-select {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #141e32 !important;
    }
  }
  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    height: vh(34);
    line-height: vh(34);
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}
.select-dropdown {
  background-color: #141e32 !important;
  border-color: #677aa5 !important;
  .popper__arrow {
    border-bottom-color: #677aa5 !important;
    &::after {
      border-bottom-color: #141e32 !important;
    }
  }
  .el-select-dropdown__wrap {
    margin-right: -16px !important;
  }
  .el-select-dropdown__item {
    color: #ccd3d9;
    font-size: vw(14) !important;
    height: vh(34);
    line-height: vh(34);
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #147bd119 !important;
    color: #4bb6ff;
  }
}
</style>
<style>
.el-table__empty-block{
  background-color: #113963 !important;
}
.el-table__empty-text{
  color: white;
}
</style>
