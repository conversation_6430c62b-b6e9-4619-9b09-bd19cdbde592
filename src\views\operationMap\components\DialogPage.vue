<template>
  <el-dialog :width="(width / 1920 * 100) + 'vw'" :append-to-body="true" :close-on-click-modal="false" :title="title" :visible.sync="dialogVisible" class="dialog-page" :before-close="closeDialog" @close="close">
    <slot />
    <template #footer>
      <slot name="footer" />
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: 'DialogPage',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 1142
    }
  },
  data() {
    return {
      dialogVisible: this.visible
    }
  },
  computed: {
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    cancle() {
      this.dialogVisible = false
      this.$emit('update:visible', false)
    },
    close() {
      this.$emit('close')
    },
    closeDialog(done) {
      this.$emit('before-close', done)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.dialog-page {
  ::v-deep .el-dialog {
    //width: vw(1142);
    background: linear-gradient(148.39deg, #113963 38.34%, #1d3052 98.51%);
    border: 1px solid #40525e;
    box-shadow: 0px 12px 32px 0px #00000024;
    border-radius: 8px;
    margin-top: vh(63) !important;
  }
  ::v-deep .el-dialog__header {
    padding: vh(24) vw(32) vh(8);
    > span {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
    }
  }
  ::v-deep .el-dialog__body {
    padding: vh(16) vw(32) vh(28);
  }

  ::v-deep .el-dialog__footer {
    text-align: center;
    padding-top: vh(8);
    padding-bottom: vh(24);
  }

  ::v-deep .el-select{
    display: flex;
  }
}
</style>
