<template>
  <div class="echartsMap">
    <div v-show="currentArea.areaLevel <= 2" id="echart-map-wrap" />
    <div v-show="currentArea.areaLevel > 2" id="baidu-map-wrap" class="map-wrap" />
    <el-drawer
      :append-to-body="false"
      :show-close="true"
      :visible.sync="drawer"
      :with-header="false"
      :modal="false"
      :wrapper-closable="false"
      :modal-append-to-body="false"
      custom-class="custom-drawer"
      direction="rtl"
      size="100%"
    >
      <div class="drawer-content">
        <TitlePart class="title-part" title="街区选择" />
        <div class="drawer-list">
          <el-checkbox-group v-model="checkList">
            <div v-for="(item, index) in streetList" :key="index" class="list-item">
              <el-checkbox :label="item.streetId" @change="streetCheckChange">{{ item.streetName }}</el-checkbox>
            </div>
          </el-checkbox-group>
          <div class="button-group">
            <div class="confirm-btn" @click="startFight">发起攻坚</div>
            <div class="search-btn" @click="drawer=false">取消</div>
          </div>
        </div>
        <div class="drawer-close">
          <i class="el-icon-close" @click="drawer=false" />
        </div>
      </div>
    </el-drawer>
    <!-- <div v-if="currentArea.areaLevel != 1" class="back-btn map-back-css'" @click="backParentLevel">
      <div class="icon"></div>
      <div>返回</div>
    </div> -->
  </div>
</template>

<script>
import Vue from 'vue'
// import mapNumJson from '../mapNum.json'
import { fitChartSize } from '@/utils/dataUtil.js'
import * as echarts from 'echarts'
import { getStreetNumInArea, getStreetInfoByCounty } from '@/api/operation'
import mapJson from '@/views/operation/mapConfig.json'
import TitlePart from '@/views/operation/components/TitlePart.vue'
export default {
  name: 'EchartsMap',
  components: { TitlePart },
  props: {
    filterList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      mapChart: null,
      map: null,
      mapJson: {}, // 地图边界值
      mapBorderJson: {}, // 地图白色边框以及地图底层边界值
      mapData: {}, // 地图悬浮数据
      geoCoordMap: {}, // 悬浮数据中心点
      currentArea: {},
      labelName: [],
      PolygonCenter: [],
      extend: [],
      polygonPaths: [],
      center: {},
      zoom: 15,
      polygonOverlays: [],
      drawer: false,
      checkList: [],
      streetList: [],
      streetId: ''
    }
  },

  async mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.mapChart) {
      this.mapChart.dispose()
    }
  },

  methods: {
    // 关闭窗口 然后打开攻坚窗口
    startFight() {
      this.drawer = false
      this.$emit('start-fight', this.checkList)
    },
    // 初始化echarts数据
    async initMapData(data) {
      this.currentArea = { areaId: data.areaId, areaLevel: parseInt(data.areaLevel) }
      // 不需要阴影
      this.mapBorderJson = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            properties: {
              name: data.areaName
            },
            geometry: {
              type: 'MultiPolygon',
              coordinates: this.polygonPath(data.boundary)
            }
          }
        ]
      }
      this.mapJson = {
        type: 'FeatureCollection',
        features: this.polygonFullPath(data.children)
      }
      this.geoCoordMap = this.getGeoCoordMap(data.children)
      this.mapData = data.children.map(item => {
        return {
          name: item.areaName,
          areaId: item.areaId,
          areaLevel: item.areaLevel,
          centralPoint: item.centralPoint,
          value: 0
        }
      })
      await this.getStreetNum()
    },
    // 获取页面上查询参数
    getSearchParams() {
      const params = { ...this.currentArea }
      this.filterList.forEach(item => {
        switch (item.label) {
          case '端口数(个)':
            params.minPort = item.firstInput
            params.maxPort = item.secondInput
            params.isAllPort = item.checked ? '1' : '0'
            break
          case '区域渗透率(%)':
            params.minAreaRate = item.firstInput
            params.maxAreaRate = item.secondInput
            params.isAllAreaRate = item.checked ? '1' : '0'
            break
          case '区域商客渗透率(%)':
            params.minBusiRate = item.firstInput
            params.maxBusiRate = item.secondInput
            params.isAllBusiRate = item.checked ? '1' : '0'
            break
          case '异网客户数(个)':
            params.minDiffNet = item.firstInput
            params.maxDiffNet = item.secondInput
            params.isAllDiffNet = item.checked ? '1' : '0'
            break
          case '图商市占率(%)':
            params.minMapBusiRate = item.firstInput
            params.maxMapBusiRate = item.secondInput
            params.isAllMapBusiRate = item.checked ? '1' : '0'
            break
        }
      })
      return params
    },
    // echarts图获取街道数量 并插入到地图数据中做更新
    async getStreetNum() {
      const params = this.getSearchParams()
      console.log(params)
      await getStreetNumInArea(params).then(({ code, data, msg }) => {
        if (code === 200) {
          this.mapData.forEach(item => {
            const obj = data.find(el => el.areaId === item.areaId)
            if (obj) {
              item.value = obj.brodbandRatio !== undefined 
                                  ? Number((obj.brodbandRatio * 100).toFixed(2)) 
                                  : 0; // 处理obj.brodbandRatio可能为undefined的情况
              item.streetNum = obj.streetNum
            } else {
              item.value = 0
            }
          })
        } else {
          this.$message.error(msg)
        }
        this.$nextTick(() => {
          this.initMapEcharts(this.mapData)
        })
      })
    },
    // 切换到百度地图初始化
    initBaiduMap(data) {
      const [lng, lat] = data.centralPoint.split(',')
      if (!this.map) {
        this.map = new BMap.Map('baidu-map-wrap', { enableMapClick: false })
        const center = new BMap.Point(lng, lat)
        // this.map.setMapStyle({ styleJson: mapJson })
        this.map.centerAndZoom(center, this.zoom)
        this.map.enableScrollWheelZoom(true)
      }
    },
    // 初始化百度地图中心及图层渲染
    initBMapData(data) {
      this.currentArea = { areaId: data.areaId, areaLevel: parseInt(data.areaLevel) }
      if (this.map != null) {
        this.map.clearOverlays()
      } else {
        this.$nextTick(() => {
          this.initBaiduMap(data)
        })
      }
      this.$nextTick(() => {
        this.polygonPaths = []
        this.extend = []
        this.PolygonCenter = []
        this.labelName = []
        const [lng, lat] = data.streetList.length > 0 ? data.streetList[0].centerPoint.split(',') : data.centralPoint.split(',')
        this.center = { lng, lat }
        if (data.streetList) {
          this.streetList = []
          data.streetList.forEach(item => {
            this.streetList.push({ streetName: item.streetName, streetId: item.streetId })
            if (item.relAreaList) {
              item.relAreaList.forEach(item2 => {
                if (item2 && item2.boundary) {
                  const tr = item2.boundary.split(';').map(function(val, ind) {
                    val = val.split(',')
                    return {
                      lng: (val[0] || '').trim(),
                      lat: (val[1] || '').trim()
                    }
                  })
                  this.labelName.push(item.streetName)
                  this.polygonPaths.push({ tr, item })
                  this.extend.push(item2)
                  if (item2.centerPoint) {
                    const [lng, lat] = item2.centerPoint.split(',')
                    this.PolygonCenter.push({ lng, lat })
                  }
                }
              })
            }
          })
        }
        this.newDrawPolygons()
        const center = new BMap.Point(this.center.lng, this.center.lat)
        this.map.centerAndZoom(center, this.zoom)
      })
      // try {
      //   getStreetInfoByCounty(params).then(({ code, data }) => {
      //
      //   })
      // } catch (error) {
      //   console.log(error)
      //   this.$message.error('加载位置信息失败，请重试')
      // }
    },
    /**
     * @description 绘制标签
     * @param
     * <AUTHOR>
     * @date 2025-07-30
     */
    drawLabels() {
      this.PolygonCenter.forEach((position, index) => {
        // const label = new BMap.Label(this.labelName[index], { position })
        // label.setStyle({
        //   fontSize: '13px',
        //   fontWeight: '400',
        //   fontFamily: 'PingFang SC',
        //   border: 'none',
        //   background: 'none',
        //   color: '#0d2269',
        //   pointerEvents: 'none'
        // })
        // this.map.addOverlay(label)
        const textData = []
        textData.push({
          geometry: {
            type: 'Point',
            coordinates: [position.lng, position.lat]
          },
          text: this.labelName[index]
        })
        const textDataSet = new mapv.DataSet(textData)
        // eslint-disable-next-line new-cap
        const label = new mapv.baiduMapLayer(this.map, textDataSet, {
          draw: 'text',
          font: '14px Arial',
          fillStyle: 'black',
          shadowColor: 'black',
          shadowBlue: 0,
          zIndex: 101,
          shadowBlur: 0
        })
        this.polygonOverlays.push([label, 'label'])
      })
    },
    getColorBySize(num) {
      if (num < 50 || num === 50) {
        return '#FF393980'
      } else if (num > 50 && (num < 100 || num === 100)) {
        return '#FF838380'
      } else if (num > 100 && num < 200) {
        return '#37D89280'
      } else {
        return '#A9FFDA80'
      }
    },
    /**
     * @description 绘制多边形
     * @param
     * <AUTHOR>
     * @date 2025-07-30
     */
    drawPolygons() {
      this.clearPolygons()
      console.log(this.polygonPaths)
      this.polygonPaths.forEach(({ item, tr }) => {
        const points = tr.map(coord => new BMap.Point(coord.lng, coord.lat))
        const polygon = new BMap.Polygon(points, {
          fillColor: 'rgba(169, 255, 218)',
          fillOpacity: 0.5,
          strokeOpacity: 1,
          strokeWeight: 1,
          strokeColor: 'rgba(169, 255, 218)',
          strokeStyle: 'none'
        })
        polygon.addEventListener('click', () => {
          this.drawer = true
          if (!this.checkList.includes(item.streetId)) {
            this.checkList.push(item.streetId)
          }
        })
        this.map.addOverlay(polygon)
        this.polygonOverlays.push(polygon)
      })
    },
    streetCheckChange() {
      this.newDrawPolygons()
    },
    newDrawPolygons() {
      // this.map.clearOverlays()
      this.clearPolygons()
      const _that = this
      const polygons = []
      this.polygonPaths.forEach(({ item, tr }) => {
        const trs = tr.map(coord => {
          return [coord.lng, coord.lat]
        })
        const fillStyle = this.checkList.includes(item.streetId) ? 'rgba(220,13,13,0.5)' : this.streetId === item.streetId ? 'rgba(5,86,168,0.5)' : 'rgba(213,159,18,0.5)'
        polygons.push({
          geometry: {
            type: 'Polygon',
            coordinates: [trs]
          },
          fillStyle: fillStyle,
          properties: {
            data: item
          }
        })
      })
      const options = {
        draw: 'simple',
        strokeStyle: 'rgb(192,159,24)',
        lineWidth: 1,
        zIndex: 100,
        methods: {
          click: function(e) {
            console.log(e)
            _that.map.clearOverlays()
            if (e?.properties?.data) {
              _that.drawer = true
              if (!_that.checkList.includes(e.properties.data.streetId)) {
                _that.checkList.push(e.properties.data.streetId)
                _that.newDrawPolygons()
              } else { // 存在就移除
                const arr = []
                _that.checkList.forEach(item => {
                  if (item !== e.properties.data.streetId) {
                    arr.push(item)
                  }
                })
                _that.checkList = [...arr]
                console.log(_that.checkList)
                _that.newDrawPolygons()
              }
            }
          }
        }
      }
      const dataSet = new mapv.DataSet(polygons)
      // eslint-disable-next-line new-cap
      const polygn = new mapv.baiduMapLayer(this.map, dataSet, options)
      this.polygonOverlays.push([polygn, 'layer'])
      this.drawLabels()
    },
    clearPolygons() {
      this.polygonOverlays.forEach(polygon => {
        // if (polygon[1] === 'layer') {
        //   polygon[0].off('click')
        // }
        polygon[0].destroy()
      })
      this.polygonOverlays = [] // 清空数组
    },
    /**
     * @description 格式化白色边框边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonPath(data) {
      const points = data.split(';')
      const polygon = points.map(item => {
        const [lon, lat] = item.split(',')
        return [parseFloat(lon.trim()), parseFloat(lat.trim())]
      })
      return [[polygon]]
    },
    /**
     * @description 格式化边界值
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    polygonFullPath(data) {
      return data.map(item => {
        return {
          type: 'Feature',
          properties: {
            name: item.areaName
          },
          geometry: {
            type: 'MultiPolygon',
            coordinates: this.polygonPath(item.boundary)
          }
        }
      })
    },
    /**
     * @description 获取悬浮中心点
     * @param
     * <AUTHOR>
     * @date 2025-01-21
     */
    getGeoCoordMap(arr) {
      const result = {}
      arr.forEach(item => {
        const [longitude, latitude] = item.centralPoint.split(',').map(parseFloat)
        result[`${item.areaName}`] = [longitude, latitude]
      })
      return result
    },
    // scatterData2() {
    //   return datas.map((item) => ({
    //     name: item.name,
    //     brodbandRatio: item.brodbandRatio,
    //     value: this.geoCoordMap[item.name]
    //   }))
    // },
    initMapEcharts(datas) {
      if (this.mapChart) {
        this.mapChart.dispose()
      }
      this.mapChart = echarts.init(document.getElementById('echart-map-wrap'))
      echarts.registerMap('map', this.mapJson)
      echarts.registerMap('mapBorder', this.mapBorderJson)
      this.mapChart.off('click')
      const option = this.getOption(datas)
      this.mapChart.setOption(option)
      this.mapChart.on('click', (params) => {
        // if (params.data.value <= 0) return
        console.log(params.data)
        this.currentArea = params.data
        if (params.data.areaLevel <= 2) {
          // this.getMapInfo(params.data)
          this.$emit('map-init', params.data)
        } else {
          this.$emit('b-map-init', params.data)
          // this.getLocation()
          this.$nextTick(() => {
            this.initBaiduMap(params.data)
          })
        }
      })
    },
    getOption(datas) {
      var that = this
      function scatterData2() {
        return datas.map((item) => ({
          name: item.name,
          areaId: item.areaId,
          value: that.geoCoordMap[item.name]
        }))
      }
      const rangeArr = parseInt(this.currentArea.areaLevel) <3 ? [25, 50, 75] : [5, 10, 20]
      console.log(rangeArr)
      return {
        geo: [
          // 第一层地图
          {
            map: 'map',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              borderColor: '#D8E6FF4D',
              borderWidth: 1
            },
            emphasis: {
              itemStyle: {
                areaColor: 'transparent'
              },
              label: {
                show: 0,
                color: '#fff'
              }
            },
            zlevel: 3
          },
          // 第二层地图和第一层重叠用作边框
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#F7F8FF',
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.25)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: fitChartSize(3.72)
            },
            zlevel: 2,
            silent: true
          },
          // 第三层地图底层
          // {
          //   map: 'mapBorder',
          //   aspectScale: 1,
          //   layoutSize: '96%',
          //   layoutCenter: ['50%', '52%'],
          //   itemStyle: {
          //     areaColor: 'transparent',
          //     borderColor: '#B6C1FB99',
          //     borderWidth: 1
          //   },
          //   zlevel: 1,
          //   silent: true
          // }
        ],
        visualMap: {
          show: false,
          type: 'piecewise',
          bottom: fitChartSize(40),
          right: fitChartSize(40),
          itemGap: fitChartSize(10),
          align: 'left',
          itemWidth: fitChartSize(16),
          itemHeight: fitChartSize(12),
          textStyle: {
            fontSize: fitChartSize(14),
            color: '#EDEDED'
          },
          seriesIndex: 0,
          pieces: [
            {
              gt: rangeArr[2],
              // lte: 10000,
              color: '#A9FFDA80',
              label: '≥200'
            },
            {
              gt: rangeArr[1],
              lte: rangeArr[2],
              color: '#37D89280',
              label: '100-200'
            },
            {
              gt: rangeArr[0],
              lte: rangeArr[1],
              color: '#FF838380',
              label: '50-100'
            },
            {
              gt: 0,
              lte: rangeArr[0],
              color: '#FF393980',
              label: '≤50'
            }
          ]
        },
        series: [
          {
            type: 'map',
            selectedMode: false,
            map: 'map',
            geoIndex: 0,
            data: datas
          },
          // 地市弹框撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: '{b}',
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#fff',
              fontSize: fitChartSize(14),
              fontWeight: '500',
              fontFamily: '',
              show: true
            },
            symbol: 'rect',
            // symbolSize: function(data, params) {
            //   const name = params.data.name
            //   const nameLength = name.length || 0
            //   const width = fitChartSize(nameLength * 20)
            //   const height = fitChartSize(30)
            //   return [width, height]
            // },
            symbolOffset: [0, fitChartSize(-30)],
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              borderWidth: fitChartSize(0.5),
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
          // 地市对应值撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: function(name) {
                const item = datas.find(item => item.name === name.name)
                return item ? `${item.value}% [${item.streetNum}] ` : ''
                // return [
                //   `{line1|${item.value}%}`,
                //   `{line2|${item.streetNum}}`
                // ].join('\n'); // 使用\n换行，配合rich配置
              },
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#ffffff',
              fontSize: fitChartSize(14),
              fontFamily: 'Milibus',
              show: true,
              // rich: {
              //   line1: {
              //     color: '#ffffff', // 第一行颜色
              //     fontSize: fitChartSize(23),
              //     fontFamily: 'Milibus',
              //     padding: [10, 0, 5, 0] // 第一行底部间距
              //   },
              //   line2: {
              //     color: '#ffffff', // 第二行颜色（可不同）
              //     fontSize: fitChartSize(23),
              //     fontFamily: 'Milibus'
              //   }
              // }
            },
            symbol: 'rect',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          }
        ]
      }
    },
    handleResize() {
      if (this.mapChart) {
        this.mapChart.resize()
        this.$nextTick(() => {
          const updatedOption = this.getOption(this.mapData)
          this.mapChart.setOption(updatedOption)
        })
      }
    },
    toBMapCenter(centerPoint, streetId) {
      this.streetId = streetId
      this.newDrawPolygons()
      this.$nextTick(() => {
        const center = new BMap.Point(centerPoint.lng, centerPoint.lat)
        this.map.centerAndZoom(center, this.zoom)
      })
    },
    // 根据当前级别和信息返回上级
    backParentLevel() {
      if (this.currentArea.areaLevel === 1) { // 最上级了
        this.$message.warning('当前已是省级地图')
        return false
      } else if (this.currentArea.areaLevel === 2) { // 市州级
        this.$emit('back-last-level', 2)
      } else if (this.currentArea.areaLevel === 3) { // 区县级
        this.$emit('back-last-level', 3)
      } else if (this.currentArea.areaLevel === 5) { // 区域显示级别（可以定位到图层）
        this.$emit('back-last-level', 3)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.echartsMap {
  .initBaiduMap {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    font-weight: bold;
  }
  #echart-map-wrap {
    margin-top: vh(52);
    margin-left: vw(315);
    width: vw(865);
    height: vh(813);
    background-color: transparent !important;
  }
  #baidu-map-wrap {
    background-color: transparent !important;
  }
  .map-wrap {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  .back-btn{
    position: absolute;
    top: vh(35);
    left: vw(390);
    display: flex;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(16);
    background-color: rgba(168, 216, 255, 1);
    background-clip: text;
    color: transparent;
    cursor: pointer;
    padding: vh(10) vw(20);
    .icon {
      width: vw(24);
      height: vh(24);
      background: url("../../../assets/images/market/icon.png") no-repeat;
      background-size: 100% 100%;
      margin-right: vw(6);
    }
  }
}
::v-deep .el-drawer__wrapper{
  width: 27%;
  height: 100%;
  right: 0;
  top: 0;
  left: inherit;
  bottom: inherit;
}
::v-deep .el-drawer__body{
  overflow: hidden;
}
</style>
<style lang="scss">
@import "~@/views/operation/index.scss";
.custom-drawer {
  background: linear-gradient(270deg, #212739 0%, #252c41 100%);
  opacity: 0.9;
  width: 27%;
}
.drawer-content {
  position: relative;
  padding: vh(27) vw(70) vh(41);
}
.title-part {
  margin: vh(30) 0 !important;
}
.list-item {
  margin-bottom: vh(28);
  padding: vh(14) vw(20);
  background: #345596;
  border-radius: 4px;
  .el-checkbox {
    display: inline-flex;
    align-items: center;
  }
  .el-checkbox__label {
    color: #e4eeff;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(16);
  }
  .el-checkbox__inner {
    border: 2px solid #74a2ff;
    background-color: transparent;
    width: vh(16);
    height: vh(16);
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #e4eeff;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(16);
  }

  .el-checkbox__input.is-focus .el-checkbox__inner {
    background-color: #345596;
  }
  .el-checkbox__inner::after {
    border-color: #ffffff;
    top: -2px;
    height: 10px;
  }
  .el-checkbox__inner::before {
    top: 4px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #253a65;
  }
}
.button-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .confirm-btn {
    width: vw(178);
    border-radius: 4px;
    padding: vh(10) vw(20);
    position: relative;
    background: linear-gradient(
          181.96deg,
          #6498ff -38.96%,
          #1d4ba7 39.59%,
          #142d60 98.35%
        )
        padding-box,
      linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
    border: 2px solid transparent;
    color: #ffffff;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: vw(16);
    text-align: center;
    cursor: pointer;
  }
  .search-btn {
    width: vw(178);
    border-radius: 4px;
    padding: vh(10) vw(20);
    position: relative;
    background: linear-gradient(
          181.96deg,
          #6498ff -38.96%,
          #1d4ba7 39.59%,
          #142d60 98.35%
        )
        padding-box,
      linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
    border: 2px solid transparent;
    color: #ffffff;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: vw(16);
    text-align: center;
    cursor: pointer;
  }
}
.drawer-list {
  .el-checkbox-group {
    flex: 1;
    overflow: auto;
  }
  height: vh(905);
  display: flex;
  flex-direction: column;
}
.drawer-close {
  .el-icon-close {
    position: absolute;
    top: 18px;
    right: 23px;
  }
}
</style>
