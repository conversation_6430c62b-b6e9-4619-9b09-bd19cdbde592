<template>
  <div class="battle-table">
    <el-table
      ref="multipleTable"
      :cell-style="cellStyle"
      :data="tableData"
      border
      cell-class-name="cellClassName"
      class="table-container"
      header-cell-class-name="headerCellClassName"
      stripe
      style="width: 100%"
    >
      <el-table-column label="区域" prop="areaName" show-overflow-tooltip />
      <el-table-column label="街区数量" prop="streetNum" show-overflow-tooltip />
      <el-table-column label="分派角色" prop="number">
        <template slot-scope="scope">
          <el-select v-model="scope.row.dispatchRole" placeholder="请选择角色" popper-class="custom-select" size="mini" @change="dispatchRoleChange(scope)">
            <el-option v-for="(roleOpt, i) in roleOpts" :key="'dispatch_role_' + scope.$index + '_option_' + i" :label="roleOpt.label" :value="roleOpt.value"/>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="人员" prop="number">
        <template slot-scope="scope">
          <el-select v-model="scope.row.dispatchPerson" filterable remote placeholder="请搜索人员" popper-class="custom-select" size="mini" :remote-method="(query) => { getRemotePersons(scope, query) }" @change="dispatchPersonChange(scope)">
            <el-option v-for="(personOpt, i) in scope.row.personOpts" :key="'dispatch_person_' + scope.$index + '_option_' + i" :label="personOpt.label" :value="personOpt.value"/>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="手机号" prop="userPhone" show-overflow-tooltip />
    </el-table>
<!--    <div class="pagination-container">-->
<!--      <el-pagination-->
<!--        :current-page="pagination.currentPage"-->
<!--        :page-size="pagination.pageSize"-->
<!--        :page-sizes="[10, 20, 50, 100]"-->
<!--        :total="pagination.total"-->
<!--        background-->
<!--        layout="total, sizes, prev, pager, next, jumper"-->
<!--        popper-class="select-dropdown"-->
<!--        @current-change="handleCurrentChange"-->
<!--        @size-change="handleSizeChange"-->
<!--      />-->
<!--    </div>-->
  </div>
</template>
<script>
import { getDispatchRoles, getDispatchList, getDispatchPersons } from '@/api/operation'

export default {
  name: 'WorkDistributed',
  props: {
    outParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      roleOpts: [],
      roleMap: {}
      // pagination: {
      //   currentPage: 1,
      //   pageSize: 10,
      //   total: 20
      // },
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
    this.getRoleList()
    // this.getTableData() // 不分页
  },
  methods: {
    cellStyle({ row, rowIndex }) {
      return {
        backgroundColor: rowIndex % 2 !== 0 ? 'rgba(36, 63, 93, 1)' : 'rgba(32, 66, 105, 1)'
      }
    },
    getTableData(arr) {
      getDispatchList({ ...this.outParams, exclusionList: arr }).then(res => {
        this.tableData = res.data.map(item => {
          item.areaId = item.countyCode || item.cityCode
          item.areaName = item.countyName || item.cityName
          item.dispatchRole = ''
          item.personOpts = []
          item.dispatchPerson = ''
          item.userPhone = ''
          item.personMap = {}
          return item
        })
      })
    },
    getRoleList() {
      getDispatchRoles().then(res => {
        this.roleOpts = res.data.map(item => {
          this.roleMap[item.roleId] = item.roleName
          return { label: item.roleName, value: item.roleId }
        })
      })
    },
    async getRemotePersons(scope, query) {
      const res = await getDispatchPersons({ roleId: scope.row.dispatchRole, keyWord: query })
      const perMap = {}
      scope.row.personOpts = res.data?.map(item => {
        perMap[item.loginName] = { userName: item.userName, userPhone: item.phoneNum }
        return { label: item.userName, value: item.loginName }
      })
      scope.row.personMap = { ...perMap }
    },
    async dispatchRoleChange(scope) {
      console.log(scope)
      // 需要查询当前选中角色下 用户用于用户的选择
      const res = await getDispatchPersons({ roleId: scope.row.dispatchRole })
      const perMap = {}
      scope.row.personOpts = res.data?.map(item => {
        perMap[item.loginName] = { userName: item.userName, userPhone: item.phoneNum }
        return { label: item.userName, value: item.loginName }
      })
      scope.row.personMap = { ...perMap }
      scope.row.dispatchPerson = ''
      scope.row.userPhone = ''
    },
    // 用户选择信息改变
    dispatchPersonChange(scope) {
      console.log(scope.row.personMap)
      scope.row.userPhone = scope.row.personMap[scope.row.dispatchPerson].userPhone || '--'
    },
    clearData() {
      this.tableData = []
    },
    getReturnParams() {
      // 返回数据拼装 同时做好校验
      const returnList = []
      const emptyDatas = []
      let validate = false
      this.tableData.forEach((item, index) => {
        if (item.dispatchRole && item.dispatchPerson) { // 表示都选择了
          returnList.push({
            areaLevel: item.areaLevel,
            areaId: item.areaId,
            areaName: item.areaName,
            roleId: item.dispatchRole,
            roleName: this.roleMap[item.dispatchRole],
            userId: item.dispatchPerson,
            userName: item.personMap[item.dispatchPerson].userName,
            userPhone: item.userPhone
          })
        } else {
          emptyDatas.push(index + 1)
          validate = true
        }
      })
      if (validate) {
        this.$message.warning(`工单派发信息不完整，列表第${emptyDatas.join(',')}行，请重新填写！`)
      }
      return [{ taskProcess: returnList }, validate]
    }
    // 处理每页数量变化
    // handleSizeChange(pageSize) {
    //   this.pagination.pageSize = pageSize
    //   this.getCommunity2HighdateInfo()
    // },
    // // 处理页码变化
    // handleCurrentChange(currentPage) {
    //   this.pagination.currentPage = currentPage
    //   this.getCommunity2HighdateInfo()
    // }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.battle-table {
  ::v-deep .el-table--border,
  .el-table--group {
    border-radius: 4px;
    border-color: #405d8b;
  }
  ::v-deep .el-table--border::after {
    background-color: #405d8b;
  }
  ::v-deep .el-table::before {
    background-color: #405d8b;
  }
  ::v-deep .el-table {
    .headerCellClassName {
      background-color: #265282 !important;
      color: rgba(228, 238, 255, 1) !important;
      border-color: #405d8b;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      padding: vh(12) 0 !important;
    }
    .cellClassName {
      color: rgba(228, 238, 255, 1) !important;
      border-color: #405d8b;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
    }

    .cell {
      padding-left: vw(16);
    }
    .el-table__cell {
      padding: vh(12) 0;
    }
  }

  ::v-deep .el-checkbox__label {
    color: #ffffff;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(16);
  }
  ::v-deep .el-checkbox__inner {
    border: 1.5px solid #8cb2ff;
    background-color: transparent;
    width: 12px;
    height: 12px;
  }

  ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner {
    background-color: transparent;
  }
  ::v-deep .el-checkbox__inner::after {
    border-color: #28436a;
    top: -2px;
    height: 10px;
  }
  ::v-deep .el-checkbox__inner::before {
    top: 4px;
  }
  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #8cb2ff;
  }
  .table-container {
    ::v-deep .el-input__inner {
      background: transparent;
      border: transparent;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      color: #e4eeff;
    }
  }

  .pagination-container {
    margin-top: vh(16);
    text-align: right;

    ::v-deep .el-input__inner {
      background: transparent;
      border-color: #adb0bd;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      color: #e4eeff;
    }

    ::v-deep .el-pagination__total {
      float: left;
      color: #8cb2ff;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
    }
    ::v-deep .el-pagination__jump {
      color: #adb0bd;
    }
    ::v-deep .el-pagination__editor.el-input {
      width: vw(48);
    }

    ::v-deep .btn-prev {
      background-color: transparent;
      color: #adb0bd;
    }
    ::v-deep .btn-next {
      background-color: transparent;
      color: #adb0bd;
    }
    ::v-deep .el-pager li {
      background-color: transparent;
      border: 1px solid #adb0bd;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      text-align: center;
      color: #e4eeff;

      &.active {
        background-color: #8cb2ff;
        border-color: #8cb2ff;
        color: #ffffff;
      }
    }
    ::v-deep .el-pagination__jump {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: vw(14);
      line-height: vh(22);
      color: rgba(173, 176, 189, 1);
    }
  }
}
</style>
