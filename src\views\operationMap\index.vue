<template>
  <div class="operation-map">
    <div class="operation-tabs">
      <div
        v-for="item in menu"
        :key="item.id"
        :class="{'operation-tabs-active': item.id === menuActive}"
        class="operation-tabs-item"
        @click="navClick(item)"
      >{{ item.name }}</div>
    </div>
    <AreaFight v-if="menuActive === 1" />
    <ProductFight v-if="menuActive === 2" />
    <IndustryFight v-if="menuActive === 3" />
  </div>
</template>
<script>
import AreaFight from './components/AreaFight.vue'
import IndustryFight from './components/IndustryFight.vue'
import ProductFight from './components/ProductFight.vue'
export default {
  name: 'OperationMap',
  components: { AreaFight, ProductFight, IndustryFight },
  data() {
    return {
      menuActive: 1,
      menu: [
        {
          id: 1,
          name: '区域作战'
        },
        // {
        //   id: 2,
        //   name: '产品作战'
        // },
        // {
        //   id: 3,
        //   name: '行业作战'
        // }
      ]
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
    navClick(item) {
      if (item.id === this.menuActive) return
      this.menuActive = item.id
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.operation-map {

  .operation-tabs {
    position: absolute;
    top: vh(30);
    left: vw(30);
    display: flex;
    z-index: 999;
    .operation-tabs-item {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      line-height: vh(32);
      padding: vh(4.5) vw(20);
      color: #8ac0ff;
      cursor: pointer;
      background: linear-gradient(
        180deg,
        rgba(15, 27, 52, 0.5) 0%,
        rgba(46, 97, 199, 0.5) 100%
      );

      position: relative;
      border: 2px solid transparent;
      border-top: 2px solid #111421;
      &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: 2px;
        background: linear-gradient(
          90deg,
          rgba(106, 145, 197, 0.4) 0%,
          rgba(170, 212, 255, 0.8) 49.52%,
          rgba(106, 145, 197, 0.4) 100%
        );
      }
      margin-right: 1px;
    }

    .operation-tabs-active {
      @extend.operation-tabs-item;
      color: #d9f7ff;
      background: linear-gradient(180deg, #2669ef 0%, #142d60 100%);
      border: 2px solid transparent;
      border-image: linear-gradient(
          90deg,
          #def1fe 3.37%,
          rgba(222, 241, 254, 0) 100%
        )
        1;
    }
  }
}
</style>
