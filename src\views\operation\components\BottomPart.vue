<template>
  <div class="bottomPart" />
</template>
<script>
export default {
  name: 'BottomPart',
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@use "sass:math";
@import "../icon/font.css";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
.bottomPart {
  width: 100%;
  height: vh(185);
  line-height: vh(185);
  background: url("../../../assets/images/dashboard/bottom-background.png")
    no-repeat;
  background-size: 100% 100%;
  position: absolute;
  bottom: 0;
}
</style>
