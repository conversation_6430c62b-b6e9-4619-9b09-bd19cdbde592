<template>
    <div class="dialog" @click.stop>
        <div class="info-box box-common">
            <div class="content-common">
                <div class="title-box">
                    任务信息展示
                </div>
                <div class="form-box1 el-common">
                    <el-form ref="form" :model="form" label-width="120px" size="mini">
                        <el-row :gutter="10">
                            <!-- 作战名称 -->
                            <el-col :span="24">
                                <el-form-item label="作战名称">
                                    <el-input v-model="form.operationName" placeholder="请输入"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <!-- 作战期限（开始、结束时间） -->
                            <el-col :span="12">
                                <el-form-item label="作战期限">
                                    <el-date-picker 
                                        v-model="form.timeRange" 
                                        type="datetimerange" 
                                        style="width: 100%"
                                        start-placeholder="开始时间" 
                                        end-placeholder="结束时间" 
                                        format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss" 
                                        @change="changeTime"
                                        range-separator=""
                                        popper-class="dataTime-select-dropdown">
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <!-- 作战目标 -->
                            <el-col :span="12">
                                <el-form-item label="作战目标">
                                    <el-input v-model="form.operationTarget" placeholder="请输入"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="10">
                            <!-- 作战清单数量 -->
                            <el-col :span="12">
                                <el-form-item label="作战清单数量">
                                    <el-input v-model="form.listCount" placeholder="请输入"></el-input>
                                </el-form-item>
                            </el-col>

                            <!-- 上个任务需求 -->
                            <el-col :span="12">
                                <el-form-item label="上个任务需求">
                                    <el-input v-model="form.lastTaskDemand" placeholder="请输入"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="10">
                            <!-- 作战描述 -->
                            <el-col :span="24">
                                <el-form-item label="作战描述">
                                    <el-input type="textarea" v-model="form.operationDesc" placeholder="请输入"
                                        rows="2"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div class="demand-box box-common">
            <div class="content-common">
                <div class="title-box">
                    任务需求
                </div>
                <div class="form-box2 el-common">
                    <el-form ref="form" :model="form" label-width="120px" size="mini">
                        <el-row :gutter="10">
                            <!-- 作战描述 -->
                            <el-col :span="24">
                                <el-form-item label="任务需求">
                                    <el-input type="textarea" v-model="form.taskRequirements" placeholder="请输入"
                                        rows="3"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div class="staff-box box-common">
            <div class="content-common">
                <div class="title-box">
                    执行人员选择
                </div>
                <div class="table-box">
                    <el-table :data="currentData" :cell-style="cellStyle" :cell-class-name="cellClassName"
                        :header-cell-class-name="headerCellClassName">
                        <el-table-column type="index" width="90" label="序号"></el-table-column>
                        <el-table-column v-for="(column) in columns" :key="column.prop" :prop="column.prop"
                            :label="column.label">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row[column.prop]" popper-class="select-dropdown"
                                    placeholder="请选择" v-if="column.prop === 'selectBusiness'">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                                <el-select v-model="scope.row[column.prop]" popper-class="select-dropdown" placeholder="请选择"
                                    v-else-if="column.prop === 'selectPeople'">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                                <span v-else>{{ scope.row[column.prop] }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination popper-class="select-dropdown" background @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" :current-page.sync="page.currentPage"
                        :page-sizes="[10, 200, 300, 400]" :page-size="page.pageSize" :total="page.total"
                        layout="sizes, prev, pager, next, jumper">
                    </el-pagination>
                </div>
            </div>
        </div>
        <div class="btn-box">
            <div class="cancel common-btn" @click="cancel">
                <div>取消</div>
            </div>
            <div class="transfer common-btn" @click="transfer">
                <div>转派</div>
            </div>
        </div>
    </div>
</template>
<script>

export default {
    name: 'DetailDialog',
    data() {
        return {
            form: {
                operationName: '',
                timeRange: null,
                operationTarget: '',
                listCount: '',
                lastTaskDemand: '',
                operationDesc: '',
                taskRequirements: '',
                selectBusiness: '',
                selectPeople: '',
            },
            options: [
                {
                    value: '选项1',
                    label: '黄金糕'
                },
                {
                    value: '选项2',
                    label: '双皮奶'
                }, 
                {
                    value: '选项3',
                    label: '蚵仔煎'
                }, 
                {
                    value: '选项4',
                    label: '龙须面'
                }, 
                {
                    value: '选项5',
                    label: '北京烤鸭'
                }
            ],
            page: { pageSize: 10, currentPage: 1, total: 100 },
            columns: [
                { label: '街区名称', prop: 'streetName' },
                { label: '归属区县', prop: 'belongArea' },
                { label: '街长', prop: 'streetLeader' },
                { label: '选择商客直销', prop: 'selectBusiness' },
                { label: '选择渠道人员', prop: 'selectPeople' },
            ],
            tableData: [
                {
                    streetName: '街1',
                    belongArea: '区1',
                    streetLeader: '街长1',
                    selectBusiness: '',
                    selectPeople: '',
                },
                {
                    streetName: '街2',
                    belongArea: '区2',
                    streetLeader: '街长2',
                    selectBusiness: '',
                    selectPeople: '',
                },
                {
                    streetName: '街3',
                    belongArea: '区3',
                    streetLeader: '街长2',
                    selectBusiness: '',
                    selectPeople: '',
                },
                {
                    streetName: '街3',
                    belongArea: '区3',
                    streetLeader: '街长2',
                    selectBusiness: '',
                    selectPeople: '',
                },
                {
                    streetName: '街3',
                    belongArea: '区3',
                    streetLeader: '街长2',
                    selectBusiness: '',
                    selectPeople: '',
                },
                {
                    streetName: '街3',
                    belongArea: '区3',
                    streetLeader: '街长2',
                    selectBusiness: '',
                    selectPeople: '',
                }
            ]
        }
    },
    created() {
        this.currentData = this.currentChangePage(this.page.pageSize, this.page.currentPage)
    },
    methods: {
        changeTime(val) {
            console.log(this.form.timeRange)
        },
        cancel() {
            this.$emit('cancel', false)
        },
        transfer() {
            this.$emit('transfer', this.currentData)
            console.log(this.currentData)
        },
        //表格样式
        cellClassName() {
            return 'cell-style'
        },
        headerCellClassName() {
            return 'header-common';
        },
        cellStyle({ row, rowIndex }) {
            return {
                backgroundColor: rowIndex % 2 !== 0 ? 'rgba(36, 63, 93, 1)' : 'rgba(32, 66, 105, 1)'
            };
        },
        //前端表格分页功能
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`)
            this.page.pageSize = val
            this.currentData = this.currentChangePage(val, this.page.currentPage);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`)
            this.currentData = this.currentChangePage(this.page.pageSize, val);
        },
        currentChangePage(pageSize, currentPage) {
            const currentData = [];
            let array = JSON.parse(JSON.stringify(this.tableData));
            array.forEach((item, index) => {
                if (pageSize * (currentPage - 1) <= index && index <= pageSize * currentPage - 1) {
                    currentData.push(item);
                }
            });
            return currentData;
        },
    },
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";

.dialog {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .box-common {
        width: vw(1840);
        padding: vw(2);
        background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));
        border-radius: vw(10);

        .content-common {
            width: 100%;
            height: 100%;
            border-radius: vw(8);
            /* 比外层小2px，避免露出底色 */
            background: #13274a;
            padding-top: vh(28);

            .title-box {
                width: vw(1200);
                height: vh(28);
                font-family: PingFang SC;
                font-weight: 600;
                font-size: vw(20);
                line-height: vh(28);
                margin: 0 auto;
                background: linear-gradient(180deg, #E4EEFF 25%, #9AC0FF 82.14%);
                background-clip: text;
                color: transparent;
                display: flex;
                align-items: center;
                margin-bottom: vh(10);
            }

            .title-box::before {
                content: '';
                display: inline-block;
                width: vw(4);
                height: vh(20);
                margin-right: vw(4);
                border-radius: vw(2);
                background: linear-gradient(201.79deg, #8CB2FF 14.28%, rgba(69, 130, 255, 0.53) 85.72%);

            }

            .form-box1 {
                width: vw(1200);
                height: vh(206);
                margin: 0 auto;
                // background-color: #8CB2FF;
            }

            .form-box2 {
                width: vw(1200);
                height: vh(92);
                margin: 0 auto;
            }

            .el-common {
                ::v-deep .el-form-item__label {
                    font-size: vw(16);
                    height: vh(32);
                    line-height: vh(32);
                    color: rgba(228, 238, 255, 1);
                }

                ::v-deep .el-form-item {
                    margin-bottom: vh(10);
                }

                ::v-deep .el-input__inner,
                ::v-deep .el-textarea__inner {
                    background-color: #1d334b;
                    padding: 0 vw(8);
                    border-color: rgba(68, 83, 105, 1);
                    color: white;
                }

                ::v-deep .el-textarea__inner {
                    min-height: vh(80) !important;
                }

                ::v-deep .el-form-item__content {
                    line-height: vh(32);
                }

                ::v-deep .el-input__inner {
                    height: vh(32);
                    line-height: vh(32);
                }

                ::v-deep .el-range-input {
                    background-color: #1d334b;
                    color: rgba(228, 238, 255, 1);
                }
            }

            .table-box {
                width: vw(1200);
                height: vh(256);
                margin: 0 auto;

                ::v-deep .el-table::before {
                    height: 0;
                }

                ::v-deep .el-table {
                    height: vh(192);
                    border-radius: vw(10);
                    overflow: auto;
                    border-color: #265282;

                    .header-common {
                        width: vw(200);
                        height: vh(48);
                        background-color: #265282 !important;
                        /* 强制生效 */
                        color: rgba(228, 238, 255, 1) !important;
                        border: vw(1) solid #4d6887;
                        // border: 0;
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: vw(14);
                        line-height: vh(22);
                        padding: 0 !important;
                    }

                    .cell-style {
                        width: vw(200);
                        height: vh(48);
                        color: rgba(228, 238, 255, 1) !important;
                        border: vw(1) solid #4d6887;
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: vw(14);
                        line-height: vh(22);
                        padding: 0 !important;
                        // border: 0;
                    }
                }

                ::v-deep .el-table .el-input__inner {
                    color: white !important;
                    background-color: transparent !important;
                    padding: 0;
                    border: 0;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: vw(14);
                    line-height: vh(22);
                }

                ::v-deep .el-pagination {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    height: vh(64);
                }

                ::v-deep .el-pagination .el-pager li {
                    border: 1px solid rgba(173, 176, 189, 1);
                    color: white !important;
                    background-color: transparent !important;
                }

                ::v-deep .el-pagination .btn-next,
                ::v-deep .el-pagination .btn-prev {
                    color: white !important;
                    background-color: transparent !important;
                }

                ::v-deep .el-pager li.active {
                    background-color: #8cb2ff !important;
                }

                ::v-deep .el-pagination__jump {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: vw(14);
                    line-height: vh(22);
                    color: rgba(173, 176, 189, 1);
                }

                ::v-deep .el-pagination .el-input__inner {
                    color: white !important;
                    background-color: transparent !important;
                }
            }
        }
    }

    .info-box {
        height: vh(304);
    }

    .demand-box {
        height: vh(176);
    }

    .staff-box {
        height: vh(334);
    }

    .btn-box {
        width: vw(212);
        height: vh(40);
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: vw(814);

        .common-btn {
            border-radius: vw(2);
            color: rgba(228, 238, 255, 1);
            box-shadow: 0px 0px 20px 0px rgba(143, 181, 255, 0.64) inset;
            background: linear-gradient(0deg, #DEF1FE 0%, rgba(222, 241, 254, 0) 100%);
            padding: vw(2);

            div {
                width: 100%;
                height: vh(36);
                cursor: pointer;
                text-align: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(16);
                line-height: vh(38);
                background: linear-gradient(181.96deg, #6498FF -38.96%, #1D4BA7 39.59%, #142D60 98.35%);
            }
        }

        .cancel {
            width: vw(80);
            height: vh(40);
        }

        .transfer {
            width: vw(122);
            height: vh(40);
        }
    }
    ::v-deep .el-range-separator {
        color: #ffffff;
    }
}
</style>
<style lang="scss">
.select-dropdown {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;

    .popper__arrow {
        border-bottom-color: #677aa5 !important;

        &::after {
            border-bottom-color: #141e32 !important;
        }
    }

    .el-select-dropdown__wrap {

        margin-right: -16px !important;
    }

    .el-select-dropdown__item {
        color: #ccd3d9;
        font-size: vw(14) !important;
        height: vh(34);
        line-height: vh(34);
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background-color: #147bd119 !important;
        color: #4bb6ff;
    }
}

.dataTime-select-dropdown {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;
    color: white;

    //   color: white !important;
    th {
        color: white !important;
    }

    .prev-month,
    .next-month {
        color: #606266 !important;
    }

    .el-input__inner {
        background-color: transparent !important;
        color: white;
    }

    .in-range div {
        background-color: #677aa5 !important;
    }

    .el-picker-panel__footer {
        background-color: #147bd119 !important;

        .el-button {
            background-color: transparent;
            border: 0;
            color: white;
        }
    }
}
</style>