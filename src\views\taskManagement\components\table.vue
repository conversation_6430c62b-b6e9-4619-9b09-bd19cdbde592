<template>
    <div class="el-table">
        <el-table :data="currentData" :cell-style="cellStyle" :cell-class-name="cellClassName"
            :header-cell-class-name="headerCellClassName">
            <el-table-column type="index" width="90" label="序号"></el-table-column>
            <el-table-column v-for="column in columns" :key="column.prop" :prop="column.prop" :label="column.label">
                <template slot-scope="scope">
                    <span v-if="column.prop === 'distributeTime'" style="color: rgba(255, 215, 137, 1);">
                        {{ scope.row[column.prop] }}
                    </span>
                    <span v-else-if="column.prop === 'status'">
                        <span class="status-dot" :style="{ backgroundColor: getDotColor(scope.row.status) }"></span>
                        <span class="status-text" :style="{ color: getTextColor(scope.row.status) }">
                            {{ getStatusText(scope.row.status) }}
                        </span>
                    </span>
                    <span v-else>{{ scope.row[column.prop] }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                    <!-- 查看操作要区分 -->
                    <el-button @click="distribute" type="text" size="small" v-if="menuActive == 1">
                        派发
                    </el-button>
                    <el-button disabled @click.stop="viewDetail" type="text" size="small">
                        查看
                    </el-button>
                    <el-button @click="termination" type="text" size="small" v-if="menuActive == 3">
                        终止
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination popper-class="select-dropdown" background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page.sync="page.currentPage" :page-sizes="[10, 200, 300, 400]" :page-size="page.pageSize"
            :total="page.total" layout="sizes, prev, pager, next, jumper">
        </el-pagination>
    </div>
</template>
<script>
export default {
    name: "Table",
    props: {
        columns: {
            type: Array,
            default: () => []
        },
        menuActive: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            page: { pageSize: 10, currentPage: 1, total: 100 },
            tableData: [
                {
                    index: 1,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 1,
                },
                {
                    index: 2,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 2,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 1,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 2,
                },
                {
                    index: 1,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 1,
                },
                {
                    index: 2,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 2,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 1,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 2,
                },
                {
                    index: 1,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 1,
                },
                {
                    index: 2,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 2,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 3,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 1,
                },
                {
                    index: 3,
                    name: '区域作战名称1',
                    address: '作战描述1',
                    creater: '创建人1',
                    createrRole: '创建人角色1',
                    createTime: '2025-07-21 15:20:00',
                    duration: '2025-07-21~2025-07-22',
                    target: '作战目标1',
                    distributeTime: '2025-07-21 15:20:00',
                    status: 2,
                }
            ],
            currentData: []
        }
    },
    created() {
        this.currentData = this.currentChangePage(this.page.pageSize, this.page.currentPage)
    },
    methods: {
        //列表回显方法
        getDotColor(status) {
            // 根据状态返回对应圆点颜色
            switch (status) {
                case 1:
                    return '#FFA600'; // 转派中橙色
                case 2:
                    return '#76E8E6'; // 执行中青色
                case 3:
                    return '#C0C4CC'; // 已完成浅灰色
            }
        },
        getTextColor(status) {
            // 根据状态返回对应文本颜色，和圆点颜色保持一致
            return this.getDotColor(status);
        },
        getStatusText(status) {
            // 根据状态返回对应文本
            switch (status) {
                case 1:
                    return '转派中';
                case 2:
                    return '执行中';
                case 3:
                    return '已完成';
            }
        },
        //前端表格分页功能
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`)
            this.page.pageSize = val
            this.currentData = this.currentChangePage(val, this.page.currentPage);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`)
            this.currentData = this.currentChangePage(this.page.pageSize, val);
        },
        currentChangePage(pageSize, currentPage) {
            const currentData = [];
            let array = JSON.parse(JSON.stringify(this.tableData));
            array.forEach((item, index) => {
                if (pageSize * (currentPage - 1) <= index && index <= pageSize * currentPage - 1) {
                    currentData.push(item);
                }
            });
            return currentData;
        },
        //表格样式
        cellClassName() {
            return 'cell-style'
        },
        headerCellClassName() {
            return 'header-common';
        },
        cellStyle({ row, rowIndex }) {
            return {
                backgroundColor: rowIndex % 2 !== 0 ? 'rgba(36, 63, 93, 1)' : 'rgba(32, 66, 105, 1)'
            };
        },
        //操作功能
        viewDetail() {
            this.$emit('viewDetail', true)
        },
        distribute() {
            console.log('派发')
        },
        termination() {
            console.log('终止')
        }
    }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.el-table::before {
    height: 0;
}
.el-table {
    width: vw(1800);
    border-radius: vw(10);
    background-color: transparent;
    .status-dot {
        display: inline-block;
        width: vw(10);
        height: vh(10);
        border-radius: 50%;
        margin-right: vw(8);
    }
    /* 文本样式可根据需求调整 */
    .status-text {
        font-size: vw(14);
    }
    ::v-deep .el-table {
        width: vw(1800);
        height: vh(736);
        border-radius: vw(10);
        overflow: auto;
        border: 0;
        background-color: transparent;
        .header-common {
            width: vw(198);
            height: vh(48);
            background-color: #265282 !important;
            /* 强制生效 */
            color: rgba(228, 238, 255, 1) !important;
            border: vw(1) solid rgba(230, 235, 240, 0.2);
            border-top: 0;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(14);
            line-height: vh(22);
            padding: 0 !important;
        }
        .cell-style {
            width: vw(198);
            height: vh(48);
            color: rgba(228, 238, 255, 1) !important;
            border: vw(1) solid rgba(230, 235, 240, 0.2);
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(14);
            line-height: vh(22);
            padding: 0 !important;
        }
    }
}
::v-deep .el-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: vh(64);
}
::v-deep .el-pagination .el-pager li {
    border: 1px solid rgba(173, 176, 189, 1);
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination .btn-prev {
    color: white !important;
    background-color: transparent !important;
}
::v-deep .el-pager li.active {
    background-color: #8cb2ff !important;
}
::v-deep .el-pagination__jump {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: vw(14);
    line-height: vh(22);
    color: rgba(173, 176, 189, 1);
}
::v-deep .el-input__inner {
    color: white !important;
    background-color: transparent !important;
}
</style>
