<template>
  <div class="task-management">
    <div class="operation-tabs" v-show="!dialogShow">
      <div v-for="item in menu" :key="item.id" :class="{ 'operation-tabs-active': item.id === menuActive }"
        class="operation-tabs-item" @click="navClick(item)">{{ item.name }}</div>
    </div>
    <div class="table">
      <div class="content">
        <Table :columns="currentColumnData" :menuActive="menuActive" @viewDetail="viewDetail"></Table>
      </div>
    </div>
    <DetailDialog class="dialog-box" v-show="dialogShow" @cancel="cancel" @transfer="transfer"></DetailDialog>
    <el-dialog
      :top="'35vh'"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      center>
      <div class="img">
        <img src="../../assets/images/taskSend.png" alt="">
        <div class="text">派发成功</div>
      </div>
      <div class="btn" @click="dialogVisible = false">
        <div class="cont">  
        <div>确定</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Table from './components/table.vue';
import DetailDialog from './components/detailDialog.vue';
export default {
  name: 'TaskManagement',
  components: {
    Table,
    DetailDialog
  },
  filters: {
  },
  data() {
    return {
      dialogVisible: false,
      menuActive: 1,
      menu: [
        {
          id: 1,
          name: '代办'
        },
        {
          id: 2,
          name: '已办'
        },
        {
          id: 3,
          name: '我创建的'
        }
      ],
      column1: [
        // { prop: 'index', label: '序号', width: '200' },
        { prop: 'name', label: '区域作战名称', width: '198' },
        { prop: 'address', label: '作战描述', width: '198' },
        { prop: 'creater', label: '创建人', width: '198' },
        { prop: 'createrRole', label: '创建人角色', width: '198' },
        { prop: 'createTime', label: '创建时间', width: '198' },
        { prop: 'duration', label: '作战期限', width: '198' },
        { prop: 'target', label: '作战目标', width: '198' },
      ],
      column2: [
        // { prop: 'index', label: '序号', width: '200' },
        { prop: 'name', label: '区域作战名称', width: '198' },
        { prop: 'address', label: '作战描述', width: '198' },
        { prop: 'creater', label: '创建人', width: '198' },
        { prop: 'createrRole', label: '创建人角色', width: '198' },
        { prop: 'createTime', label: '创建时间', width: '198' },
        { prop: 'duration', label: '作战期限', width: '198' },
        { prop: 'target', label: '作战目标', width: '198' },
        { prop: 'distributeTime', label: '派发时间', width: '198' },
        { prop: 'status', label: '当前状态', width: '198' },
      ],
      dialogShow: false,
    }
  },
  computed: {
    currentColumnData() {
      return this.menuActive === 2? this.column2 : this.column1
    },
    currentTableData() {
      return this.menuActive === 2? this.tableData2 : this.tableData1
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    navClick(item) {
      if (item.id === this.menuActive) return
      this.menuActive = item.id
    },
    viewDetail(data) {
      this.dialogShow = true
    },
    cancel(data) {
      this.dialogShow = false
    },
    transfer(data) {
      this.dialogVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.task-management {
  width: 100%;
  // position: relative;
  .operation-tabs {
    position: absolute;
    top: vh(30);
    left: vw(30);
    display: flex;
    .operation-tabs-item {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(16);
      line-height: vh(32);
      padding: vh(4.5) vw(20);
      color: #8ac0ff;
      cursor: pointer;
      background: linear-gradient(180deg,
          rgba(15, 27, 52, 0.5) 0%,
          rgba(46, 97, 199, 0.5) 100%);
      position: relative;
      border: 2px solid transparent;
      border-top: 2px solid #111421;
      &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: 2px;
        background: linear-gradient(90deg,
            rgba(106, 145, 197, 0.4) 0%,
            rgba(170, 212, 255, 0.8) 49.52%,
            rgba(106, 145, 197, 0.4) 100%);
      }
      margin-right: 1px;
    }
    .operation-tabs-active {
      @extend.operation-tabs-item;
      color: #d9f7ff;
      background: linear-gradient(180deg, #2669ef 0%, #142d60 100%);
      border: 2px solid transparent;
      border-image: linear-gradient(90deg,
          #def1fe 3.37%,
          rgba(222, 241, 254, 0) 100%) 1;
    }
  }
  .table {
    width: vw(1840);
    height: vh(840);
    margin: 0 auto;
    margin-top: vh(101);
    padding: 2px;
    background: linear-gradient(180deg, rgba(89, 183, 255, 1), rgba(41, 33, 98, 0));
    border-radius: vw(10);
    .content {
      /* 内层div作为内容容器 */
      padding: vh(20) vw(20);
      border-radius: vw(8);
      /* 比外层小2px，避免露出底色 */
      background: #13274a;
    }
  }
  .dialog-box {
      position: absolute;
      top: vh(31);
      left: vw(40);
      width: vw(1840);
      height: vh(916);
      background: #151927;
      // z-index: 101;
  }
  ::v-deep .el-dialog  {
    width: vw(503);
    height: vh(295);
    background: linear-gradient(148.39deg, #113963 38.34%, #1D3052 98.51%);
    position: relative;
    border-radius: vw(8);
    // transform: translate(-50%, -50%);
    .dialog-footer {
      position: absolute;
      bottom: 0;
    }
    .el-dialog__body {
      padding: 0 !important;
    }
  }
  .img {
    width: 100%;
    height: vh(148);
    position: absolute;
    bottom: vh(92.5);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    img {
      width: vw(106);
      height: vh(106);
      margin: 0 auto;
    }
    .text {
      width: vw(72);
      height: vh(28);
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(18);
      line-height: vh(28);
      text-align: center;
      background: linear-gradient(180deg, #E4EEFF 25%, #9AC0FF 82.14%);
      background-clip: text;
      color: transparent;
    }
  }
  .btn {
    width: 100%;
    height: vh(72);
    position: absolute;
    bottom: 0;
    padding-top: vh(12);
     .cont {
      width: vw(80);
      border-radius: vw(2);
      color: rgba(228, 238, 255, 1);
      box-shadow: 0px 0px 20px 0px rgba(143, 181, 255, 0.64) inset;
      background: linear-gradient(0deg, #DEF1FE 0%, rgba(222, 241, 254, 0) 100%);
      padding: vw(2);
      margin: 0 auto;
      div {
          width: 100%;
          height: vh(36);
          cursor: pointer;
          text-align: center;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: vw(16);
          line-height: vh(38);
          background: linear-gradient(181.96deg, #6498FF -38.96%, #1D4BA7 39.59%, #142D60 98.35%);
      }
    }
  }
 
}
</style>
