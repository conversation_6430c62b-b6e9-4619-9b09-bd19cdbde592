<template>
    <div class="customerHeatMap">
         <div v-show="areaLevel < 4" class="echarts-map-back"></div>
        <EchartsMap ref="echartsMap" @getTotalCount="getTotalCount" :strategyBatchList="checkList"
            :selectForm="selectForm" @updataCode="updataCode" :parentLevel="areaLevel"
            @updadeLegendList="updadeLegendList"/>
        <div class="back" @click="backMap" v-if="areaLevel !== 1">
            <div class="icon"></div>
            <div>返回</div>
        </div>
        <div class="select-part">
            <div class="area-select">
                <i slot="prefix" class="icon"></i>
                <el-select v-model="selectForm.cityCode" :clearable="false" placeholder="全省" popper-class="custom-select"
                    size="mini" @change="onCityChange()" :disabled="userLevel > 1">
                    <el-option label="全省" :value="999"></el-option>
                    <el-option v-for="item in selectAreaList.cityList" :key="item.areaId" :label="item.areaName"
                        :value="item.areaId" />
                </el-select>
                <el-select v-model="selectForm.countyCode" :clearable="false" placeholder="区县" popper-class="custom-select" 
                    size="mini" @change="onCountyChange()" :disabled="userLevel > 2">
                    <el-option v-for="item in selectAreaList.countyList" :key="item.areaId" :label="item.areaName"
                        :value="item.areaId" />
                </el-select>
                <el-select v-model="selectForm.areaCode" :clearable="false" placeholder="网格"
                    popper-class="custom-select" size="mini" @change="onAreaChange()">
                    <el-option v-for="item in selectAreaList.areaList" :key="item.areaId" :label="item.areaName"
                        :value="item.areaId" />
                </el-select>
                <el-select v-model="selectForm.villageCode" :clearable="false" placeholder="行政村"
                    popper-class="custom-select" size="mini" @change="onGridChange()">
                    <el-option v-for="item in selectAreaList.villageList" :key="item.areaId" :label="item.areaName"
                        :value="item.areaId" />
                </el-select>
            </div>
            <div class="body-box">
                <el-dialog :visible.sync="dialogVisible" class="dialog-page" :modal="false" :show-close="false" center>
                    <div class="page-body">
                        <div class="body-title">
                            <div style="display: flex;">
                                <div class="bg"></div>
                                <div class="text">作战目标客户（
                                    {{ customerArea }}）
                            </div>
                            </div>
                            <!-- <div class="switch">
                                <el-switch v-model="isstrategy" inactive-text="选择策略">
                                </el-switch>
                            </div> -->
                        </div>
                        <div class="search">
                            <el-input placeholder="请输入" v-model="searchKey" class="search-input"
                                @keydown.enter.native="search">
                                <i slot="prefix" class="el-input__icon" @click="search"></i>
                            </el-input>
                        </div>
                        <div class="select-box">
                            <el-checkbox-group v-model="checkIdData" :min="1">
                                <div v-for="(item, index) in currentData" :key="item.customerGroupId"
                                    class="select-card">
                                    <el-checkbox :label="item.strategyId" @change="handleChange(item)">
                                        <div class="id-label">
                                            <el-tooltip :content="item.strategyName" placement="bottom">
                                                <span class="name">
                                                    {{ item.strategyName }}
                                                </span>
                                            </el-tooltip>
                                            <span class="label"
                                                :class="[item.strategyType == '重点' ? 'green' : 'yellow']">
                                                {{ item.strategyType }}</span>
                                        </div>
                                        <div class="id-number">
                                            <span class="id">ID：<span>{{ item.realId }}</span></span>
                                            <span class="number">客群数量：<span>{{ item.customerGroupSize }}</span>人</span>
                                        </div>
                                    </el-checkbox>
                                </div>
                            </el-checkbox-group>
                        </div>
                        <div class="page-box">
                            <el-button class="prv" @click="prvPage"
                                :disabled="this.page.currentPage == 1">上一页</el-button>
                            <div>{{ page.currentPage }}/{{ page.total }}</div>
                            <el-button class="next" @click="nextPage"
                                :disabled="this.page.currenPage == this.page.total">下一页</el-button>
                        </div>
                    </div>
                    <div class="page-footer" slot="footer" v-if="areaLevel !== 1">
                        <el-button @click="createTask">下一步</el-button>
                    </div>
                </el-dialog>
            </div>
        </div>
        <div class="echarts-legend">
            <!-- <div class="legend-title">街区数量</div> -->
            <div v-for="item, index in legendList" :key="index" class="legend-icon">
                <div :style="{ backgroundColor: item.color, borderColor: item.borderColor }" class="legend-icon-box" />
                <div>{{ item.text }}</div>
            </div>
        </div>
    </div>
</template>
<script>
import EchartsMap from './EchartsMap.vue'
import market from '@/api/market'
import common from '@/api/common'
export default {
    name: 'CustomerHeatMap',
    components: {
        EchartsMap,
    },
    data() {
        return {
            areaType: '1',
            searchKey: '',
            isstrategy: true,
            dialogVisible: true,
            currentData: [],
            checkIdData: [],
            checkList: [],
            page: {
                currentPage: 1,
                total: '6',
                pageSize: 6,
            },
            selectForm: {
                cityCode: '',
                countyCode: '',
                areaCode: '',
                villageCode: '',
            },
            selectAreaNameList: {
                cityName: '',
                countyName: '',
                areaName: '',
                gridName: '',
            },
            selectAreaList: {
                cityList: [],
                countyList: [],
                areaList: [],
                villageList: [],
            },
            legendList: [
                { color: '#FF393980', borderColor: '#FF8787', text: '' },
                { color: '#FF838380', borderColor: '#FFBCB9', text: '' },
                { color: '#37D89280', borderColor: '#A9FFDA', text: '' },
                { color: '#A9FFDA80', borderColor: '#A9FFDA', text: '' }
            ],
            customerTotalCount: 0,
            areaLevel: null, //1是省级， 2是地市，3是区县，4是网格
            areaId: '', //999是省级 不同人员接口入参
            userLevel: '', //用户等级，控制筛选
        }
    },
    created() {
       this.getUserInfo()
    },
    watch: {
        // areaType(newVal, oldVal) {
        //     console.log('areaType 变化了', newVal, oldVal);
        //     this.onAreaChange()
        // }
    },
    computed: {
        customerArea() {
            let name = ''
            let city = this.selectAreaList.cityList.find(item => item.areaId === this.selectForm.cityCode)
            let county = this.selectAreaList.countyList.find(item => item.areaId === this.selectForm.countyCode)
            let area = this.selectAreaList.areaList.find(item => item.areaId === this.selectForm.areaCode)
            let grid = this.selectAreaList.villageList.find(item => item.areaId === this.selectForm.villageCode)
            if (grid) {
                name = city.areaName+' '+county.areaName+' '+area.areaName+' '+grid.areaName
            } else if (area) {
                name = city.areaName+' '+county.areaName+' '+area.areaName
            } else if (county) {
                name = city.areaName+' '+county.areaName
            } else if (city) {
                name = city.areaName
            } else {
                name = '全省'
            }
            this.customerAreaName = name
            return name
        }
    },
    async mounted() {
        // 首次进入获取地市的选择
        await this.getAreaByPid(999, 'cityList')
        //从网络覆盖跳转过来处理, 带来已选中的地域信息
        // 第一次获取全省策略清单列表，要默认添加
        // await this.getStrategyList()

        if (this.$route.query.from && this.$route.query.from === 'networkCoverage') {
            this.selectForm.cityCode = this.$route.query.cityCode || ''
            this.selectForm.countyCode = this.$route.query.countyCode || ''
            this.selectForm.areaCode = this.$route.query.areaCode || ''
            this.selectForm.villageCode = this.$route.query.villageCode || ''
            const level = Number(this.$route.query.level) || 0;
            // 判断是否大于等于5，是的话赋值为5，否则使用原始值
            this.areaLevel = level >= 5 ? 5 : level;
            this.netWorkJump()
        } else {
            this.getUserCode()
        }
    },
    methods: {
        updadeLegendList(data) {  //子组件暂时不需要调用更改图例
            this.legendList = [
                { color: '#FF393980', borderColor: '#FF8787', text: data.label1 },
                { color: '#FF838380', borderColor: '#FFBCB9', text: data.label2 },
                { color: '#37D89280', borderColor: '#A9FFDA', text: data.label3 },
                { color: '#A9FFDA80', borderColor: '#A9FFDA', text: data.label4 }
            ]
        },
        getUserInfo() {
            const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
            this.areaLevel = userInfo.roleType 
            this.userLevel = userInfo.roleType 
        },
        async getUserCode() {
            const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
            switch (this.areaLevel) {
                case 1:
                    this.areaId = userInfo.provId //999
                    await this.getStrategyList()
                    // await this.$refs.echartsMap.getShengArea() //获取云南省边界
                    await this.$refs.echartsMap.getDimAreaByPid({ //获取地市边界
                        areaId: this.areaId,
                        areaLevel: this.areaLevel+1
                    })
                    break
                case 2:
                    this.areaId = userInfo.cityId
                    this.selectForm.cityCode = userInfo.cityId || ''
                    this.onCityChange()
                    break
                case 3:
                    this.areaId = userInfo.countyId
                    this.selectForm.cityCode = userInfo.cityId || ''
                    this.selectForm.countyCode = userInfo.countyId || ''
                    await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
                    this.onCountyChange()
                    break
                case 4:
                    this.areaId = userInfo.gridId
                    this.selectForm.cityCode = userInfo.cityId || ''
                    this.selectForm.countyCode = userInfo.countyId || ''
                    this.selectForm.areaCode = userInfo.gridId || ''
                    await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
                    await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
                    //通过子组件方法获得百度地图行政村中心的
                    await this.$refs.echartsMap.getDimAreaByPid({ //获取网格边界, arealvel=4
                        areaId: this.selectForm.countyCode,
                        areaLevel: 4, //接口获取为4
                        type: 1 //只获得边界信息
                    });
                    this.onAreaChange()
            }
        },
        //网络覆盖跳转加载信息
        async netWorkJump() {
             switch (this.areaLevel) {
                case 1:
                    this.areaId = this.$route.query.provinceCode //999
                    await this.getStrategyList()
                    // await this.$refs.echartsMap.getShengArea() //获取云南省边界
                    await this.$refs.echartsMap.getDimAreaByPid({ //获取地市边界
                        areaId: this.areaId,
                        areaLevel: this.areaLevel+1
                    })
                    break
                case 2:
                    this.onCityChange()
                    break
                case 3:
                    await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
                    this.onCountyChange()
                    break
                case 4:
                    await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
                    await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
                    //通过子组件方法获得百度地图行政村中心的
                    await this.$refs.echartsMap.getDimAreaByPid({ //获取网格边界, arealvel=4
                        areaId: this.selectForm.countyCode,
                        areaLevel: 4, //接口获取为4
                        type: 1 //只获得边界信息
                    });
                    this.onAreaChange()
                    break
                case 5: 
                    await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
                    await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
                    await this.getAreaByPid(this.selectForm.areaCode, 'villageList')
                    await this.$refs.echartsMap.getDimAreaByPid({ //获取网格边界, arealvel=4
                        areaId: this.selectForm.countyCode,
                        areaLevel: 4, //接口获取为4
                        type: 1 //只获得边界信息
                    });
                    await this.$refs.echartsMap.getStreetData(5, this.selectForm.areaCode); //行政村是5
                    this.onGridChange()
            }
        },
        //从子组件获取customerTotalCount传给创建任务弹窗
        getTotalCount(data) {
            this.customerTotalCount = data;
        },
        updataCode(data) {
            //只到网格
            if (data.areaLevel === 3) { //点击地市触发获取区县
                this.selectForm.cityCode = data.areaId
                // this.selectAreaNameList.cityName = data.name
                this.onCityChange()
            } else if (data.areaLevel === 4) {
                this.selectForm.countyCode = data.areaId
                // this.selectAreaNameList.countyName = data.name
                this.onCountyChange()
            } else if (data.areaLevel === 5) {
                this.selectForm.areaCode = data.areaId
                // this.selectAreaNameList.areaName = data.name
                this.onAreaChange()
            } else if (data.area_level === '5') {  //点击行政村到小区
                this.selectForm.villageCode = data.area_id
                // this.selectAreaNameList.gridName = data.area_name
                this.onGridChange()
            }
        },
        async onCityChange() {
            this.selectForm.countyCode = ''
            this.selectForm.areaCode = ''
            this.selectForm.villageCode = ''
            this.selectAreaList.countyList = []
            this.selectAreaList.areaList = []
            this.selectAreaList.villageList = []
            if (this.selectForm.cityCode == 999) {
                this.selectForm.cityCode = ''   //重新选回云南省查客群数量入参需要为空
                this.areaLevel = 1
                this.areaId = 999
                this.getUserCode()
                return
            }
            this.areaLevel = 2
            await this.getStrategyList()
            await this.$refs.echartsMap.getDimAreaByPid({ //获取昆明区县边界, arealvel=2
                areaId: this.selectForm.cityCode,
                areaLevel: this.areaLevel+1 //接口获取为3
            });
            await this.getAreaByPid(this.selectForm.cityCode, 'countyList')
        },
        async onCountyChange() {
            this.areaLevel = 3
            this.selectForm.areaCode = ''
            this.selectForm.villageCode = ''
            this.selectAreaList.areaList = []
            this.selectAreaList.villageList = []
            await this.getStrategyList()
            await this.$refs.echartsMap.getDimAreaByPid({ //获取网格边界, arealvel=3
                areaId: this.selectForm.countyCode,
                areaLevel: this.areaLevel+1 //接口获取为4
            });
            await this.getAreaByPid(this.selectForm.countyCode, 'areaList')
        },
        async onAreaChange() {
            this.areaLevel = 4
            await this.getStrategyList()
            this.selectForm.villageCode = ''
            this.selectAreaList.villageList = []
            await this.$refs.echartsMap.getVillageData(4,this.checkList);
            await this.$refs.echartsMap.getStreetData(5, this.selectForm.areaCode); //行政村是5
            await this.getAreaByPid(this.selectForm.areaCode, 'villageList')
        },
        async onGridChange() {
            this.areaLevel = 5
            await this.getStrategyList()
            await this.$refs.echartsMap.getVillageData(5,this.checkList);
            await this.$refs.echartsMap.getStreetData(6,this.selectForm.villageCode); //小区6
        },
        //地图返回上一级
        backMap() {
            console.log(this.areaLevel);
            switch (this.areaLevel) {
                case 2: 
                    this.selectForm.cityCode = ''   //重新选回云南省查客群数量入参需要为空
                    // this.selectForm.countyCode = ''
                    // this.selectForm.areaCode = ''
                    // this.selectAreaList.countyList = []
                    // this.selectAreaList.areaList = []
                    this.areaLevel = 1
                    this.areaId = 999
                    this.getUserCode()
                    break;
                case 3: 
                    this.onCityChange()
                    break;
                case 4: 
                    this.onCountyChange()
                    break;
                case 5: 
                    this.onAreaChange()
                    break;
            }
        },
        // 获取下拉框选择列表
        async getAreaByPid(code, listName) {
            await common.getAreaByPid({
                areaPid: code,
            }).then(res => {
                if (res.code === 200) {
                    this.selectAreaList[listName] = res.data
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        //获取策略清单列表
        async getStrategyList() {
            await market.getStrategyList({
                cityCode: this.selectForm.cityCode || '',
                countyCode: this.selectForm.countyCode || '',
                areaCode: this.selectForm.areaCode || '',
                villageCode: this.selectForm.villageCode || '',
                level: this.areaLevel-1, //全省:0,地市:1,区县:2,网格:3,行政村:4
                pageNum: this.page.currentPage,
                pageSize: this.page.pageSize,
                strategyName: this.searchKey
            }).then((res) => {
                if (res.code === 200) {
                    this.currentData = res.data.list
                    this.page.total = (Math.ceil(res.data.total / 6));
                    this.checkIdData = [this.currentData[0].strategyId];
                    this.checkList = [this.currentData[0]];
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 上一页
        prvPage() {
            // 只有当前页大于1时才执行翻页操作
            if (this.page.currentPage > 1) {
                this.page.currentPage--;
                this.getStrategyList();
            }
        },
        // 下一页
        nextPage() {
            // 只有当前页小于总页数时才执行翻页操作
            if (this.page.currentPage < this.page.total) {
                this.page.currentPage++;
                this.getStrategyList();
            }
        },
        //下一步按钮
        createTask() {
            let taskArea = []
            if (this.areaLevel === 2) {
                taskArea = this.selectAreaList.cityList.find(item => item.areaId === this.selectForm.cityCode)
            } else if (this.areaLevel === 3) {
                taskArea = this.selectAreaList.countyList.find(item => item.areaId === this.selectForm.countyCode)
            } else if (this.areaLevel === 4) {
                taskArea = this.selectAreaList.areaList.find(item => item.areaId === this.selectForm.areaCode)
            } else if (this.areaLevel === 5) {
                taskArea = this.selectAreaList.villageList.find(item => item.areaId === this.selectForm.villageCode)
            }
            console.log(this.checkList)
            this.$emit('createTask', {
                checkList: this.checkList,
                totalCount: this.customerTotalCount,
                taskAreaList: taskArea,
                areaLevel: this.areaLevel,
                customerAreaName: this.customerAreaName,
            })
        },
        // 搜索策略清单
        async search() {
            await this.getStrategyList()
        },
        // 清单列表改变时加载地图数据
        async handleChange(item) {
            //清空列表
            this.checkList = [];
            this.checkIdData = [];
            this.checkIdData.push(item.strategyId);
            this.checkList.push(item);
            if (this.areaLevel < 4) {
                await this.$refs.echartsMap.calculateCustomerGroup(1,this.checkList)
            } else if (this.areaLevel == 4) {
                this.$refs.echartsMap.getVillageData(4,this.checkList); //行政村接口 
            } else if (this.areaLevel == 5) {
                this.$refs.echartsMap.getVillageData(5,this.checkList); //小区接口
            }
        }
    }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";
.customerHeatMap {
    .echarts-map-back{
        position: absolute;
        left: vw(-60);
        top: vh(-40);
        bottom: vh(30);
        right: vw(380);
        // z-index: 996;
        background-image: url("../../../assets/images/indexInsight/echarts-back.png");
        background-size: 100% 100%;
    }
    .back {
        display: flex;
        align-items: center;
        cursor: pointer;
        .icon {
            width: vw(24);
            height: vh(24);
            background: url("../../../assets/images/market/icon.png") no-repeat;
            background-size: 100% 100%;
            margin-right: vw(6);
        }
        position: absolute;
        top: vh(23);
        left: vw(56);
        background-color: rgba(168, 216, 255, 1);
        background-clip: text;
        color: transparent;
        font-size: vw(16);
        z-index: 999;
    }
    .select-part {
        position: absolute;
        top: 0;
        right: vw(50);
        width: vw(580);
        height: vh(930);
        // background-color: aqua;
    }
    .area-select {
        width: vw(580);
        height: vh(50);
        line-height: vh(50);
        padding: 0 vw(20);
        position: relative;
        background: linear-gradient(#254177) padding-box,
            linear-gradient(90deg,
                rgba(178, 209, 255, 0.5) 0%,
                rgba(123, 176, 255, 0.25) 50%,
                rgba(178, 209, 255, 0.5) 100%) border-box;
        border: 2px solid transparent;
        border-radius: vw(10);
        font-family: PingFang SC;
        font-weight: 600;
        font-size: vw(14);
        color: #ffffff;
        margin-bottom: vh(23);
        display: flex;
        .icon {
            display: inline-block;
            width: vw(54) !important;
            height: vh(50);
            background: url("../../../assets/images/market/search.png") no-repeat;
            background-size: 100% 100%;
        }
        ::v-deep .el-input__inner {
            // width: vw(160);
            height: vh(36);
            line-height: vh(36);
            background-color: transparent;
            // border-color: #677aa5;
            border: none;
            border-radius: vw(4);
            font-size: vw(14);
            color: #ffffff !important;
            padding: vw(5);
            text-align: right;
            padding-right: vw(30);
        }
        ::v-deep .el-input__suffix-inner .el-icon-arrow-up:before {
           // content: "\e78f";
        }
        ::v-deep .el-input__suffix {
            height: auto;
        }
        ::v-deep .el-select {
            font-size: vw(14);
        }
    }
    .body-box {
        padding: vh(2) vw(2);
        background: linear-gradient(180deg, #59B7FF -8.14%, rgba(41, 33, 98, 0.0001) 100%);
        border-radius: vw(24);
        .dialog-page {
            border-radius: vw(22);
            /* 比外层小2px，避免露出底色 */
            background: rgba(37, 65, 119, 1);
            position: relative;
            ::v-deep .el-dialog {
                width: 100%;
                height: vh(860);
                background: linear-gradient(148.39deg, #113963 38.34%, #1d3052 98.51%);
                border: 1px solid #40525e;
                box-shadow: 0px 12px 32px 0px #00000024;
                border-radius: 8px;
                margin-top: 0 !important;
            }
            ::v-deep .el-dialog__header {
                padding: vh(24) vw(32) vh(8);
                position: absolute;
                >span {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #ffffff;
                }
            }
            ::v-deep .el-dialog__body {
                width: 100%;
                height: vh(759);
                padding: vh(24) 0 0 0 !important;
                .page-body {
                    width: vw(528);
                    height: vh(750);
                    margin: 0 auto;
                    padding: vh(20) vw(20);
                    background-color: #192848;
                    border-radius: vw(10);
                    .body-title {
                        display: flex;
                        justify-content: space-between;
                        .bg {
                            width: vw(69);
                            height: vh(29);
                            background: linear-gradient(90deg, rgba(44, 115, 255, 0.8) 27.5%, rgba(76, 107, 172, 0) 100%);
                        }
                        .text {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: vw(18);
                            line-height: vh(28);
                            color: white;
                            margin-left: vw(-61);
                        }
                    }
                    .select-box {
                        margin-top: vh(20);
                        .select-card {
                            width: vw(488);
                            height: vh(78);
                            margin-bottom: vh(14);
                            background: linear-gradient(180deg, #254177 0%, #1F4A9C 100%),
                                radial-gradient(62.59% 72.5% at 3.63% 87.5%, rgba(93, 147, 255, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%)
                                /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */
                                ,
                                radial-gradient(50% 50% at 50% 50%, rgba(83, 140, 253, 0) 0%, rgba(83, 140, 253, 0.2) 100%)
                                /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */
                            ;
                            border-radius: vw(10);
                            border: 2px solid #7196d7;
                        }
                    }
                    .page-box {
                        margin-top: vh(20);
                        width: vw(488);
                        height: vh(32);
                        display: flex;
                        justify-content: space-between;
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: vw(14);
                        line-height: vh(32);
                        color: white !important;
                        .prv,
                        .next {
                            width: vw(65);
                            text-align: center;
                            color: white !important;
                            border: 0;
                            padding: 0;
                            font-size: vw(14);
                        }
                        .prv {
                            background-color: rgba(43, 87, 175, 1);
                        }
                        .next {
                            background: linear-gradient(251.83deg, #3D7DFE 9.27%, #2453B0 56.49%, #3F7FFF 104.63%);
                        }
                    }
                }
            }
            .switch {
                ::v-deep .el-switch {
                    width: vw(122);
                    height: vh(22);
                    // background-color: #6498ff;
                    .el-switch__label {
                        display: inline-block;
                        width: vw(64);
                        height: vh(22);
                        color: #6498ff !important;
                        span {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: vw(16) !important;
                            line-height: vh(22);
                        }
                    }
                    .is-active {
                        color: white !important;
                    }
                }
            }
            .search {
                margin-top: vh(20);
                .search-input {
                    ::v-deep .el-input__inner {
                        height: vh(50) !important;
                        background-color: #254177 !important;
                        padding-left: vw(60);
                        color: white;
                    }
                    ::v-deep .el-input__icon {
                        display: inline-block;
                        width: vw(54);
                        height: vh(50);
                        background: url("../../../assets/images/market/search.png") no-repeat;
                        background-size: 100% 100%;
                        cursor: pointer;
                    }
                }
            }
            ::v-deep .el-checkbox-group {
                width: vw(488);
                height: vh(538);
            }
            ::v-deep .el-checkbox {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                .el-checkbox__input {
                    // padding: vh(31) vw(30);
                    margin: 0 vw(30);
                    .el-checkbox__inner {
                        width: vw(16);
                        height: vh(16);
                        background-color: transparent;
                        border-color: #74a2ff;
                    }
                }
                .is-checked {
                    background-color: #192848 !important;
                }
                .el-checkbox__label {
                    width: vw(382);
                    height: vh(54);
                    // background-color: black;
                    padding: 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    .id-label {
                        width: vw(382);
                        height: vh(24);
                        display: flex;
                        justify-content: space-between;
                        .name {
                            display: inline-block;
                            width: vw(280);
                            overflow: hidden;
                            text-overflow: ellipsis;
                            /* 添加省略号 */
                            white-space: nowrap;
                            /* 防止换行 */
                            height: (22);
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: vw(16);
                            line-height: vh(24);
                            color: rgba(255, 255, 255, 1);
                        }
                        .label {
                            display: inline-block;
                            width: fit-content;
                            height: vh(24);
                            text-align: center;
                            color: white;
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: vw(14);
                            line-height: vh(20);
                            border-radius: vw(2);
                        }
                        .green {
                            background-color: rgba(77, 160, 124, 1);
                        }
                        .yellow {
                            background-color: #c5891b;
                        }
                    }
                    .id-number {
                        width: fit-content;
                        height: vh(20);
                        .id,
                        .number {
                            display: inline-block;
                            width: fit-content;
                            height: (20);
                            font-family: MiSans;
                            font-weight: 330;
                            font-size: vw(14);
                            line-height: 100%;
                            color: #8fa1c1;
                            margin-right: vw(10);
                            span {
                                color: white;
                            }
                        }
                    }
                }
            }
            ::v-deep .el-dialog__footer {
                text-align: center;
                width: 100%;
                margin-top: vh(20);
            }
            .page-footer {
                width: vw(528);
                display: flex;
                justify-content: flex-end;
                ::v-deep .el-button {
                    width: vw(107);
                    height: vh(32);
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: vw(14);
                    line-height: vh(32);
                    padding: 0;
                    color: #e4eeff;
                    background: linear-gradient(181.96deg,
                            #6498ff -38.96%,
                            #1d4ba7 39.59%,
                            #142d60 98.35%) padding-box,
                        linear-gradient(0deg, #def1fe 0%, rgba(222, 241, 254, 0) 100%) border-box;
                    border: 1.5px solid transparent;
                    box-shadow: 0px 0px 20px 0px #8fb5ffa3 inset;
                    // span {
                    //     display: inline-block;
                    //     width: vw(45);
                    //     height: vh(20);
                    // }
                }
            }
        }
    }
}
.area-select {
    ::v-deep .el-collapse {
        border: none;
        .el-collapse-item__header {
            height: vh(50);
            line-height: vh(50);
            background-color: transparent !important;
            border: none;
            .el-collapse-item__arrow {
                color: white;
            }
        }
        .el-collapse-item__wrap {
            z-index: 999;
            background-color: black;
        }
    }
}
.type-box {
    width: vw(215);
    height: vh(86);
    position: absolute;
    top: vh(74);
    left: vw(1052);
    border-radius: vw(10);
    padding: vw(2);
    background-color: linear-gradient(90deg, rgba(178, 209, 255, 0.5) 0%, rgba(123, 176, 255, 0.25) 50%, rgba(178, 209, 255, 0.5) 100%);
    .content {
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, #254177 0%, #1F4A9C 100%),
            radial-gradient(62.59% 72.5% at 3.63% 87.5%, rgba(93, 147, 255, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),
            radial-gradient(50% 50% at 50% 50%, rgba(83, 140, 253, 0) 0%, rgba(83, 140, 253, 0.2) 100%);
        border-radius: vw(8);
        display: flex;
        flex-direction: column;
        padding-top: vh(15);
        padding-left: vw(22);
        ::v-deep .el-radio {
            width: vw(74);
            height: vh(22);
            color: white;
            margin-right: vh(10);
            .el-radio__input {
                .el-radio__inner {
                    width: vw(16);
                    height: vh(16);
                    border-radius: 0;
                }
            }
            .el-radio__label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(16);
                line-height: vh(22);
            }
        }
    }
}
.echarts-legend {
    position: fixed;
    left: vw(77);
    bottom: vh(80);
    .legend-title {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: vw(16);
        color: #ffffff;
    }
    .legend-icon {
        color: #ffffff;
        display: flex;
        align-items: center;
        margin-top: 10px;
        >div {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: vw(14);
            &:first-child {
                margin-right: vw(12);
                width: vw(13);
                height: vw(13);
                border: vw(2) solid;
            }
        }
    }
}
</style>
<style lang="scss">
.custom-select {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;
    .popper__arrow {
        border-bottom-color: #677aa5 !important;
        &::after {
            border-bottom-color: #141e32 !important;
        }
    }
    .el-select-dropdown__item {
        color: #ccd3d9;
        font-size: vw(14) !important;
        height: vh(34);
        line-height: vh(34);
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background-color: #147bd119 !important;
        color: #4bb6ff;
    }
}
</style>