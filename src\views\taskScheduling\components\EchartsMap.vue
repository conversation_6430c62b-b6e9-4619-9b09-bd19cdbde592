<template>
  <div class="echartsMap">
    <!-- 百度地图使用层级大于等于5 -->
    <div v-show="currentArea.areaLevel < 5" id="echart-map-wrap" /> 
    <div v-show="currentArea.areaLevel >= 5" id="baidu-map-wrap" class="map-wrap" ></div>
  </div>
</template>
<script>
import { fitChartSize } from '@/utils/dataUtil.js'
import * as echarts from 'echarts'
import mapJson from '@/views/market/mapConfig.json'
import common from '@/api/common'
import market from '@/api/market'
export default {
  name: 'EchartsMap',
  props: {
    strategyBatchList: {
      type: Array,
      default: () => []
    },
    selectForm: {
      type: Object,
      default: () => {}
    },
    parentLevel: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      mapChart: null,
      map: null,
      mapJson: {}, // 地图边界值
      mapBorderJson: {}, // 地图白色边框以及地图底层边界值
      mapData: {}, // 地图悬浮数据
      geoCoordMap: {}, // 悬浮数据中心点

      currentArea: {},
      customerNumberData: [],
      areaId: '999',
      areaLevel: 2, 
      countMap: [],
      customerTotalCount: 0,
      //baiduMap
      center: {},
      zoom: 15,
      labelName: [],
      labelOverlays: [],
      polygonPaths: [],
      polygonOverlays: [],
      extend: [],
      PolygonCenter: [],
      regionsLabel: {}, //色阶划分
      regionsNumber: {},
      villageData: [],
      villageMap: [],
      mapAreaData: [],
      lastMousePoint: {}
    }
  },
  watch: {
    customerTotalCount: {
      handler(val) {
       this.$emit('getTotalCount', this.customerTotalCount)
      },
      deep: true,
    }
  },
  async mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.mapChart) {
      this.mapChart.dispose()
    }
  },
  methods: {
    //获取地图数据信息
    async calculateCustomerGroup(t, checkList) {  //获取昆明下属区县的数据level=2
      if (t===1 || t === 2) {  //1 表示更换指标
        this.strategyBatchList = checkList
      }
      await market.calculateCustomerGroup({
        cityCode: this.selectForm.cityCode || '',
        countyCode: this.selectForm.countyCode || '',
        areaCode: this.selectForm.areaCode || '',
        villageCode: this.selectForm.villageCode || '',
        level: this.areaLevel-1 || 0, //与全局地图层级-1使用
        pageNum: 1,
        pageSize: 10,
        strategyBatchList: this.strategyBatchList,
        calculateType: 0
      }).then(res => {
        if (res.code === 200) {
          this.customerNumberData = res.data.regionStatistics
          this.countMap = this.customerNumberData.reduce((map, item) => {
            map[item.regionCode] = item.count;
            return map;
          }, {});
          this.customerTotalCount = res.data.totalCount
          //色阶划分处理
          if (this.customerNumberData.length > 0) {
            const maxItem = this.customerNumberData.reduce((prev, current) => {
              return (prev.count > current.count) ? prev : current;
            });
            const minItem = this.customerNumberData.reduce((prev, current) => {
              return (prev.count < current.count) ? prev : current;
            });
            const totalDiff = maxItem.count - minItem.count;
            const interval = totalDiff / 4;
            const region1End = Math.round((minItem.count + interval) / 10) * 10;
            const region2End = Math.round((minItem.count + interval * 2) / 10) * 10;
            const region3End = Math.round((minItem.count + interval * 3) / 10) * 10;
            this.regionsLabel = {
              label1: `< ${region1End}` ,
              label2: `${region1End} - ${region2End}` ,
              label3: `${region2End} - ${region3End}` ,
              label4: `≥ ${region3End}` 
            };
            this.regionsNumber = {
              number1: region1End,
              number2: region2End,
              number3: region3End,
            }
            console.log(this.regionsLabel, this.regionsNumber)
            this.$emit('updadeLegendList', this.regionsLabel)
          }
          if (t===1) {
            this.mapData = this.mapData.map(item => {
              return {
                ...item, 
                value: this.countMap[item.areaId] ?? 0 
              };
            });
            this.$nextTick(() => {
              this.initMapEcharts(this.mapData)
            })
          } else if (t===2) {
            console.log(987)
          }
          
        } else (
          this.$message.error(res.msg)
        )
      })
    },
    //获取区域边界 获取云南省0，1
    async getShengArea() {
      await common.getDimAreaByPid({
        areaPid: 0,
        areaLevel: 1
      }).then(async (res) => {
        if (res.code === 200) {
          this.mapBorderJson = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {
                  name: res.data[0].area_name
                },
                geometry: {
                  type: 'MultiPolygon',
                  coordinates: this.polygonPath(res.data[0].area_location)
                }
              }
            ]
          }
        }
      })
    },
    //获取地图边界
    async getDimAreaByPid(params) {
      this.currentArea = params
      this.areaLevel = params.areaLevel
      if (params.type == null) {
        await this.calculateCustomerGroup()
      }
      await common.getDimAreaByPid({
        areaPid: params.areaId,
        areaLevel: params.areaLevel
      }).then(async ({ code, data }) => {
        if (code === 200) {
          this.mapJson = {
            type: 'FeatureCollection',
            features: this.polygonFullPath(data)
          }
          console.log(this.mapJson)
          this.geoCoordMap = this.getGeoCoordMap(data)
          this.mapData = data.map(item => {
            return {
              name: item.area_name,
              areaId: item.area_id,
              areaLevel: params.areaLevel+1,
              centralPoint: item.central_point,
              value: this.countMap[item.area_id] ?? 0
            }
          })
          if (params.type == null) {
            this.$nextTick(() => {
              this.initMapEcharts(this.mapData)
            })
          }
        }
      })
    },
    //获取百度地图街区边界 及初始化
    async getStreetData(type,code) {
      let data = []
      if (type == 6) {
        this.zoom = 16
        data = this.mapAreaData.find(item => item.areaId === code)
      } else {
        this.zoom = 14
        data = this.mapData.find(item => item.areaId === code)
      }
      this.currentArea = data
      console.log(this.currentArea)
      if (this.map) {
        this.map.clearOverlays()
      } else {
        this.$nextTick(() => {
          this.initBaiduMap(this.currentArea)
        })
      }
      await common.getDimCommunityByPid({
        areaLevel: type,
        areaPid: this.currentArea.areaId,
      }).then((res) => {
        this.polygonPaths = []
        this.extend = []
        this.PolygonCenter = []
        this.labelName = []
        const [lng, lat] = this.currentArea.centralPoint.split(',')
        this.center = { lng, lat }
        if (res.code === 200) {
          if (res.data) {
            res.data.forEach(item => {
              if (item && item.area_location) {
                const locations = item.area_location.split('|')
                locations.forEach(locationStr => {
                  const tr = locationStr.split(';').map(val => {
                    const [lng, lat] = val.split(',')
                    return {
                      lng: (lng || '').trim(),
                      lat: (lat || '').trim()
                    }
                  })
                  this.polygonPaths.push({ tr, item })
                })
                this.labelName.push(item)
                if (item.central_point) {
                  const [lng, lat] = item.central_point.split(',')
                  this.PolygonCenter.push({ lng, lat })
                }
              }
            })
            console.log(this.polygonPaths)
            if (type == 5) {
              this.mapAreaData = res.data.map(item => {
                return {
                  name: item.area_name,
                  areaId: item.area_id,
                  areaLevel: 6,
                  centralPoint: item.central_point,
                  value: this.countMap[item.area_id] ?? 0
                }
              })
            }
            this.drawLabels()
            this.drawPolygons()
            const center = new BMap.Point(this.center.lng, this.center.lat)
            this.map.centerAndZoom(center, this.zoom)
          } else {
            this.$message.error(msg)
          }
        }
      })
    },
    //获取行政村展示数据
    async getVillageData(type,list) {
      await market.calculateCustomerGroup({
        cityCode: this.selectForm.cityCode || '',
        countyCode: this.selectForm.countyCode || '',
        areaCode: this.selectForm.areaCode || '',
        villageCode: this.selectForm.villageCode || '',
        level: type, //与全局地图层级-1使用
        pageNum: 1,
        pageSize: 10,
        strategyBatchList: list,
        calculateType: 0
      }).then(res => {
        if (res.code === 200) {
          this.villageData = res.data.regionStatistics
          if (this.villageData.length > 0) {
            const maxItem = this.villageData.reduce((prev, current) => {
              return (prev.count > current.count) ? prev : current;
            });
            const minItem = this.villageData.reduce((prev, current) => {
              return (prev.count < current.count) ? prev : current;
            });
            const totalDiff = maxItem.count - minItem.count;
            const interval = totalDiff / 4;
            const region1End = Math.round((minItem.count + interval) / 10) * 10;
            const region2End = Math.round((minItem.count + interval * 2) / 10) * 10;
            const region3End = Math.round((minItem.count + interval * 3) / 10) * 10;
            this.regionsLabel = {
              label1: `< ${region1End}` ,
              label2: `${region1End} - ${region2End}` ,
              label3: `${region2End} - ${region3End}` ,
              label4: `≥ ${region3End}` 
            };
            this.regionsNumber = {
              number1: region1End,
              number2: region2End,
              number3: region3End,
            }
            this.$emit('updadeLegendList', this.regionsLabel)
          }
          this.villageMap = this.villageData.reduce((map, item) => {
            map[item.regionCode] = item.count; // 存储 id 与 count 的对应关系
            return map;
          }, {});
          this.clearPolygons()
          this.drawLabels()
          this.drawPolygons()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /**
     * @description 格式化白色边框边界值
     * @param
     * <AUTHOR> @date 
     */
    polygonPath(data) {
      const area = data.split('|')
      const areaMap = area.map(item => {
        const points = data.split(';')
        const polygon = points.map(item => {
          const [lon, lat] = item.split(',')
          return [parseFloat(lon.trim()), parseFloat(lat.trim())]
        })
        return polygon
      })
      return [areaMap]
    },
    /**
     * @description 格式化边界值
     * @param
     * <AUTHOR> @date 
     */
    polygonFullPath(data) {
      return data.map(item => {
        return {
          type: 'Feature',
          properties: {
            name: item.area_name
          },
          geometry: {
            type: 'MultiPolygon',
            coordinates: this.polygonPath(item.area_location)
          }
        }
      })
    },
    /**
     * @description 获取悬浮中心点
     * @param
     * <AUTHOR> @date 
     */
    getGeoCoordMap(arr) {
      const result = {}
      arr.forEach(item => {
        const [longitude, latitude] = item.central_point.split(',').map(parseFloat)
        result[`${item.area_name}`] = [longitude, latitude]
      })
      return result
    },
    scatterData2() {
      return datas.map((item) => ({
        name: item.name,
        value: this.geoCoordMap[item.name]
      }))
    },
    initMapEcharts(datas) {
      if (this.mapChart) {
        this.mapChart.dispose()
      }
      this.mapChart = echarts.init(document.getElementById('echart-map-wrap'))
      echarts.registerMap('map', this.mapJson)
      echarts.registerMap('mapBorder', this.mapBorderJson)
      this.mapBorderJson = {}
      this.mapChart.off('click')
      const option = this.getOption(datas)
      this.mapChart.setOption(option)
      this.mapChart.on('click', (params) => {
        console.log(params.data)
        // if (params.data.value <= 0) return
        if (params.data.areaLevel < 5) { //行政村边界为6，继续加载边界无数据
          this.$emit('updataCode', params.data) //6是行政村，5是网格，4是区县，3是地市
          // this.getDimAreaByPid(params.data)
        } else {
          this.currentArea = params.data
          this.$emit('updataCode', params.data)
          // this.$nextTick(() => {
          //   this.initBaiduMap(params.data)
          // })
        }
      })
    },
    initBaiduMap(data) {
      const [lng, lat] = data.centralPoint.split(',')
      if (!this.map) {
        this.map = new BMap.Map('baidu-map-wrap', { enableMapClick: false })
        const center = new BMap.Point(lng, lat)
        this.map.setMapStyle({ styleJson: mapJson })
        this.map.centerAndZoom(center, this.zoom)
        this.map.enableScrollWheelZoom(true)
        this.map.addEventListener("mousemove", function (e) {
          // console.log(e)
          this.lastMousePoint = e.point;
        });

        // 监听鼠标滚轮事件
        this.map.addEventListener("mousewheel", function (e) {
          console.log(1111)
          if (this.lastMousePoint) {
            // 将像素坐标转换为地理坐标
            const newPoint = map.pixelToPoint(lastMousePoint);
            // 设置地图中心点为鼠标指针所在位置
            map.panTo(newPoint);
          }
        });
      }
    },
    drawLabels() {
      this.labelName = this.labelName.map(label => {
        // 查找对应 id 的 count，不存在则默认为 0
        const count = this.villageMap[label.area_id] ?? 0;
        // 返回新对象，避免直接修改原对象（如果需要）
        return {
          ...label,
          count: count // 添加 count 属性
        };
      });
      this.PolygonCenter.forEach((position, index) => {
        const labelContent = `
          <div style="text-align: center; line-height: 1.2;">
            <div>${this.labelName[index].area_name}</div>
            <div>${this.labelName[index].count}人</div>
          </div>
        `;
        const label = new BMap.Label(labelContent, { position })
        label.setStyle({
          fontSize: '13px',
          fontWeight: '400',
          fontFamily: 'PingFang SC',
          border: 'none',
          background: 'none',
          color: '#ffffff',
          pointerEvents: 'none'
        })
        this.map.addOverlay(label)
        this.labelOverlays.push(label); // 添加到数组管理
      })
    },
    /**
     * @description 绘制多边形
     * @param
     * <AUTHOR> @date 
     */
    drawPolygons() {
      // this.clearPolygons()
      const colorLevels = [
        '#7f2933',   //  - 最低值范围
        '#824751',  //  - 较低值范围
        '#1d7259', //  - 较高值范围
        '#56867d'   //  - 最高值范围
      ];
      let color = '';
      this.polygonPaths = this.polygonPaths.map(label => {
        // 查找对应 id 的 count，不存在则默认为 0
        const count = this.villageMap[label.item.area_id] ?? 0;
        if (count<this.regionsNumber.number1) {
          color = colorLevels[0]
        } else if (count<this.regionsNumber.number2) {
          color = colorLevels[1]
        } else if (count<this.regionsNumber.number3) {
          color = colorLevels[2]
        } else {
          color = colorLevels[3]
        }
        // 返回新对象，避免直接修改原对象（如果需要）
        return {
          ...label,
          fillColor: color,
          count: count // 添加 count 属性
        };
      });
      console.log(this.polygonPaths,99)
      this.polygonPaths.forEach(({ item, tr, fillColor }) => {
        const points = tr.map(coord => new BMap.Point(coord.lng, coord.lat))
        const polygon = new BMap.Polygon(points, {
          fillColor: fillColor,
          fillOpacity: 0.5,
          strokeOpacity: 1,
          strokeWeight: 1,
          strokeColor: 'rgba(169, 255, 218)',
          strokeStyle: 'none'
        })
        polygon.addEventListener('click', () => {
          console.log(item)
          this.drawer = true
           this.$emit('updataCode', item)
        })
        this.map.addOverlay(polygon)
        this.polygonOverlays.push(polygon)
      })
    },
    clearPolygons() {
      this.polygonOverlays.forEach(polygon => {
        this.map.removeOverlay(polygon) // 从地图移除
      })
      this.polygonOverlays = [] // 清空数组
      this.labelOverlays.forEach(label => {
          this.map.removeOverlay(label);
      });
      // 清空数组
      this.labelOverlays = [];
    },
    getOption(datas) {
      var that = this
      function scatterData2() {
        return datas.map((item) => ({
          name: item.name,
          areaId: item.areaId,
          value: that.geoCoordMap[item.name]
        }))
      }
      return {
        geo: [
          // 第一层地图
          {
            map: 'map',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              borderColor: '#D8E6FF4D',
              borderWidth: 1
            },
            emphasis: {
              itemStyle: {
                areaColor: 'transparent'
              },
              label: {
                show: 0,
                color: '#fff'
              }
            },
            zlevel: 3,
            roam: true, // 核心：开启缩放和平移（拖动）
          },
          // 第二层地图和第一层重叠用作边框
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '50%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#F7F8FF',
              borderWidth: 2,
              shadowColor: 'rgba(0, 0, 0, 0.25)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: fitChartSize(3.72)
            },
            zlevel: 2,
            silent: true,
            //  roam: true, // 核心：开启缩放和平移（拖动）
          },
          // 第三层地图底层
          {
            map: 'mapBorder',
            aspectScale: 1,
            layoutSize: '96%',
            layoutCenter: ['50%', '52%'],
            itemStyle: {
              areaColor: 'transparent',
              borderColor: '#B6C1FB99',
              borderWidth: 1
            },
            zlevel: 1,
            silent: true,
            //  roam: true, // 核心：开启缩放和平移（拖动）
          }
        ],
        visualMap: {
          show: false,
          type: 'piecewise',
          bottom: fitChartSize(40),
          right: fitChartSize(40),
          itemGap: fitChartSize(10),
          align: 'left',
          itemWidth: fitChartSize(16),
          itemHeight: fitChartSize(12),
          textStyle: {
            fontSize: fitChartSize(14),
            color: '#EDEDED'
          },
          seriesIndex: 0,
          pieces: [
            {
              lt: this.regionsNumber.number1,
              color: '#FF393980',
              label: this.regionsLabel.label1
            },
            {
              gte: this.regionsNumber.number1,
              lt: this.regionsNumber.number2,
              color: '#FF838380',
              label: this.regionsLabel.label2
            },
            {
              gte: this.regionsNumber.number2,
              lt: this.regionsNumber.number3,
              color: '#37D89280',
              label: this.regionsLabel.label3
            },
            {
              gte: this.regionsNumber.number3,
              color: '#A9FFDA80',
              label: this.regionsLabel.label4
            }
          ]
        },
        series: [
          {
            type: 'map',
            selectedMode: false,
            map: 'map',
            geoIndex: 0,
            data: datas,
          },
          // 地市弹框撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: '{b}',
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#fff',
              fontSize: fitChartSize(20),
              fontWeight: '500',
              fontFamily: '',
              show: true
            },
            symbol: 'rect',
            // symbolSize: function(data, params) {
            //   const name = params.data.name
            //   const nameLength = name.length || 0
            //   const width = fitChartSize(nameLength * 20)
            //   const height = fitChartSize(30)
            //   return [width, height]
            // },
            symbolOffset: [0, fitChartSize(-30)],
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              borderWidth: fitChartSize(0.5),
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
          // 地市对应值撒点
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            zlevel: 4,
            label: {
              formatter: function (name) {
                const item = datas.find(item => item.name === name.name)
                return item ? item.value : ''
              },
              position: 'inside',
              align: 'center',
              verticalAlign: 'middle',
              color: '#ffffff',
              fontSize: fitChartSize(23),
              fontFamily: 'Milibus',
              show: true
            },
            symbol: 'rect',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent',
              opacity: 1
            },
            silent: true,
            data: scatterData2()
          },
        ]
      }
    },
    handleResize() {
      if (this.mapChart) {
        this.mapChart.resize()
        this.$nextTick(() => {
          const updatedOption = this.getOption(this.mapData)
          this.mapChart.setOption(updatedOption)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";
.echartsMap {
  .initBaiduMap {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    font-weight: bold;
  }
  #echart-map-wrap {
    margin-top: vh(52);
    margin-left: vw(315);
    width: vw(865);
    height: vh(813);
    background-color: transparent !important;
  }
  #baidu-map-wrap {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: transparent !important;
  }
}
</style>
