<template>
    <div class="dialog" @click.stop>
        <div class="title"></div>
        <div class="box-common">
            <div class="content-common">
                <el-form ref="form" :rules="rules" :model="form" label-width="140px" size="mini">
                    <el-row :gutter="10">
                        <!-- 任务名称 -->
                        <el-col :span="24">
                            <el-form-item label="任务名称" prop="operationName">
                                <el-input v-model="form.operationName" placeholder="预约服务单"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <!-- 任务描述 -->
                        <el-col :span="24">
                            <el-form-item label="任务描述">
                                <el-input type="textarea" v-model="form.taskDesc" placeholder="请输入" rows="2"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="开始时间" prop="startTime">
                                <el-date-picker v-model="form.startTime" type="datetime" placeholder="开始时间"
                                    value-format="yyyy-MM-dd HH:mm:ss" default-time="09:00:00" popper-class="dataTime-select-dropdown">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="结束时间" prop="endTime">
                                <el-date-picker v-model="form.endTime" type="datetime" placeholder="结束时间"
                                    value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59" popper-class="dataTime-select-dropdown">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="渠道选择" required>
                                <el-radio-group v-model="form.radio">
                                    <el-radio label="0">中台渠道</el-radio>
                                    <el-radio label="1">看管渠道</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="策略名称：">
                                <el-tooltip :content="strategyBatchList[0].strategyName" placement="bottom">
                                    <el-input autosize v-model="strategyBatchList[0].strategyName" disabled></el-input>
                                </el-tooltip>

                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="策略ID：">
                                <el-input autosize v-model="strategyBatchList[0].realId" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="客群数量(人)：">
                                <el-input autosize v-model="strategyBatchList[0].customerGroupSize" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="24">
                            <el-form-item label="客户所属区域：">
                                <el-input autosize v-model="customerAreaName" disabled></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div class="btn-box">
                    <div class="transfer common-btn" @click="cancel">
                        <div>上一步</div>
                    </div>
                    <div class="transfer common-btn" @click="transfer">
                        <div>去派发</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import market from '@/api/market'
export default {
    name: 'TaskInfo',
    props: {
        strategyBatchList: {
            type: Array,
            default: () => []
        },
        taskAreaList: {
            type: Object,
            default: () => {}
        },
        areaLevel: {
            yupe: Number,
            default: null
        },
        customerAreaName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            form: {
                operationName: '',
                startTime:  new Date(new Date().setDate(new Date().getDate() + 1)),
                endTime: null,
                taskDesc: '',
                radio: '0'
            },
            rules: {
                operationName: [
                    {required: true, message: '请输入任务名称'},
                ],
                startTime: [
                    {required: true, message: '请选择开始时间'},
                ],
                endTime: [
                    {required: true, message: '请选择结束时间'},
                ],
            }
        }
    },
    created() {
        this.form.startTime.setHours(9, 0, 0, 0); // 重置时间为00:00:00
        this.dataTime(this.form.startTime)
    },
    methods: {
        dataTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，补0
            const day = String(date.getDate()).padStart(2, '0');       // 日期补0
            const hours = String(date.getHours()).padStart(2, '0');   // 小时补0
            const minutes = String(date.getMinutes()).padStart(2, '0'); // 分钟补0
            const seconds = String(date.getSeconds()).padStart(2, '0'); // 秒补0
            // 拼接成目标格式
            this.form.startTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        changeTime(val) {
        },
        cancel() {
            this.$emit('cancel', false)
        },
        //派发按钮
        transfer() {
             this.$refs.form.validate((valid) => {
                if (valid) {
                    //派发接口
                    market.createTask({
                        taskName: this.form.operationName,
                        taskDesc: this.form.taskDesc,
                        startTime: this.form.startTime,
                        endTime: this.form.endTime,
                        calculateType: 0,
                        taskLevel: this.areaLevel, //  2:地市3: 区县 4网格
                        taskType: 0,
                        chnlType: this.form.radio,
                        customerTotalCount: this.strategyBatchList[0].customerGroupSize,
                        taskStrategyList: this.strategyBatchList,
                        taskAreaList: [{
                            areaCode: this.taskAreaList.areaId,
                            areaName: this.taskAreaList.areaName,
                        }]
                    }).then(res => {
                        if (res.code === 200) {
                            this.$emit('transfer', true)
                        } else {
                            this.$emit('transfer', false)
                            this.$message.error(res.msg)
                        }
                    })

                } else {
                    console.log('error submit!!');
                    return false;
                }
             });
        },
    },
}
</script>
<style lang="scss" scoped>
@import "~@/views/operation/index.scss";
.dialog {
    .title {
        margin-top: vh(14);
        margin-left: vw(40);
        width: vw(576);
        height: vh(92);
        background: url("../../../assets/images/market/taskInfo-bg.png") no-repeat;
        background-size: 100% 133%;
    }
    .box-common {
        width: vw(1840);
        height: vh(813);
        padding: vw(2);
        margin: 0 auto;
        background: #13274aa1;
        border: 2px solid #396a9a;
        border-radius: vw(10);
        .content-common {
            width: vw(550);
            height: vh(348);
            margin: 0 auto;
            border-radius: vw(8);
            /* 比外层小2px，避免露出底色 */
            padding-top: vh(28);
            ::v-deep .el-form-item__label {
                font-size: vw(18);
                height: vh(32);
                line-height: vh(32);
                color: rgba(228, 238, 255, 1);
            }
            ::v-deep .el-form-item {
                margin-bottom: vh(26);
            }
            ::v-deep .el-input__inner,
            ::v-deep .el-textarea__inner {
                background-color: #1d334b;
                padding: 0 vw(8);
                border-color: rgba(68, 83, 105, 1);
                color: white;
                font-size: vw(18);
            }
            ::v-deep .el-textarea__inner {
                min-height: vh(80) !important;
            }
            ::v-deep .el-form-item__content {
                line-height: vh(32);
            }
            ::v-deep .el-input__inner {
                height: vh(32);
                line-height: vh(32);
                font-size: vw(18);
            }
            ::v-deep .el-date-editor {
                width: 100%;
                // background-color: #6498FF;
                .el-input__prefix {
                    // width: 100%;
                    left: vw(400);
                    display: none;
                }
            }
            ::v-deep .el-radio-group {
                .el-radio {
                    color: white;
                }
                .el-radio__label {
                    font-size: vw(18);
                }
            }
        }
    }
    .btn-box {
        width: vw(281);
        height: vh(40);
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 auto;
        .common-btn {
            border-radius: vw(2);
            color: rgba(228, 238, 255, 1);
            box-shadow: 0px 0px 20px 0px rgba(143, 181, 255, 0.64) inset;
            background: linear-gradient(0deg, #DEF1FE 0%, rgba(222, 241, 254, 0) 100%);
            padding: vw(2);
            div {
                width: 100%;
                height: vh(36);
                cursor: pointer;
                text-align: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: vw(18);
                line-height: vh(38);
                background: linear-gradient(181.96deg, #6498FF -38.96%, #1D4BA7 39.59%, #142D60 98.35%);
            }
        }
        .cancel {
            width: vw(80);
            height: vh(40);
        }
        .transfer {
            width: vw(122);
            height: vh(40);
        }
    }
}
</style>
<style lang="scss">
.dataTime-select-dropdown {
    background-color: #141e32 !important;
    border-color: #677aa5 !important;
    color: white;
    //   color: white !important;
    th {
        color: white !important;
    }
    .prev-month,
    .next-month {
        color: #606266 !important;
    }
    .el-input__inner {
        background-color: transparent !important;
        color: white;
    }
    .in-range div {
        background-color: #677aa5 !important;
    }
    .el-picker-panel__footer {
        background-color: #147bd119 !important;
        .el-button {
            background-color: transparent;
            border: 0;
            color: white;
        }
    }
    .el-date-picker__header-label {
        color: white;
    }
    .el-picker-panel__icon-btn {
        color: white;
    }
    .el-time-panel {
        background-color: #141e32 !important;
    }
    .el-scrollbar__view {
        .el-time-spinner__item {
            color: white;
        }
        .active {
            color: #3194ff;
        }
        .el-time-spinner__item:hover {
            color: #3194ff;
        }
    }
    .el-time-panel__footer {
        .cancel {
            color: white;
        }
    }
}
</style>
