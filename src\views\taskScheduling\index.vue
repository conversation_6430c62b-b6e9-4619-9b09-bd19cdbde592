<template>
  <div class="task-scheduling">
    <!-- <div class="title" v-show="!taskShow"></div> -->
    <CustomerHeatMap @createTask="createTask" v-show="!taskShow"></CustomerHeatMap>
    <TaskeInfo v-if="taskShow && loading" 
      @cancel="cancel" 
      @transfer="transfer" 
      :taskAreaList="taskAreaList" 
      :customerTotalCount="customerTotalCount"
     :strategyBatchList="strategyBatchList"
     :areaLevel="areaLevel"
     :customerAreaName="customerAreaName"></TaskeInfo>
    <el-dialog :top="'35vh'" :visible.sync="dialogVisible" :modal-append-to-body="false" center>
      <div class="img">
        <img src="../../assets/images/taskSend.png" alt="">
        <div class="text">派发成功</div>
      </div>
      <div class="btn" @click="dialogVisible = false">
        <div class="cont">
          <div>确定</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CustomerHeatMap from './components/CustomerHeatMap.vue';
import TaskeInfo from './components/taskInfo.vue';
export default {
  name: 'TaskScheduling',
  components: {
    CustomerHeatMap,
    TaskeInfo
  },
  data() {
    return {
      taskShow: false,
      loading: false,
      dialogVisible: false,
      strategyBatchList: [],
      customerTotalCount: 0,
      taskAreaList: [],
      areaLevel: '',
      customerAreaName: '',
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
    //下一步接口，取得strategyBatchList传给创建任务弹窗
    createTask(params) {
      this.strategyBatchList = params.checkList
      this.customerTotalCount = params.totalCount
      this.taskAreaList = params.taskAreaList
      this.areaLevel = params.areaLevel
      this.customerAreaName = params.customerAreaName
      this.taskShow = true
      this.loading = true
    },
    cancel(data) {
      this.taskShow = false
      this.loading = false
    },
    //创建任务弹窗派发派发按钮
    transfer(data) {
      this.taskShow = false
      this.loading = false
      this.dialogVisible = data
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/views/market/index.scss";
.task-scheduling {
  width: 100%;
  height: 100%;
  .title {
    margin-top: vh(10);
    margin-left: vw(40);
    width: vw(958);
    height: vh(92);
    background: url("../../assets/images/market/title-bg.png") no-repeat;
    background-size: 100% 133%;
  }
  ::v-deep .el-dialog {
    width: vw(503);
    height: vh(295);
    background: linear-gradient(148.39deg, #113963 38.34%, #1D3052 98.51%);
    position: relative;
    border-radius: vw(8);
    // transform: translate(-50%, -50%);
    .dialog-footer {
      position: absolute;
      bottom: 0;
    }
    .el-dialog__body {
      padding: 0 !important;
    }
  }
  .img {
    width: 100%;
    height: vh(148);
    position: absolute;
    bottom: vh(92.5);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    img {
      width: vw(106);
      height: vh(106);
      margin: 0 auto;
    }
    .text {
      width: vw(72);
      height: vh(28);
      font-family: PingFang SC;
      font-weight: 600;
      font-size: vw(18);
      line-height: vh(28);
      text-align: center;
      background: linear-gradient(180deg, #E4EEFF 25%, #9AC0FF 82.14%);
      background-clip: text;
      color: transparent;
    }
  }
  .btn {
    width: 100%;
    height: vh(72);
    position: absolute;
    bottom: 0;
    padding-top: vh(12);
    .cont {
      width: vw(80);
      border-radius: vw(2);
      color: rgba(228, 238, 255, 1);
      box-shadow: 0px 0px 20px 0px rgba(143, 181, 255, 0.64) inset;
      background: linear-gradient(0deg, #DEF1FE 0%, rgba(222, 241, 254, 0) 100%);
      padding: vw(2);
      margin: 0 auto;
      div {
        width: 100%;
        height: vh(36);
        cursor: pointer;
        text-align: center;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: vw(16);
        line-height: vh(38);
        background: linear-gradient(181.96deg, #6498FF -38.96%, #1D4BA7 39.59%, #142D60 98.35%);
      }
    }
  }
}
</style>
